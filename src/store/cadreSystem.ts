import { defineStore } from 'pinia'

export const useCadreSystem = defineStore('cadreSystem', {
	state: () => {
		return {
			// 散点图点击后的user_id
			coor_user_id: '-1',
			// 坐标分布图选中的菜单
			coor_menu_selected: 2,
			// 坐标分布菜单
			menu: [
				{
					label: '同序列',
					key: 1,
				},
				{
					label: '乡镇/部门',
					key: 2,
				},
				{
					label: '全县',
					key: 0,
				},
			],
		}
	},
	getters: {
		getMenu(): Array<{ label: string; key: number }> {
			return this.menu
		},
		// 人大、政协、纪委、公安、检察院、法院人员 去掉同序列
		menuFilterSame(): Array<{ label: string; key: number }> {
			return this.menu.filter((item) => item.key !== 2)
		},
		//特殊十二人
		menuFilterSpecial(): Array<{ label: string; key: number }> {
			return this.menu.filter((item) => ![1, 2].includes(item.key))
		},
	},
	actions: {
		updateCoorUserId(user_id: string) {
			this.coor_user_id = user_id
		},
		updateCoorMenuSelected(key: number) {
			this.coor_menu_selected = key
		},
	},
})
