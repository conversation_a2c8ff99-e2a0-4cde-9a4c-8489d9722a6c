import { defineStore } from 'pinia'
import { getOrgList } from '@/apis/cadreSystem'
import { message } from 'ant-design-vue'

const org = defineStore('org', {
	state: () => {
		const orgList = sessionStorage.getItem('org_list')
		const flatData = sessionStorage.getItem('flat_data')

		return { orgTree: JSON.parse(orgList || '[]') as any, flatData: JSON.parse(flatData || '[]') }
	},
	actions: {
		updateDetail(orgTree: any) {
			sessionStorage.setItem('org_list', JSON.stringify(orgTree))
			this.orgTree = orgTree || {}
		},
		updateFlatData(orgTree: any) {
			const flatData = (data = []) => {
				const _data: any = data.flatMap((org: any) => {
					const { children, ...other } = org
					if (Array.isArray(children) && children.length) {
						return [org, ...flatData(children as any)]
					}
					return [org]
				})
				return _data
			}
			// 数据扁平化
			this.flatData = flatData(orgTree)
			sessionStorage.setItem('flat_data', JSON.stringify(this.flatData))
		},
		async loadOrgTree() {
			const res = await getOrgList()

			if (res.code === 0) {
				this.updateDetail(res.data)
			} else {
				message.error(res.message)
			}
		},
	},
})
export default org
