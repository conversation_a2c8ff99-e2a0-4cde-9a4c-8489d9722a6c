import axios, { InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { ResponesType } from '@/types/http'
// 创建axios实例
import router from '@/router/index'
import { ENV_OFFLINE, XUNCHA_VERSION, XUNCHA_PRO } from '@/config/env'
import { INDEX_PATH } from '@/router/index'

const service = axios.create({
	baseURL: XUNCHA_PRO ? window.location.origin + '/owsz' : import.meta.env.VITE_APP_BASE_API, // api的base_url
	timeout: 100000, // 请求超时时间
})
// pad 请求
const padService = axios.create({
	baseURL: XUNCHA_PRO ? window.location.origin + '/owsz' : import.meta.env.VITE_APP_PAD_API,
	timeout: 100000,
})

const createHeaders = (_userInfo?: any) => {
	const userInfo = _userInfo || JSON.parse(sessionStorage.getItem('userInfo') || '{}')

	const _personal_token = sessionStorage.getItem('personal_token') || ''

	const _header: any = {
		'Access-Control-Allow-Credentials': '*',
		'Content-Type': 'application/json',
		_menu_id: userInfo ? escape(userInfo._menu_id || '-1') : '-1',
		_oid: userInfo ? escape(userInfo._oid || '-1') : '-1',
		_tk: userInfo ? escape(userInfo._tk || '-1') : '-1',
		_uid: userInfo ? escape(userInfo._uid || '1') : '-1',
		_un: userInfo ? escape(userInfo._un || '-1') : '-1',
		_type: userInfo ? escape(userInfo._type || '2') : '2',
		_org_name: userInfo ? escape(userInfo._org_name || '-1') : '-1',
		_org_type: userInfo ? escape(userInfo._org_type || '-1') : '-1',
		// _org_type: 102801,
		// _uoid: userInfo ? escape(userInfo.sel_org_id || '-1') : '-1',
		// _region_id: userInfo ? escape(userInfo._region_id || '-1') : '-1',
		// _region_id: 35,
		_region_id: 91,
		_personal_token,
	}
	if (ENV_OFFLINE) {
		_header['_user_id'] = userInfo._uid
		_header['_token'] = userInfo._tk
	}
	return _header
}
const emptyHeader = () => {
	const _header: any = {
		'Access-Control-Allow-Credentials': '*',
		'Content-Type': 'application/json',
		_menu_id: '-1',
		_oid: '-1',
		_tk: '-1',
		_uid: '-1',
		_un: '-1',
		_type: '2',
		_org_name: '-1',
		_org_type: '-1',
		// _org_type: 102801,
		// _uoid: userInfo ? escape(userInfo.sel_org_id || '-1') : '-1',
		// _region_id: userInfo ? escape(userInfo._region_id || '-1') : '-1',
		// _region_id: 35,
		_region_id: 91,
	}
	return _header
}
// request拦截器
service.interceptors.request.use(
	(config: InternalAxiosRequestConfig) => {
		const headers = config.url === '/login/login' ? emptyHeader() : createHeaders()
		for (const key in headers) {
			console.log(config.headers[key])
			if (!config.headers[key]) {
				config.headers[key] = headers[key]
			}
		}
		return config
	},
	(error: any) => {
		Promise.reject(error)
	}
)

// respone拦截器
service.interceptors.response.use(
	(response: AxiosResponse) => {
		const res = response.data
		if (res.code !== 0) {
			if (res.code === 9905) {
				sessionStorage.clear()

				!XUNCHA_VERSION && router.replace(INDEX_PATH)
			}
			return res
		} else {
			return response.data
		}
	},
	(error: any) => {
		console.log('err' + error) // for debug
		return Promise.reject(error)
	}
)

// respone拦截器
padService.interceptors.response.use(
	(response: AxiosResponse) => {
		if (response.data.code === 9905) {
			sessionStorage.clear()

			router.replace(INDEX_PATH)
		}
		return response.data
	},
	(error: any) => {
		console.log('err' + error) // for debug
		return Promise.reject(error)
	}
)
padService.interceptors.request.use(
	(config: InternalAxiosRequestConfig) => {
		const userInfo = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
		const _personal_token = sessionStorage.getItem('personal_token') || ''
		const { _tk, _uid } = userInfo

		config.headers['_token'] = _tk
		config.headers['_user_id '] = _uid
		config.headers['_personal_token'] = _personal_token

		return config
	},
	(error: any) => {
		Promise.reject(error)
	}
)
type config = object extends InternalAxiosRequestConfig ? never : InternalAxiosRequestConfig

const createHttp = (prefix: string) => {
	return {
		get(url: string, params?: { [key in string]: unknown }, config?: config): Promise<ResponesType<any>> {
			return service.get(prefix + url, { params, ...config })
		},
		post(url: string, data?: { [key in string]: unknown }, config?: config): Promise<ResponesType<any>> {
			return service.post(prefix + url, data, config)
		},
	}
}
const URL_MAP = {
	user: ENV_OFFLINE ? import.meta.env.VITE_APP_PAD_API : '/user',
	pms: ENV_OFFLINE ? import.meta.env.VITE_APP_PAD_API : '/pms',
	login: '/login',
}
// 用户中心
const userRequest = createHttp(URL_MAP.user)
// 登录中心
const loginRequest = createHttp(URL_MAP.login)
// 干部系统
// const pmsRequest = createHttp('')
const pmsRequest = createHttp(URL_MAP.pms)
// host
const baseURL = XUNCHA_PRO ? window.location.origin + '/owsz' : import.meta.env.VITE_APP_BASE_API
export { service as axios, padService as padAxios, userRequest, pmsRequest, loginRequest, baseURL, createHeaders, emptyHeader }
