<template>
	<div className="new-login">
		<div className="inner-box" v-if="loginStatus === -1">
			<div className="header"></div>
			<div className="form-box">
				<a-form layout="horizontal" ref="formRef" :model="formState" :rules="rules">
					<a-form-item name="username">
						<a-input v-model:value="formState.username" placeholder="账号" v-focus>
							<template #prefix>
								<i class="icon-user"></i>
							</template>
						</a-input>
					</a-form-item>
					<a-form-item name="password">
						<a-input v-model:value="formState.password" type="password" placeholder="密码" v-focus>
							<template #prefix>
								<i class="icon-password"></i>
							</template>
						</a-input>
					</a-form-item>
					<a-form-item name="captcha" v-if="!ENV_OFFLINE">
						<a-input v-model:value="formState.captcha" placeholder="请输入右侧图片中的文字" v-focus>
							<template #prefix>
								<i class="icon-code"></i>
							</template>
						</a-input>
						<img :src="`${baseURL}/user/show-captcha.png?uuid=${uuid}`" alt="" className="verification-code" @click="refreshVerificationCode" />
					</a-form-item>
					<a-form-item>
						<a-button
							type="primary"
							@click="
								() => {
									ENV_OFFLINE ? onOfflinePassword() : onSubmit()
								}
							"
							:loading="submitloading"
						>
							登录
						</a-button>
					</a-form-item>
				</a-form>
			</div>
		</div>
		<div class="menu-box" v-else-if="loginStatus === 1">
			<div class="system-img">
				<img src="./image/title.png" v-if="FULL_VERSION" style="width: 100%; height: 100%" />
				<img src="./image/title1.png" v-else style="width: 100%; height: 100%" />
			</div>
			<div class="system-menu">
				<div
					class="menu-card"
					:class="{
						'limit-system': LIMIT_VERSION,
						[item.className]: true,
					}"
					@click="onRouter(item.path)"
					v-for="item in menu"
					:key="item.path"
				>
					<span>{{ item.name }}</span>
				</div>
				<!-- <div class="menu-card" @click="onSync">数据同步</div> -->
			</div>
		</div>
		<div class="renzheng" v-if="bottomLogo">
			<img :src="RenzhengPng" />
			数据已加密传输
		</div>
		<div class="data-sync">
			<div class="sync-button" @click="onSyncClick">
				<span
					class="sync-icon"
					:class="{
						'rotateZ-90deg': syncButtonVisible,
					}"
				></span>
				<span class="text">{{ loginStatus === 1 ? '同步数据' : '用户信息同步' }}</span>
			</div>
			<Transition name="drop">
				<div class="drop-box" v-if="syncButtonVisible">
					<div class="drop-inner-box">
						<div class="sync-button" @click="onSync(1)">
							<span class="sync-icon avatar"></span>
							<span class="text">头像</span>
						</div>
						<div class="sync-button" @click="onSync(0)">
							<span class="sync-icon data"></span>
							<span class="text">数据</span>
						</div>
					</div>
				</div>
			</Transition>
		</div>
	</div>
	<ResetPasswordModal
		:visible="passwordVisible"
		:headers="headers"
		:user_id="reset_user_id"
		:maskClosable="false"
		:closable="false"
		:onClose="onPasswordClose"
	/>
</template>

<script lang="ts" setup>
import { reactive, ref, inject, onMounted, h } from 'vue'
import { useRouter } from 'vue-router'
import md5 from 'js-md5'
import type { UnwrapRef } from 'vue'
import type { Rule } from 'ant-design-vue/es/form'
import { message, Modal, type FormInstance } from 'ant-design-vue'

import { baseURL } from '@/utils/axios'
import { login, chooseOrg, offlineLogin, syncSysUser, getSyncPercent } from '@/apis/login'
import { syncJob } from '@/apis/sand-table-exercise'
import { generateUUID, getUserInfoItem } from '@/utils/utils'
import useKeepAlive from '@/store/keepalive'
import { USER_LOGIN, FULL_VERSION, VERSION, LIMIT_VERSION, ENV_OFFLINE } from '@/config/env'
import RenzhengPng from './image/renzheng.png'
import useOrg from '@/store/org'
import { createHeaders } from '@/utils/axios'

interface FormState {
	username: string
	password: string
	captcha: string
}

const SandBoxPath = {
	[VERSION.FULL]: '/new-sand-table-exercise/home',
	[VERSION.LIMIT]: '/new-sand-table-exercise/adjust-list',
}

const menu: any = [
	{
		name: FULL_VERSION ? '干部选用' : '调整名单',
		path: SandBoxPath[FULL_VERSION ? VERSION.FULL : VERSION.LIMIT],
		className: 'select-cadre',
	},
]

FULL_VERSION &&
	menu.unshift({
		name: '干部画像',
		path: '/cadre-system/information',
	})

FULL_VERSION &&
	menu.push({
		name: '班子评估',
		path: '/cadre-system/statistics?org_id=12',
	})
const progress: any = inject('progress')

const router = useRouter()
const org = useOrg()

const formRef = ref<FormInstance>()

const syncButtonVisible = ref(false)

const bottomLogo = ref(true)

const uuid = ref(generateUUID())

const submitloading = ref(false)

const keepalive = useKeepAlive()
// 清空页面路由缓存
keepalive.clear()

const formState: UnwrapRef<FormState> = reactive({
	username: window?.localStorage?.getItem('username') || USER_LOGIN.username,
	password: USER_LOGIN.password,
	captcha: '',
})
// 登录状态 , 1 为登录 -1未登录
const loginStatus = ref(getUserInfoItem('_tk') ? 1 : -1)
// const loginStatus = ref(ENV_OFFLINE ? 1 : getUserInfoItem('_tk') ? 1 : -1)

// 到登录界面后，防止pad端使用原生返回

const vFocus = {
	mounted: (el: any) => {
		const input = el.querySelector('input')

		input.addEventListener('focus', () => {
			bottomLogo.value = false
		})
		input.addEventListener('blur', () => {
			bottomLogo.value = true
		})
	},
}
const ROUTE_WHITE_LIST = ['/login', '/data-screen/login']
router.beforeEach((_, from) => {
	if (ROUTE_WHITE_LIST.includes(from.path) && loginStatus.value !== 1) {
		return false
	}

	return true
})

const onRouter = (path: any) => {
	console.log('🚀 ~ onRouter ~ path:', path)
	// if (path === '/new-sand-table-exercise/home' && ENV_OFFLINE) {
	// 	return message.warn('离线版暂未开放该功能')
	// }
	router.replace(path)
}
const onSync = async (flag: number) => {
	syncButtonVisible.value = false

	if (progress.startLoad.value) {
		return message.warn('数据同步中')
	}
	message.loading('请求中...', 10)

	const res = await syncJob({ flag })

	message.destroy()

	if (res.code === 0) {
		// message.success(res.message)
		progress.start(flag)
	} else {
		message.error(res.message)

		progress.close()
	}
}

//
const validateUserName = async (_rule: Rule, value: string) => {
	if (value === '') {
		return Promise.reject('请输入用户名')
	} else {
		return Promise.resolve()
	}
}

const validatePassword = async (_rule: Rule, value: string) => {
	if (value === '') {
		return Promise.reject('请输入密码')
	} else {
		return Promise.resolve()
	}
}

const rules: Record<string, Rule[]> = reactive({
	password: [{ required: true, validator: validatePassword, trigger: 'change' }],
	username: [{ validator: validateUserName, trigger: 'change' }],
})

const refreshVerificationCode = () => {
	uuid.value = generateUUID()
}

const passwordVisible = ref(false)

const onPasswordClose = () => {
	passwordVisible.value = false

	submitloading.value = false
}

const headers = ref()
const reset_user_id = ref()
const resetPasswordModal = ({ pwd_check_message, header, user_id }: any) => {
	Modal.info({
		title: '提示',
		content: h('div', {}, [h('p', pwd_check_message)]),
		okText: '更改密码',
		onOk() {
			passwordVisible.value = true
			reset_user_id.value = user_id

			headers.value = header
		},
	})
}

const onSubmit = () => {
	if (submitloading.value) {
		return
	}

	formRef.value
		?.validate()
		.then(async (values) => {
			const { password, username, captcha } = values
			const params = {
				captcha,
				phone: username,
				uuid: uuid.value,
				password: md5(password),
				flag: 2,
			}

			submitloading.value = true

			const res = await login(params)
			if (res.code === 0) {
				const { type, more_org_form = {}, login_form, pwd_check_code, pwd_check_message } = res.data

				if (type === 1) {
					const { name, oid, org_type, type, user_id, token } = login_form

					const userInfo: any = {
						_uid: user_id,
						_un: encodeURIComponent(username),
						_tk: token,
						_oid: oid,
						_type: type,
						// _region_id: 35,
						_region_id: 91,
						_org_type: type,
						_org_name: encodeURIComponent(name),
						org_nme: name,
						_on: encodeURI(name || ''),
					}

					if (pwd_check_code !== 0 && pwd_check_code !== undefined) {
						return resetPasswordModal({
							pwd_check_code,
							pwd_check_message,
							user_id,
							header: createHeaders(userInfo),
						})
					}

					const keys = Object.keys(userInfo)

					keys.forEach((key) => {
						window.sessionStorage.setItem(key, userInfo[key])
					})

					sessionStorage.setItem('userInfo', JSON.stringify(userInfo))
					loginStatus.value = 1
					message.success('登录成功！')
				} else {
					const { org_list, token } = more_org_form

					let user = org_list?.[0] || login_form

					if (user) {
						const { name, oid, org_type, type, user_id } = user

						const userInfo = {
							_uid: user_id,
							_un: encodeURIComponent(username),
							_tk: token,
							_oid: oid,
							_type: type,
							// _region_id: 35,
							_region_id: 91,
							_org_type: type,
							_org_name: encodeURIComponent(name),
							org_nme: name,
						}
						// sessionStorage.setItem('userInfo', JSON.stringify(userInfo))

						const res = await chooseOrg({ user_id, name: encodeURIComponent(name), oid, type, token })

						if (res.code === 0) {
							const { token, user_name, menu, dep_id, dep_name, org_type, name } = res.data
							//登录返回token
							userInfo._tk = token
							//用户名
							userInfo._un = encodeURIComponent(user_name)
							userInfo._org_name = encodeURIComponent(name)
							userInfo._org_type = encodeURIComponent(org_type)

							if (pwd_check_code !== 0 && pwd_check_code !== undefined) {
								return resetPasswordModal({
									pwd_check_code,
									pwd_check_message,
									header: createHeaders(userInfo),
									user_id,
								})
							}

							//部门ID
							window.sessionStorage.setItem('_did', dep_id || '')
							//部门名称
							window.sessionStorage.setItem('_dn', escape(dep_name || ''))
							//单位名称
							window.sessionStorage.setItem('_on', encodeURI(name || ''))

							window.sessionStorage.setItem('_mu', JSON.stringify(menu))

							message.success('登录成功')

							loginStatus.value = 1

							sessionStorage.setItem('userInfo', JSON.stringify(userInfo))
						} else {
							message.error(res.message)
						}
					}
				}
			} else {
				message.error(res.message)

				refreshVerificationCode()
			}

			submitloading.value = false
		})
		.catch((err) => {
			console.log('error', err)
		})
}
const onOfflinePassword = () => {
	if (submitloading.value) {
		return
	}

	formRef.value?.validate().then(async (values) => {
		submitloading.value = true

		const { password, username } = values

		const params = {
			account: username,
			password: md5(password),
		}
		try {
			const res = await offlineLogin(params)

			if (res.code === 0) {
				const { token, sys_user_id, pwd_check_code, pwd_check_message } = res.data

				const userInfo = { _uid: sys_user_id, _tk: token, _un: encodeURI(username || '') }

				if (pwd_check_code !== 0 && pwd_check_code !== undefined) {
					return resetPasswordModal({
						pwd_check_code,
						pwd_check_message,
						header: createHeaders(userInfo),
						user_id: sys_user_id,
					})
				}
				loginStatus.value = 1

				sessionStorage.setItem('userInfo', JSON.stringify(userInfo))

				localStorage.setItem('username', username)
				//单位名称
				message.success('登录成功')

				org.loadOrgTree()
			} else {
				message.error(res.message)
			}
		} catch (err) {}

		submitloading.value = false
	})
}
const onSyncClick = async () => {
	if (loginStatus.value === 1) {
		syncButtonVisible.value = !syncButtonVisible.value
	} else {
		const res = await syncSysUser()
		if (res.code === 0) {
			message.success('同步成功')
		} else {
			message.error(res.message)
		}
	}
}
</script>

<style lang="less" scoped>
.new-login {
	width: 100%;
	height: 100%;
	background: url(./image/bg.png) no-repeat center / cover;
	overflow: hidden;
	// .select-cadre {
	// 	background: linear-gradient(90deg, #18b5d8 0%, #14c4eb 100%) !important;
	// }

	.inner-box {
		margin: 10.88vh auto 0;
		width: 1069px;
		display: flex;
		flex-direction: column;
		align-items: center;
		.header {
			width: 100%;
			height: 155px;
			background: url(./image/header.png) no-repeat center / contain;
		}
		.form-box {
			margin-top: 38px;
			padding: 46px;
			width: 554px;
			background: linear-gradient(to bottom, #daf1ff 100%, #daf1ff 0%);
			border-radius: 10px;
			border: 1px solid #ffffff;

			// ::v-deep(.ant-form-item) {
			// 	margin-bottom: 35px;
			// }
			// ::v-deep(.ant-form-item-with-help) {
			// 	margin-bottom: 0px;
			// }
			::v-deep(.ant-input-affix-wrapper) {
				width: 100%;
				height: 68px;
				background: rgba(255, 255, 255, 0.5);
				border-radius: 4px 4px 4px 4px;
				opacity: 1;
				input {
					background-color: transparent;
					font-size: 20px;
				}
			}
			::v-deep(button) {
				width: 100%;
				height: 48px;
				box-shadow: 0px 4px 4px 0px rgba(96, 179, 255, 0.35);
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				font-size: 19px;
			}
		}
	}
	.menu-box {
		margin: 12.5vh auto;
		width: 1463px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		.system-img {
			width: 100%;
			height: 184px;
		}
		.system-menu {
			margin-top: 95px;
			display: flex;
			.menu-card {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 280px;
				height: 160px;
				background: linear-gradient(90deg, #1796ef 0%, #3fa0f2 100%);
				border-radius: 10px 10px 10px 10px;
				opacity: 1;
				margin-right: 106px;
				&:nth-last-child(1) {
					margin-right: 0px;
				}

				span {
					font-size: 40px;
					font-family: Source Han Sans CN-Bold, Source Han Sans CN;
					font-weight: bold;
					color: #ffffff;
				}
			}
			.limit-system {
				width: 330px;
			}
		}
	}
	.renzheng {
		position: absolute;
		bottom: 48px;
		left: 50%;
		transform: translate(-50%, 0);

		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		font-size: 26px;
		line-height: 26px;
		color: #ffffff;

		display: flex;
		align-items: center;

		img {
			width: 36px;
			height: 36px;
		}
	}
	.sync-button {
	}
	.data-sync {
		position: absolute;
		top: 90vh;
		right: 0px;
		padding: 0px 10px;
		min-width: 196px;
		height: 70px;
		background: rgba(22, 112, 179, 0.4);
		box-shadow: 0px 0 16px 0px rgba(19, 19, 19, 0.1);
		border-radius: 10px 0px 0px 10px;
		display: flex;
		align-items: center;
		justify-content: center;
		.sync-button {
			display: flex;
			align-items: center;
			justify-content: center;
			.sync-icon {
				margin-right: 10px;
				display: inline-block;
				width: 30px;
				height: 30px;
				background: url('./image/sync-icon.png') no-repeat center / contain;
				transition: all 0.3s cubic-bezier(0.42, 0, 0.73, 1.6);
			}
			.text {
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				font-size: 28px;
				color: rgba(255, 255, 255, 0.8);
			}
			.data {
				background: url('./image/data-icon.png') no-repeat center / contain !important;
			}
			.avatar {
				background: url('./image/avatar-icon.png') no-repeat center / contain !important;
			}
		}
		.drop-box {
			position: absolute;
			left: 0px;
			top: 0px;
			width: 146px;
			background: rgba(22, 112, 179, 0.4);
			box-shadow: 0px 0 16px 0px rgba(19, 19, 19, 0.1);
			transform: translateY(-109%);
			.drop-inner-box {
				width: 100%;
				.sync-button {
					padding: 10px 0px;
				}
			}
			&::after {
				content: '';
				display: inline-block;
				position: absolute;
				bottom: 0px;
				left: 50%;
				transform: translate(-50%, 100%);
				width: 0px;
				height: 0px;
				border: transparent solid 10px;
				border-top: rgba(22, 112, 179, 0.4) solid 10px;
			}
		}
	}
}
.margin-top-35 {
	margin-top: 35px;
}
.icon-common {
	display: inline-block;
	width: 24px;
	height: 24px;
}
.icon-user {
	.icon-common;
	background: url(./image/user.png) center / cover no-repeat;
}
.icon-password {
	.icon-common;
	background: url(./image/password.png) center / cover no-repeat;
}
.icon-code {
	.icon-common;
	background: url(./image/code.png) center / cover no-repeat;
}
.verification-code {
	position: absolute;
	right: 0;
	top: 50%;
	transform: translate(0, -50%);
	z-index: 99;
	display: inline-block;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	width: 120px;
	height: 40px;
	cursor: pointer;
	border: none;
}
.drop-enter-from,
.drop-leave-to {
	opacity: 0;
}
.drop-enter-active,
.drop-leave-active {
	transition: all 0.3s ease;
}
.rotateZ-90deg {
	transform: rotateZ(45deg);
}
</style>
<style lang="less">
.ant-modal-confirm-title {
	font-size: 30px !important;
	line-height: 1;
}
.ant-modal-confirm-body {
	.anticon-info-circle {
		margin-top: 6px;
		width: 30px;
		height: 30px;
		font-size: 30px;
	}
	.ant-modal-confirm-content {
		font-size: 25px;
		height: 150px;
	}
}
.ant-modal-confirm-btns {
	button {
		height: 40px;
		width: 120px;
		font-size: 20px;
	}
}
</style>
