<template>
	<div class="cadre-form">
		<div class="header"></div>
		<div class="table-box">
			<table>
				<colgroup align="left" :class="`col-group-${index}`" :style="{ width: item.width }" v-for="(item, index) in colgroup" :key="item"></colgroup>
				<tbody>
					<tr>
						<td colspan="2" class="table-item__title">姓名</td>
						<td colspan="2">{{ data.name }}</td>
						<td colspan="2" class="table-item__title">性别</td>
						<td colspan="2">{{ data.gender }}</td>
						<td colspan="2" class="table-item__title">出生岁月(岁)</td>
						<td colspan="2" class="min-padding">{{ data.birth_and_age }}</td>
						<td colspan="2" rowspan="4" class="td-avatar">
							<div class="avatar">
								<CodeAvatar :head_url="data.head_url">
									<template #avatar="{ avatar }">
										<div class="avatar-img" :style="{ backgroundImage: `url(${avatar})` }"></div>
									</template>
								</CodeAvatar>
							</div>
						</td>
					</tr>
					<tr>
						<td colspan="2" class="table-item__title">民族</td>
						<td colspan="2">{{ data.ethnicity }}</td>
						<td colspan="2" class="table-item__title">籍贯</td>
						<td colspan="2" class="min-padding">{{ data.native_place }}</td>
						<td colspan="2" class="table-item__title">出生地</td>
						<td colspan="2" class="min-padding">{{ data.birth_place }}</td>
					</tr>
					<tr>
						<td colspan="2" class="table-item__title">入党时间</td>
						<td colspan="2">{{ data.party_join_date }}</td>
						<td colspan="2" class="table-item__title">参加工作时间</td>
						<td colspan="2">{{ data.work_join_date }}</td>
						<td colspan="2" class="table-item__title">健康状况</td>
						<td colspan="2">{{ data.health_status }}</td>
					</tr>
					<tr>
						<td colspan="2" class="table-item__title min-padding">专业技术职务</td>
						<td colspan="4">{{ data.professional_title }}</td>
						<td colspan="2" class="table-item__title tx-align-ct min-padding">
							<div>熟悉专业</div>
							<div>有何专长</div>
						</td>
						<td colspan="4">{{ data.expertise }}</td>
					</tr>

					<tr>
						<td colspan="2" rowspan="2" class="table-item__title">学历学位</td>
						<td colspan="2" class="table-item__title">全日制教育</td>
						<td colspan="4">
							<div class="education" v-for="(item, index) in data.full_time_education" :key="index">
								{{ item }}
							</div>
						</td>
						<td colspan="2" class="table-item__title tx-align-ct">毕业院校系及专业</td>
						<td colspan="4">
							<div class="education tx-align-lt" v-for="(item, index) in data.full_time_education_school" :key="index">
								{{ item }}
							</div>
						</td>
					</tr>
					<tr>
						<td colspan="2" class="table-item__title">在职教育</td>
						<td colspan="4">
							<div class="education" v-for="(item, index) in data.part_time_education" :key="index">
								{{ item }}
							</div>
						</td>
						<td colspan="2" class="table-item__title tx-align-ct">毕业院校系及专业</td>
						<td colspan="4">
							<div class="education tx-align-lt" v-for="(item, index) in data.part_time_education_school" :key="index">
								{{ item }}
							</div>
						</td>
					</tr>
					<tr>
						<td colspan="4" class="table-item__title">现任职务</td>
						<td colspan="10" class="tx-align-lt">{{ data.current_job }}</td>
					</tr>
					<!-- <tr>
						<td colspan="4" class="table-item__title">分管领域（工作）</td>
						<td colspan="10" class="tx-align-lt">{{ data.work_domain }}</td>
					</tr> -->
					<tr>
						<td colspan="4" class="table-item__title">拟任职务</td>
						<td colspan="10" class="tx-align-lt">{{ data.intended_position }}</td>
					</tr>
					<tr>
						<td colspan="4" class="table-item__title">拟免职务</td>
						<td colspan="10" class="tx-align-lt">{{ data.intended_removal_position }}</td>
					</tr>
					<tr>
						<td colspan="1" class="table-item__title table-item__title-vertical tx-align-ct letter-space">
							<div class="max-height-60">简历</div>
						</td>
						<td colspan="13">
							<div class="resume-box" v-for="(item, index) in data.resume" :key="index">
								<div class="time">{{ item.time_difference }}</div>
								<div class="remark">{{ item.remark }}</div>
							</div>
						</td>
					</tr>
					<tr>
						<td colspan="1" class="table-item__title table-item__title-vertical tx-letter-space-4">奖惩情况</td>
						<td colspan="13" class="tx-align-lt">{{ data?.rewards_and_punishments?.join('') }}</td>
					</tr>
					<tr>
						<td colspan="1" class="table-item__title table-item__title-vertical min-padding-row">
							<div class="split-text">
								<div class="left">年度考</div>
								<div class="right">核结果</div>
							</div>
						</td>
						<td colspan="13" class="tx-align-lt">{{ data?.annual_assessment_results?.join('') }}</td>
					</tr>
					<tr>
						<td colspan="1" class="table-item__title table-item__title-vertical tx-letter-space-4">班子回访</td>
						<td colspan="13" class="tx-align-lt">{{ data.team_visit }}</td>
					</tr>
					<tr>
						<td colspan="1" class="table-item__title table-item__title-vertical tx-letter-space-4">任免理由</td>
						<td colspan="13" class="tx-align-lt">{{ data.appointment_reason }}</td>
					</tr>
					<tr>
						<td colspan="1" class="table-item__title table-item__title-vertical tx-align-ct tx-letter-space-4">家庭主要成员及重要社会关系</td>
						<td colspan="13" style="padding: 0">
							<div class="inner-table" style="width: 100%; height: 100%">
								<table border="0">
									<thead>
										<tr>
											<td class="first-column">称谓</td>
											<td>姓名</td>
											<td>出生日期</td>
											<td>政治面貌</td>
											<td class="inner-table-position last-column">工作单位及职务</td>
										</tr>
									</thead>
									<tbody>
										<tr v-for="(item, index) in data.family_members" :key="index">
											<td class="first-column">{{ item.appellation }}</td>
											<td>{{ item.name }}</td>
											<td>{{ item.birth_date }}</td>
											<td>{{ item.positive_identity }}</td>
											<td class="last-column">{{ item.work_unit_and_position }}</td>
										</tr>
										<template v-if="toNumber > 0">
											<tr v-for="_item in toNumber" :key="_item">
												<td
													v-for="item in 5"
													:key="item"
													:class="[`${_item === toNumber ? 'last-row' : ''}`, item === 1 && 'first-column', item === 5 && 'last-column']"
												></td>
											</tr>
										</template>
									</tbody>
								</table>
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import { getCadreFormInfo } from '@/apis/cadreForm'
import { getUserInfoItem } from '@/utils/utils'
import { message } from 'ant-design-vue'
import CodeAvatar from '@/components/CodeAvatar.vue'
const CDN_URL = import.meta.env.VITE_CDN_URL

const user_id = getUserInfoItem('user_id')
const route = useRoute()

const data = ref({
	user_id: '',
	name: '',
	head_url: '',
	gender: '',
	birth_and_age: '',
	ethnicity: '',
	native_place: '',
	birth_place: '',
	party_join_date: '',
	work_join_date: '',
	health_status: '',
	professional_title: '',
	expertise: '',
	full_time_education: [],
	full_time_education_school: [],
	part_time_education: [],
	part_time_education_school: [],
	current_job: '',
	work_domain: '',
	intended_position: '',
	intended_removal_position: '',
	resume: [],
	rewards_and_punishments: [],
	annual_assessment_results: [],
	team_visit: '',
	appointment_reason: '',
	family_members: [],
})
const colgroup = [
	{
		width: '7.142%',
	},
	{
		width: '7.142%',
	},
	{
		width: '7.142%',
	},
	{
		width: '7.142%',
	},
	{
		width: '7.142%',
	},
	{
		width: '7.142%',
	},
	{
		width: '7.142%',
	},
	{
		width: '7.142%',
	},
	{
		width: '7.142%',
	},
	{
		width: '7.142%',
	},
	{
		width: '7.142%',
	},
	{
		width: '7.142%',
	},
	{
		width: '7.142%',
	},
]

const avatarUrl = computed(() => (data.value.head_url ? `url("${CDN_URL}/fr_img/${data.value.head_url}")` : ''))

const toNumber = computed(() => 9 - data.value.family_members.length)

const loadData = async () => {
	const res = await getCadreFormInfo({ user_id: route.query.user_id || user_id })
	if (res.code === 0) {
		const { data: _data } = res
		data.value = _data
	}
}
loadData()
</script>

<style lang="less" scoped>
.cadre-form {
	width: 100%;
	height: 100%;
	overflow-y: auto;

	&::-webkit-scrollbar {
		width: 0px;
		height: 0px;
	}
	// .header {
	// 	width: 100%;
	// 	height: 500px;
	// 	background: url('@/assets/images/cadre-form.png') center center / cover;
	// }
	.table-box {
		margin: 0 auto;
		padding: 24px 24px;
		margin-top: 20px;
		width: 1500px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;

		table {
			width: 100%;
			border-collapse: collapse;
			margin: 0 auto;
			text-align: center;
		}
		table p {
			display: inline-block;
			text-align: right;
			margin-top: 70px;
		}
		table td {
			padding: 14px 25px;
			font-size: 18px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #222222;
			min-height: 60px;
			border: 1px solid #d8dadd;
		}

		.td-avatar {
			padding: 0;
			.avatar {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 100%;
				background-color: #f0f0f0;
				img {
					width: 166px;
					height: 219px;
					background: center/cover no-repeat;
				}
			}
		}
		.inner_title {
			width: 100%;
			min-height: 60px;
		}
		.education {
			text-align: center;
		}
		.resume-box {
			width: 100%;
			display: flex;
			.time {
				text-align: left;
				width: 200px;
				flex-shrink: 0;
			}
			.remark {
				flex: 1;
				text-align: left;
			}
		}

		.inner-table {
			table {
				border: 0px;
				.first-column {
					border-left: 0px;
				}
				.last-row {
					border-bottom: 0px;
				}
				.last-column {
					border-right: 0px;
				}
				td {
					height: 60px;
					border: 1px solid #d8dadd;
					border-top: 0px;
					border-collapse: collapse;
				}
				.inner-table-position {
					width: 400px;
				}
			}
		}
	}
}
.min-padding {
	padding: 5px 23px !important;
}
.tx-align-lt {
	text-align: left !important;
	text-align-last: left;
}
.tx-align-ct {
	text-align: center;
	text-align-last: center !important;
	-moz-text-align-last: center !important;
}
.tx-letter-space-4 {
	letter-spacing: 4px;
}
.table-item__title {
	text-align-last: justify;
	text-align: justify;
	word-break: break-all;
	text-justify: distribute;
}
.table-item__title-vertical {
	writing-mode: vertical-lr;
	text-align: center;
}
.tx-align-justify {
	text-align-last: justify;
}
.min-padding-row {
	padding-left: 5px;
	padding-right: 5px;
}
.split-text {
	display: flex;
	flex-direction: column;
}
// colgroup

.letter-space {
	letter-spacing: 40px;
}
</style>
