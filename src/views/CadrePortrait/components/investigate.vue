<template>
	<div class="investigate">
		<div class="title">{{ user.name }}</div>
		<div class="radio-box">
			<div :class="`radio ${item.key === activeKey ? 'active' : ''}`" @click="onActive(item.key)" v-for="(item, index) in radio" :key="item.key">
				{{ item.label }}
			</div>
		</div>
		<div class="list-box" v-if="activeKey === '1'">
			<div class="list" v-for="(item, index) in data" :key="index">
				<template v-if="item.year === 2024">
					<div class="header">{{ item.year }}年度班子回访调研评价</div>
					<div class="content">
						<div class="flex jusitify year-2024">
							<div class="content-item flex-1">
								<div class="pre_fix"><span class="text-block">时任职务</span></div>
								<div class="text">{{ item.job }}</div>
							</div>
							<div class="content-item flex-1">
								<div class="pre_fix"><span class="text-block">回访组排序</span></div>
								<div class="text">{{ item.group_rank }}</div>
							</div>
							<div class="content-item m-top-0 flex-1" v-if="item.leader_rank">
								<div class="pre_fix"><span class="text-block">一把手排序</span></div>
								<div class="text">{{ item.leader_rank }}</div>
							</div>
						</div>
						<div class="content-item">
							<div class="pre_fix"><span class="text-block">评价</span></div>
							<div class="text">
								{{ item.assessment }}
							</div>
						</div>
						<div class="content-item">
							<div class="pre_fix"><span class="text-block">个人意愿</span></div>
							<div class="c-i-content m-top-12">
								<div class="tag-box">
									<div class="tag-item" :class="{ 'tag-item-active': item.personal_will_map?.tb === '1' }">提拔</div>
									<div class="tag-item" :class="{ 'tag-item-active': item.personal_will_map?.zy === '1' }">重用</div>
									<div class="tag-item" :class="{ 'tag-item-active': item.personal_will_map?.zg === '1' }">转岗</div>
									<div class="tag-item" :class="{ 'tag-item-active': item.personal_will_map?.jl === '1' }">交流</div>
									<div class="tag-item" :class="{ 'tag-item-active': item.personal_will_map?.lr === '1' }">留任</div>
									<div class="tag-item" :class="{ 'tag-item-active': item.personal_will_map?.gf === '1' }">改非</div>
									<div class="flex">
										<div class="tag-item" :class="{ 'tag-item-active': item.personal_will_map?.qt === '1' }">其他</div>
										<div class="tag-item" style="border-left: 0px" v-if="item.personal_will_map?.qt_remark">
											{{ item.personal_will_map?.qt_remark }}
										</div>
									</div>
								</div>
								<div class="text m-top-10 cic-desc">具体描述：{{ item.personal_will_map?.detail }}</div>
							</div>
						</div>

						<div class="level-icon" v-if="item.attention === 1">
							<img src="../images/zhongdianguanzhu.png" alt="" srcset="" />
						</div>
					</div>
				</template>
				<template v-else>
					<div class="header">{{ item.year }}年度班子回访调研评价</div>
					<div class="content">
						<div class="content-item">
							<div class="pre_fix"><span class="text-block">时任职务</span></div>
							<div class="text">{{ item.job }}</div>
						</div>
						<div class="content-item">
							<div class="pre_fix"><span class="text-block">民主测评</span></div>
							<div class="text">{{ item.evaluation }}</div>
						</div>
						<div class="content-item">
							<div class="pre_fix"><span class="text-block">评价</span></div>
							<div class="text">
								{{ item.assessment }}
							</div>
						</div>
					</div>
				</template>
			</div>
		</div>
		<div class="list-box" v-if="activeKey === '2'">
			<div class="list" v-for="(item, index) in data1" :key="index">
				<div class="header">{{ item.year_round }}</div>
				<div class="content report">
					<div class="flex jusitify">
						<div class="content-item flex-1">
							<div class="pre_fix"><span class="text-block">被巡视巡象时间</span></div>
							<div class="text">{{ item.time }}</div>
						</div>
						<div class="content-item flex-1">
							<div class="pre_fix"><span class="text-block">巡查组</span></div>
							<div class="text">{{ item.patrol_group }}</div>
						</div>
					</div>
					<div class="flex">
						<div class="content-item flex-1">
							<div class="pre_fix"><span class="text-block">时任职务</span></div>
							<div class="text">
								<!-- 某某镇政法委员、人武部长、副镇长 -->
								{{ item.job }}
							</div>
						</div>
						<div class="content-item flex-1">
							<div class="pre_fix"><span class="text-block">政治生态评级</span></div>
							<div class="text">
								{{ item.grade }}
							</div>
						</div>
					</div>

					<div class="content-item">
						<div class="pre_fix"><span class="text-block">正面评价</span></div>
						<div class="text">
							{{ item.virtue }}
						</div>
					</div>
					<div class="content-item">
						<div class="pre_fix"><span class="text-block">主要问题和不足</span></div>
						<div class="text">
							{{ item.drawback }}
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="list-box" v-if="activeKey === '4'">
			<a-table :columns="columns" :data-source="data3" bordered :pagination="false"></a-table>
		</div>
		<div class="list-box" v-if="activeKey === '6'">
			<div class="list" v-for="(item, index) in data6" :key="index">
				<div class="header">{{ item.classes }}</div>
				<div class="content">
					<div class="content-item">
						<div class="pre_fix"><span class="text-block">时任职务</span></div>
						<div class="text">{{ item.job }}</div>
					</div>
					<div class="content-item">
						<div class="pre_fix"><span class="text-block">排名</span></div>
						<div class="text">{{ item.rank }}</div>
					</div>

					<div class="level-icon" v-if="item.excellent === 1">
						<img src="../images/youxiuxueyuan.png" alt="" srcset="" />
					</div>
				</div>
			</div>
		</div>
		<div class="list-box" v-if="activeKey === '7'">
			<div class="list" v-for="(item, index) in data7.list" :key="index">
				<div class="header">{{ item.year }}</div>
				<div class="content" v-if="data7.user_type === 3">
					<div class="flex jusitify">
						<div class="content-item">
							<div class="pre_fix"><span class="text-block">时任职务</span></div>
							<div class="text">{{ item.job }}</div>
						</div>
					</div>

					<ExpandBox title="思想认识">{{ item.ideology }}</ExpandBox>
					<ExpandBox title="工作综述">{{ item.work_summary }}</ExpandBox>
					<ExpandBox title="典型案例"> {{ item.typical_case }}</ExpandBox>
					<ExpandBox title="主要不足"> {{ item.deficiency }}</ExpandBox>
				</div>

				<div class="content" v-else>
					<div class="flex jusitify">
						<div class="content-item">
							<div class="pre_fix"><span class="text-block">时任职务</span></div>
							<div class="text">{{ item.job }}</div>
						</div>
						<div class="content-item">
							<div class="pre_fix w-425"><span class="text-block">分管联系工作</span></div>
							<div class="text">{{ item.charge_range }}</div>
						</div>
					</div>

					<ExpandBox title="主要特点及具体事例">{{ item.merit }}</ExpandBox>
					<ExpandBox title="主要不足及具体事例">{{ item.drawback }}</ExpandBox>
					<ExpandBox title="最满意的工作">{{ item.satisfaction }}</ExpandBox>
					<ExpandBox title="不满意的工作">{{ item.dissatisfaction }}</ExpandBox>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { reactive, ref, watchEffect } from 'vue'
import { returnVisit, ecologicalReport, getPerformance, getUserTrain, getAnualReport } from '@/apis/cadre-portrait/home'
import ExpandBox from './expandBox.vue'
const props = defineProps<{
	user: {
		user_id: number
		[key: string]: any
	}
}>()

// 班子回访
const data = ref<any[]>([])
// 巡视巡察
const data1 = ref<any[]>([])
// 重大表现
const data3 = ref<any[]>([])
// 培训情况
const data6 = ref<any[]>([])
// 年度述职
// const data7 = ref<any>({
// 	user_type: 2,
// 	list: [
// 		{
// 			annual_report_id: 1,
// 			user_id: 123,
// 			year: 2023,
// 			job: 'Manager',
// 			charge_range: 'Finance',
// 			merit: 'Excellent performance',
// 			drawback: 'Needs improvement in communication',
// 			satisfaction: 'Team collaboration',
// 			dissatisfaction: 'Workload balance',
// 			ideology: 'Positive outlook',
// 			work_summary: 'Successful project execution',
// 			typical_case: 'Led a major initiative',
// 			deficiency: 'Time management',
// 		},
// 	],
// })
const data7 = ref<any>({
	user_type: undefined,
	list: [],
})
// 展开状态
const expandStatus = reactive<any>({
	'1': false,
	'2': false,
	'3': false,
	'4': false,
	'5': false,
	'6': false,
	'7': false,
})
const radio = [
	{
		label: '班子回访评价',
		key: '1',
	},
	{
		label: '巡视巡察评价',
		key: '2',
	},
	{
		label: '审计评价',
		key: '3',
	},
	{
		label: '重大表现',
		key: '4',
	},
	{
		label: '调整动议情况',
		key: '5',
	},
	{
		label: '培训情况',
		key: '6',
	},
	{
		label: '年度述职',
		key: '7',
	},
]

const getLevel = (level: any) => {
	const levelMap: any = {
		1: 'A等次',
		2: 'B等次',
		3: 'C等次',
		4: 'D等次',
	}
	return levelMap[level]
}
const activeKey = ref(radio[0].key)

const columns = [
	{
		dataIndex: 'current_job',
		key: 'current_job',
		title: '时任职务',
		align: 'left',
	},
	{
		dataIndex: 'event',
		key: 'event',
		title: '事件',
		align: 'left',
	},
	{
		dataIndex: 'describe',
		key: 'describe',
		title: '简要描述',
		align: 'left',
	},
]

const onActive = (key: any) => {
	activeKey.value = key
}

const onExpand = (type: string) => {
	expandStatus[type] = !expandStatus[type]
}

const fetchData = async (userId: number) => {
	returnVisit(userId).then((res) => {
		if (res.code === 0) {
			data.value = res.data
		}
	})
	ecologicalReport(userId).then((res) => {
		if (res.code === 0) {
			data1.value = res.data
		}
	})
	getPerformance({ user_id: userId }).then((res) => {
		if (res.code === 0) {
			data3.value = res.data
		}
	})
	getUserTrain({
		user_id: userId,
	}).then((res) => {
		if (res.code === 0) {
			data6.value = res.data
		}
	})
}

const getAnualReportApi = async (user_id: number) => {
	const res = await getAnualReport({ user_id })
	if (res.code === 0) {
		data7.value = res.data

		console.log(data7.value.user_type)
	}
}

// 监听 user 变化
watchEffect(() => {
	const { user_id } = props.user
	fetchData(user_id)
	getAnualReportApi(user_id)
})
</script>

<style lang="less" scoped>
.flex {
	display: flex;
}
.flex-1 {
	flex: 1;
}
.jusitify {
	justify-content: space-between;
}
.m-top-0 {
	margin-top: 0 !important;
}
.investigate {
	padding: 36px 156px;
	width: 100%;
	height: 100%;
	background: #ffffff;
	overflow: auto;

	.title {
		font-size: 28px;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 600;
		color: #000000;
	}

	.radio-box {
		margin-top: 38px;
		display: flex;
		.radio {
			border: 1px solid rgba(229, 37, 27, 0.4);
			margin-right: 34px;
			padding: 8px 10px;
			background: rgba(229, 37, 27, 0.03);
			border-radius: 4px;
			font-size: 20px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #e5251b;
			line-height: 23px;
		}
		.active {
			background: #e5251b;
			color: #ffffff;
		}
	}

	.list-box {
		margin-top: 30px;
		.list {
			border-radius: 10px;
			overflow: hidden;

			.level-icon {
				position: absolute;
				right: 10px;
				top: 0px;
				width: 162px;
				height: 162px;
				img {
					width: 100%;
					height: 100%;
				}
			}
			&:not(:first-child) {
				margin-top: 26px;
			}
			.header {
				padding: 18px 24px;
				display: flex;
				align-items: center;
				background: #daf4dd;
				opacity: 1;
				font-size: 24px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #000000;
				line-height: 28px;
				&::before {
					content: '';
					display: inline-block;
					margin-right: 12px;
					width: 8px;
					height: 24px;
					background: url('../images/list-header-icon.png') center / 100% 100% no-repeat;
				}
			}
			.content {
				display: flex;
				flex-direction: column;
				gap: 24px 0px;
				position: relative;
				background: #f6f9fc;
				padding: 31px 28px;
				.year-2024 {
					width: calc(100% - 162px - 40px);
				}
				.content-item {
					.pre_fix {
						margin-right: 15px;
						flex-shrink: 0;
						font-size: 22px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 500;
						color: rgba(0, 0, 0, 0.95);
						line-height: 39px;
						.text-block {
							display: inline-block;
							min-width: 90px;
							width: fit-content;
							text-align-last: justify;
						}
						&::before {
							margin-right: 9px;
							content: '';
							display: inline-block;
							width: 12px;
							height: 12px;
							background: #ec4224;
							border-radius: 0px 0px 0px 0px;
							opacity: 1;
							border-radius: 50%;
						}
					}
					.expand-icon {
						display: flex;
						align-items: center;
						cursor: pointer;
						img {
							margin-right: 8px;
							width: 32px;
							height: 32px;
							vertical-align: middle;
							transform: rotateZ(180deg);
						}
						span {
							font-size: 22px;
							line-height: 22px;
							color: #ee391f;
						}
					}
					.c-i-content {
						padding-left: 20px;
						.tag-box {
							display: flex;
							gap: 0px 10px;
							.tag-item {
								padding: 5px 18px;
								min-width: 80px;
								text-align: center;
								font-family: Source Han Sans CN, Source Han Sans CN;
								font-weight: 400;
								font-size: 22px;
								color: #000000;
								line-height: 22px;
								border: 1px solid #d9d9d9;
							}
							.tag-item-active {
								background: #097afb;
								color: #ffffff;
								border-color: #097afb;
							}
						}
					}
					.cic-desc {
						padding-left: 0px !important;
					}
					.text {
						margin-top: 10px;
						padding-left: 20px;
						font-size: 20px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.95);
						line-height: 26px;
					}
				}
			}
			.report {
				.pre_fix {
					.text-block {
						width: 170px;
					}
				}
			}
		}
	}
}

:deep(.ant-table-container) {
	.ant-table-cell {
		font-size: 20px !important;
	}
	.ant-table-tbody {
		.ant-table-cell {
			font-size: 18px !important;
		}
	}
}
</style>
