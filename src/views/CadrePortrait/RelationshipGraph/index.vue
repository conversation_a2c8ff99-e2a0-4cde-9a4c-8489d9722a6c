<template>
	<div class="relationship-graph">
		<div class="graph-container">
			<a-tabs v-model:activeKey="currentView" class="view-tabs" @change="handleTabChange">
				<a-tab-pane key="relative" tab="亲属关系">
					<RelationGraph ref="seeksRelationGraph" :options="options.userGraphOptions" :on-node-click="onNodeClick">
						<template v-slot:node="{ node }">
							<NodeTop :node="node" v-if="node.data.slotType === 'top'" :avatar="user.detail.avatar" />
							<Vnode v-else-if="node.data.whether === 1" />
							<NodeNormal :node="node" v-else :select="selectId" />
						</template>
					</RelationGraph>
				</a-tab-pane>
				<a-tab-pane key="classmate" tab="校友关系">
					<RelationGraph ref="seeksSchoolRelationGraph" :options="options.schoolGraphOptions" :on-node-click="onNodeClick">
						<template v-slot:node="{ node }">
							<NodeTop :node="node" v-if="node.data.slotType === 'top'" :avatar="user.detail.avatar" />
							<Vnode v-else-if="node.data.whether === 1" />
							<NodeNormal :node="node" v-else :select="selectId" />
						</template>
					</RelationGraph>
				</a-tab-pane>
				<a-tab-pane key="work" tab="工作关系">
					<RelationGraph ref="seeksWorkRelationGraph" :options="options.workGraphOptions" :on-node-click="onNodeClick">
						<template v-slot:node="{ node }">
							<NodeTop :node="node" v-if="node.data.slotType === 'top'" :avatar="user.detail.avatar" />
							<Vnode v-else-if="node.data.whether === 1" />
							<NodeNormal :node="node" v-else :select="selectId" />
						</template>
					</RelationGraph>
				</a-tab-pane>
			</a-tabs>
		</div>
		<div class="tips-box">
			<div class="tips-item tips-area">
				<div class="tips-item__icon"></div>
				<div class="tips-item__text">丰都行政机构体系内</div>
			</div>
			<div class="tips-item unit">
				<div class="tips-item__icon"></div>
				<div class="tips-item__text">本人同单位</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { getRelationship } from '@/apis/cadre-portrait/relationship'
import useUser from '@/store/user'
import RelationGraph from 'relation-graph/vue3'
import { computed, inject, onBeforeUnmount, onMounted, reactive, ref } from 'vue'
import NodeNormal from './components/node-normal.vue'
import NodeTop from './components/node-top.vue'
import Vnode from './components/v-node.vue'
import { convertDataRecursive, createUnikey, flattenAndModifyData } from './util/index'

const CDN_URL = import.meta.env.VITE_CDN_URL

const user = useUser()
const selectId = ref('')
const seeksRelationGraph = ref()
const seeksSchoolRelationGraph = ref()
const seeksWorkRelationGraph = ref()
const options = reactive({
	slotTeamplateId: 'slot1',
	userGraphOptions: {
		allowSwitchLineShape: true,
		allowSwitchJunctionPoint: true,
		defaultJunctionPoint: 'border',
		defaultNodeShape: 0,
		defaultNodeBorderWidth: 0,
		defaultLineColor: '#448FFF',
		defaultLineWidth: 4,
		disableDragNode: false,
		isMoveByParentNode: true,
		disableDragCanvas: false,
		debug: false,
		layouts: [
			{
				label: '中心布局',
				layoutName: 'center',
				layoutClassName: 'seeks-layout-force',
				distance_coefficient: 1,
			},
		],
		defaultLineMarker: {
			markerWidth: '0',
			markerHeight: '0',
			refX: 6,
			refY: 6,
			data: 'M2,2 L10,6 L2,10 L6,6 L2,2',
		},
	},
	schoolGraphOptions: {
		allowSwitchLineShape: true,
		allowSwitchJunctionPoint: true,
		defaultJunctionPoint: 'border',
		defaultNodeShape: 0,
		defaultNodeBorderWidth: 0,
		defaultLineColor: '#448FFF',
		defaultLineWidth: 4,
		disableDragNode: false,
		isMoveByParentNode: true,
		disableDragCanvas: false,
		debug: false,
		layouts: [
			{
				label: '中心布局',
				layoutName: 'center',
				layoutClassName: 'seeks-layout-force',
				distance_coefficient: 1,
			},
		],
		defaultLineMarker: {
			markerWidth: '0',
			markerHeight: '0',
			refX: 6,
			refY: 6,
			data: 'M2,2 L10,6 L2,10 L6,6 L2,2',
		},
	},
	workGraphOptions: {
		allowSwitchLineShape: true,
		allowSwitchJunctionPoint: true,
		defaultJunctionPoint: 'border',
		defaultNodeShape: 0,
		defaultNodeBorderWidth: 0,
		defaultLineColor: '#448FFF',
		defaultLineWidth: 4,
		disableDragNode: false,
		isMoveByParentNode: true,
		disableDragCanvas: false,
		debug: false,
		layouts: [
			{
				label: '中心布局',
				layoutName: 'center',
				layoutClassName: 'seeks-layout-force',
				distance_coefficient: 1,
			},
		],
		defaultLineMarker: {
			markerWidth: '0',
			markerHeight: '0',
			refX: 6,
			refY: 6,
			data: 'M2,2 L10,6 L2,10 L6,6 L2,2',
		},
		defaultLineTextOffset_x: 2, // 文字水平偏移量
		defaultLineTextOffset_y: -3, // 文字垂直偏移量
		lineTextAnchor: 'middle', // 文字锚点位置
		lineTextPlace: 'middle', // 文字在曲线上的位置
		lineLabel: {
			fontSize: 34, // 文字大小
			color: '#000', // 文字颜色
			strokeWidth: 2, // 描边宽度
			padding: [20, 5], // 文字周围的填充
		},
	},
})

const avatarUrl = computed(() => (user.detail.avatar ? `${CDN_URL}/fr_img/${user.detail.avatar}` : ''))
const user_id = inject('user_id') as string | undefined
const currentView = ref('relative') // 默认显示亲属关系

const addClassToList = (list: any) => {
	return list.map((item: any, index: number) => {
		const newItem: any = { styleClass: '' }
		if (index === 0) {
			newItem.styleClass = 'c-node-menu-item__top'
			newItem.disableDrag = true
		} else if (item.whether === 1) {
			newItem.styleClass = 'c-node-menu-item__vnode'
		} else {
			newItem.styleClass = 'c-node-menu-item__normal'
		}
		return {
			...item,
			...newItem,
		}
	})
}

const onNodeClick = (data: any) => {
	selectId.value = data.data.id
}

const createData = (data: any, type: string) => {
	const __d: any[] = createUnikey([data]) // 确保 __d 是一个数组
	console.log(__d, '这个是数据源data')
	const nodes = addClassToList(flattenAndModifyData(__d))
	const lines = convertDataRecursive(__d)
	console.log(lines, '可以构造线条')

	// 根据关系类型决定是否添加 line.text
	if (type === 'work') {
		lines.forEach((line: any) => {
			// 遍历 __d 数组查找对应的 children 和 remark 字段
			for (const item of __d) {
				if (item.children) {
					const child = item.children.find((child: any) => child.op_key === line.target)
					if (child && child.remark) {
						line.text = child.remark
						break
					}
				}
			}
			// 如果没有找到 remark 或 line.text 为空，则设置默认值
			if (!line.text) {
				line.text = '' // 如果没有 text，设置默认值
			}
		})
	}

	return {
		nodes,
		lines,
		rootId: nodes[0].id,
	}
}
const loadRelationshipData = async (type: string) => {
	if (!user_id) {
		return
	}
	try {
		const res = await getRelationship({ user_id })
		if (res.code !== 0) {
			return
		}
		const data = res.data
		if (!data) {
			return
		}

		// 根据 type 获取对应的关系数据
		const selectedData = data.find((item: any) => item.type === (type === 'relative' ? 1 : type === 'classmate' ? 2 : 3))
		if (selectedData) {
			const graphData = createData(selectedData, type)
			if (type === 'relative') {
				seeksRelationGraph.value.setJsonData(graphData)
			} else if (type === 'classmate') {
				seeksSchoolRelationGraph.value.setJsonData(graphData)
			} else if (type === 'work') {
				seeksWorkRelationGraph.value.setJsonData(graphData)
			}
		} else {
			console.warn(`No data found for type: ${type}`)
		}
	} catch (error) {
		console.error('Failed to load relationship data:', error)
	}
}

const handleTabChange = (key: string) => {
	currentView.value = key
	loadRelationshipData(key)
}

onMounted(() => {
	// 默认加载亲属关系数据
	loadRelationshipData('relative')
})

onBeforeUnmount(() => {
	seeksRelationGraph.value.getInstance().stopAutoLayout()
	seeksSchoolRelationGraph.value.getInstance().stopAutoLayout()
	seeksWorkRelationGraph.value.getInstance().stopAutoLayout()
})
</script>

<style lang="less" scoped>
.relationship-graph {
	width: 100%;
	height: 100%;
	position: relative;
	background: #fff;

	.graph-container {
		width: 100%;
		height: 100%;
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;

		:deep(.ant-tabs) {
			width: 100%;
			height: 100%;
		}

		:deep(.ant-tabs-content) {
			height: 100%;
		}

		:deep(.ant-tabs-tabpane) {
			height: 100%;
		}
	}

	.view-tabs {
		position: absolute;
		right: 20px;
		top: 20px;
		z-index: 10;

		:deep(.ant-tabs-nav) {
			margin: 0;
			.ant-tabs-nav-wrap {
				justify-content: flex-end;
			}
		}

		:deep(.ant-tabs-tab) {
			padding: 8px 16px;
			margin: 0;
			display: inline-flex;
			align-items: center;
			justify-content: center;
			background: #fff;
			border-radius: 4px;
			margin-right: 12px;
			transition: all 0.3s;

			&:hover {
				color: #008eff;
			}

			&:last-child {
				margin-right: 0;
			}
		}

		:deep(.ant-tabs-tab-active) {
			.ant-tabs-tab-btn {
				color: #fff !important;
			}
			background: #e5251b;
		}

		:deep(.ant-tabs-ink-bar) {
			display: none;
		}

		:deep(.ant-tabs-nav-list) {
			flex-direction: row; // 改为水平排列
			display: flex;
			align-items: center;
		}
	}
}
.tips-box {
	position: absolute;
	bottom: 30px;
	left: 200px;
	.tips-item {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
		.tips-item__icon {
			width: 10px;
			height: 10px;
			border-radius: 50%;
			margin-right: 10px;
		}
		.tips-item__text {
			font-size: 12px;
			color: #333333;
		}
	}
	.tips-area {
		.tips-item__icon {
			background-color: #ff6e41;
		}
	}
	.unit {
		.tips-item__icon {
			background-color: #ff1212;
		}
	}
}
::v-deep(.c-node-menu-item__top) {
	width: 186px !important;
	height: 186px !important;
	background-color: #008eff !important;
	box-shadow: 0px 0px 23px 0px #3496e2 !important;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	border-radius: 50%;
}

::v-deep(.c-node-menu-item__normal) {
	width: 90px !important;
	height: 90px !important;
	background: linear-gradient(140deg, #75c2ff 3%, #39a4fa 19%, #108ff3 100%);
	box-shadow: 0px 4px 20px 0px rgba(0, 142, 255, 0.25);
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	border-radius: 50%;
}
::v-deep(.c-node-menu-item__vnode) {
	width: 30px !important;
	height: 30px !important;
	background: linear-gradient(173deg, #74deff 13%, #4aafff 89%, #41abff 95%);
	box-shadow: 0px 4px 20px 0px rgba(0, 142, 255, 0.25);
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	border-radius: 50%;
}
::v-deep(.rel-linediv) {
	z-index: unset !important;
}
::v-deep(.rel-nodediv) {
	z-index: unset !important;
}
</style>
