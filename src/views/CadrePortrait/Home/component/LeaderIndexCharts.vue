<template>
	<Card title="干部指数" :sub-title="title" header size-type="2">
		<template v-slot:right>
			<div class="data-menu" v-if="user.detail.promotion_type !== 4">
				<div
					:class="['menu-item', coor_menu_selected === item.key && 'menu-active']"
					v-for="item in menuComputed"
					:key="item.key"
					@click="onMenu(item.key)"
				>
					{{ item.label }}
				</div>
			</div>
		</template>
		<div class="pyramid">
			<div :class="`pyramid-item pyramid-${item.key}`" v-for="item in pyramidList" :key="item.key" @click="onActive(item.key)">
				<div class="pyramid-img">
					<div class="pyramid-label">{{ item.label }}</div>
					<img :src="item.img" alt="" />
					<div class="select" v-if="activeInfo.pyramid_index === item.key - 1">
						<div class="select-line"></div>
						<div class="text">{{ activeInfo.user_name }} {{ activeInfo.rindex }}</div>
					</div>
					<div class="splitLine">
						<div class="line-box">
							<div class="left-line"></div>
							<div class="right-line"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</Card>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, toRefs, onMounted, inject } from 'vue'
import { getPyramid } from '@/apis/cadre-portrait/home'
import { useHomeStore } from '@/store/home'
import useUser from '@/store/user'
import { debounce } from '@/utils/utils'

import ImgPyramid1 from '@/assets/images/pyramid-1.png'
import ImgPyramid2 from '@/assets/images/pyramid-2.png'
import ImgPyramid3 from '@/assets/images/pyramid-3.png'
import ImgPyramid4 from '@/assets/images/pyramid-4.png'

// 0-全县, 1-本单位, 2-同序列, 3-全部乡镇/部门
const menu = [
	{
		label: '本单位',
		key: 1,
	},
	{
		label: '同序列',
		key: 2,
	},
	{
		label: '乡镇/部门',
		key: 3,
	},
	{
		label: '全县',
		key: 0,
	},
]
const apiData = {
	user_id: 4,
	user_name: '🐎某某',
	rindex: 99.99,
	pyramid_index: 0,
	index_list: [
		[
			{
				user_id: 1,
				user_name: '🐎某某',
				r_index: 99.99,
			},
		],
		[
			{
				user_id: 3,
				user_name: '鲁迅',
				r_index: 92.46,
			},
		],
		[
			{
				user_id: 2,
				user_name: '李白',
				r_index: 88.5,
			},
		],
		[
			{
				user_id: 4,
				user_name: '杜甫',
				r_index: 73.1,
			},
		],
	],
}
// const self = [91, 28.8]
export default defineComponent({
	name: 'Coordinate',
	setup() {
		const store = useHomeStore()
		const user = useUser()
		const activeRow = ref(1)
		const activeInfo = ref<any>({})
		const { menu, coor_menu_selected, coor_user_id } = toRefs(store)
		// 用户信息
		const user_id = inject('user_id')

		const pyramidList = computed(() => {
			const list = [
				{
					img: ImgPyramid1,
					label: '10%',
					key: 1,
				},
				{
					img: ImgPyramid2,
					label: '20%',
					key: 2,
				},
				{
					img: ImgPyramid3,
					label: '30%',
					key: 3,
				},
				{
					img: ImgPyramid4,
					label: '40%',
					key: 4,
				},
			]
			return list
		})

		const title = computed(() => {
			return `${activeInfo.value.type === 1 ? '（县管正职）' : '（县管副职）'}${user.detail.promotion_type === 4 ? '(新提任)' : ''}`
		})
		const onMenu = (key: number) => {
			// currentIndex.value = key
			store.updateCoorMenuSelected(key)
		}
		/**
		 * @description: 加载数据
		 * @return {*}
		 */
		const loadData = async (user_id: any) => {
			if (!user_id) return
			// 0-全县, 1-本单位, 2-同序列, 3-全部乡镇/部门 4 新提拔
			const { promotion_type } = user.detail

			const flag = promotion_type === 4 ? 4 : coor_menu_selected.value

			const res = await getPyramid({ user_id, flag })
			if (res.code === 0 && res.data) {
				activeInfo.value = res.data
			}
		}
		const onActive = (key: number) => {
			activeRow.value = key
		}

		const menuComputed = computed(() => {
			const type = user.detail.promotion_type
			if (type === 2) {
				return store.menuFilterSame
			} else if (type === 3) {
				return store.menuFilterSpecial
			} else {
				return store.menu
			}
		})
		// 监听currentIndex变化 更新图表数据
		watch(
			coor_menu_selected,
			debounce((newValue, oldValue) => {
				newValue !== oldValue && loadData(user_id)
			}, 500),
			{
				immediate: true,
			}
		)
		/**
		 * @description:  人员切换响应
		 * @return {*}
		 */
		watch(coor_user_id, (newValue, oldValue) => {
			if (coor_user_id.value !== '-1' && newValue !== oldValue) {
				loadData(coor_user_id.value)
			}
		})
		// echarts 绑定点击事件
		// watch(echartsRef, () => {
		// 	echartsRef.value.chart.on('click', (params: any) => {
		// 		store.updateCoorUserId(params.data?.[3])
		// 	})
		// })
		onMounted(() => {
			// 角度
			const angle = 30
			// 规定邻边的长度
			const adjacent = 100
			// 获取对应容器中心点
			const primary1 = document.querySelector('.pyramid-1')
			// 存放斜边坐标点
			const axisArray = []
			// 获取宽度
			if (primary1) {
				const width = primary1.clientWidth
				// 获取中心点
				const center = width / 2
				// 计算正切
				const tan = Math.tan((angle * Math.PI) / 180)
				// 计算对边的长度
				const opposite = tan * adjacent

				axisArray.push([center + opposite, adjacent])
			}

			// function getPointIn
		})
		return {
			user,
			menu,
			title,
			coor_menu_selected,
			activeInfo,
			ImgPyramid1,
			ImgPyramid2,
			ImgPyramid3,
			ImgPyramid4,
			pyramidList,
			menuComputed,
			onMenu,
			onActive,
		}
	},
})
</script>

<style scoped lang="less">
.data-menu {
	display: flex;
	align-items: center;
	.menu-item {
		margin-left: 12px;
		font-size: 18px;
		font-weight: 500;
		color: #666666;
		// line-height: 28px;
		cursor: pointer;
	}
	.menu-active {
		color: #333333;
		position: relative;
		font-weight: bold;
	}
	.menu-active::after {
		content: '';
		display: inline-block;
		// width: 40px;
		width: 70%;
		height: 2px;
		background: #ff2121;
		border-radius: 2px 2px 2px 2px;
		position: absolute;
		bottom: -5px;
		left: 50%;
		transform: translate(-50%, 0);
	}
}

::v-deep(.pyramid) {
	padding: 0px 29px 27px 10px;
}

.pyramid {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	user-select: none;
	.pyramid-item {
		position: relative;
		transform: translate(-60px);

		.pyramid-img {
			position: relative;
			margin: 0 auto;
			.pyramid-label {
				position: absolute;
				top: 40%;
				left: -20px;
				font-size: 22px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #666666;
				line-height: 26px;
			}
			img {
				width: 100%;
				height: 100%;
			}
			.select {
				position: absolute;
				top: 0;
				left: 100%;
				display: flex;
				align-items: center;
				width: 341px;
				height: 75.5px;
				-webkit-clip-path: polygon(0 0, 100% 0%, 100% 100%, 7% 100%);
				clip-path: polygon(0 0, 100% 0%, 100% 100%, 12% 100%);
				.select-line {
					height: 1px;
					background: #278236;
					border-radius: 0px 0px 0px 0px;
					opacity: 1;
				}
				.text {
					margin-left: 10px;
					font-size: 22px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #278236;
					line-height: 26px;
				}
			}
			.splitLine {
				position: absolute;
				inset: 0;
				// background-color: rgba(0, 0, 0, 0.9);
				.line-box {
					.left-line {
					}
					.right-line {
					}
				}
			}
		}
		&:nth-child(2) {
			margin-top: -10px;
		}
		&:nth-child(3) {
			margin-top: -20px;
		}
		&:nth-child(4) {
			margin-top: -20px;
		}
	}
	.pyramid-1 {
		.pyramid-img {
			width: 74px;
			height: 86px;

			.pyramid-label {
				transform: translateX(-15px);
			}
		}
		.select {
			width: 341px;
			background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
			transform: translate(-40px);
			.select-line {
				width: 191px;
				background-color: #278236;
			}
			.text {
				color: #278236;
			}
		}
	}
	.pyramid-2 {
		.pyramid-img {
			width: 162.23px;
			height: 93.9px;
			.pyramid-label {
				left: -30px !important;
			}
		}
		.select {
			width: 271px;
			background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
			transform: translate(-40px);
			.select-line {
				width: 146px;
				background-color: #278236;
			}
			.text {
				color: #278236;
			}
		}
	}
	.pyramid-3 {
		.pyramid-img {
			width: 265.23px;
			height: 112px;
		}
		.select {
			width: 271px;
			background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
			transform: translate(-45px, 10px);
			.select-line {
				width: 126px;
				background-color: #278236;
			}
			.text {
				color: #278236;
			}
		}
	}
	.pyramid-4 {
		.pyramid-img {
			width: 384.87px;
			height: 140.51px;
		}
		.select {
			width: 271px;
			background: linear-gradient(263deg, rgba(217, 217, 217, 0) 0%, rgba(39, 130, 54, 0.11) 122%);
			transform: translate(-50px, 20px);
			.select-line {
				width: 66px;
				background-color: #278236;
			}
			.text {
				color: #278236;
			}
		}
	}
}
</style>
