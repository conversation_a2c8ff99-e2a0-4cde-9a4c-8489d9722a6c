<template>
	<Card title="干部指数" :sub-title="title" header size-type="5">
		<template v-slot:right>
			<div class="data-menu">
				<template v-if="user.detail.promotion_type !== 4">
					<div
						:class="['menu-item', coor_menu_selected === item.key && 'menu-active']"
						v-for="item in menuComputed"
						:key="item.key"
						@click="onMenu(item.key)"
					>
						{{ item.label }}
					</div>
				</template>

				<div class="contrast-button data-btn">
					<a-button class="button-box" type="primary" :disabled="!data?.length" @click="onTableModal">查看全部</a-button>
				</div>
				<div class="contrast-button">
					<a-button class="button-box" type="primary" :disabled="!data?.length" @click="onModal">对比分析</a-button>
				</div>
			</div>
		</template>
		<div class="personal-analysis" ref="topBoxRef">
			<DataTable :data-source="data" :columns="columns" ref="table" row-id="user_id" body-scroll :rowClick="onTableRowClick">
				<template v-slot:rank="{ value }">
					<span v-if="value > 3">{{ value }}</span>
					<span v-else :class="`rank-icon rank-${value}`"></span>
				</template>
				<template v-slot:portrait="{ data }">
					<span class="custom-portrait" @click="onLocation(data)"></span>
				</template>
				<template v-slot:operation="{ data }">
					<div class="collection-btn" v-if="comparativeStore.getComparativeIndex(data.user_id) == -1" @click.stop="onAddCompare(data)">+ 对比</div>
					<div class="collection-btn gray-status" @click.stop="onAddCompare(data)" v-else>已加入</div>
				</template>
				<template v-slot:same_rank="{ value }">{{ value }} </template>
			</DataTable>
		</div>

		<!-- <Modal :visible="visible" @close="onModal" class="constast-modal">
			<div class="contrast-box">
				<Card title="人员设置" header class="chosse-box">
					<div class="choose">
						<div class="contrast-seek">
							<div class="seek-title">
								<div class="input-box"><span>姓名：</span><a-input type="text" v-model:value="search.name" /></div>
								<div class="input-box"><span>职务：</span><a-input type="text" v-model:value="search.current_position" /></div>
								<button @click="lookUp" class="look-up" border>查询</button>
								<button @click="onReset" class="reset">重置</button>
							</div>
							<div class="seek-table">
								<a-table
									:row-selection="{ selectedRowKeys: state.selectedRowKeys, onSelect: onSelect, hideSelectAll: true }"
									:columns="contrast_columns"
									:data-source="dataSource"
									:pagination="false"
									rowKey="user_id"
									bordered
									:style="{
										maxHeight: '551px',
										overflow: 'auto',
									}"
								/>
							</div>
						</div>
						<div class="already-have">
							<div class="have-nubmer">
								<span class="label">已选: </span>
								<span class="select-number">{{ quotaList.length }}/10</span>
							</div>
							<div class="have-name">
								<div v-for="(item, index) in quotaList" :key="index" class="select-box">
									<span>{{ item.name }}</span
									><span class="have-delete" @click.stop="deleteName(index)"></span>
								</div>
							</div>
						</div>
					</div>
				</Card>
				<Card title="基础信息" header class="veidoo-box">
					<div class="veidoo">
						<div class="veidoo-item">
							<div class="title">基础信息</div>
							<div class="data">
								<div class="check-item" v-for="(item, index) in basicMessage" :key="index">
									<a-checkbox v-model:checked="item.is_select">{{ item.name }}</a-checkbox>
								</div>
							</div>
						</div>
						<div class="veidoo-item">
							<div class="title">干部画像</div>
							<div class="data">
								<div class="check-item" v-for="(item, index) in cadreMessage" :key="index">
									<a-checkbox v-model:checked="item.is_select">{{ item.name }}</a-checkbox>
								</div>
							</div>
						</div>
					</div>
				</Card>
				<div class="start-analyse">
					<a-button class="analyse" @click.stop="skipTo">开始分析</a-button>
				</div>
			</div>
		</Modal> -->
		<!-- <Teleport to="body">
			<Modal :visible="visibleTable" @close="onTableModal" class="table-modal">
				<div class="data-modal">
					<DataTable :data-source="data" :columns="columns_modal" body-scroll ref="modalTable" row-id="user_id" :rowClick="onTableRowClick">
						<template #:rank="{ value }">
							<span v-if="value > 3">{{ value }}</span>
							<span v-else :class="`rank-icon rank-${value}`"></span>
						</template>
						<template #head_url="{ value }">
							<img :src="`${CDN_URL}/fr_img/${value}`" alt="" class="avatar" />
						</template>
						<template #:portrait="{ data }">
							<span class="custom-portrait" @click="onLocation(data, $event)"></span>
						</template>
					</DataTable>
				</div>
			</Modal>
		</Teleport> -->
		<a-drawer title="对比分析" class="comparative-drawer" placement="right" :closable="true" :visible="visible" @close="onModal">
			<div v-for="(item, index) in comparativeStore.comparative" :key="index" class="select-box">
				<span class="icon">{{ item.name }}</span>
				<div class="position">{{ item.current_position }}</div>
				<span class="have-delete" @click.stop="deleteName(index)"></span>
			</div>

			<template #footer>
				<div class="footer-box">
					<a-button style="margin-right: 8px" @click="skipTo">开始对比（{{ comparativeStore.comparative.length }}/10）</a-button>
					<a-button type="primary" @click="onClear">清空</a-button>
				</div>
			</template>
		</a-drawer>
	</Card>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, watch, toRefs, onMounted, computed, inject, shallowRef, h } from 'vue'
import Card from '@/components/Card.vue'
import DataTable from '@/components/Table.vue'
import Modal from '@/components/Modal.vue'
import { debounce, Base64, getQueryVariable } from '@/utils/utils'
import { historyPush } from '@/utils/history'
// 全局状态
import { useHomeStore } from '@/store/home'
import { useComparative } from '@/store/comparative'
import useUser from '@/store/user'
import { getDimensionConfig, getCadreIndex } from '@/apis/cadre-portrait/home'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
const CDN_URL = import.meta.env.VITE_CDN_URL

const urlParams = getQueryVariable()

const menu = [
	{
		label: '本单位',
		key: 1,
	},
	{
		label: '同序列',
		key: 2,
	},
	{
		label: '乡镇/部门',
		key: 3,
	},
	{
		label: '全县',
		key: 0,
	},
]
const contrast_columns = [
	{
		title: '姓名',
		dataIndex: 'name',
	},
	{
		title: '职务',
		dataIndex: 'current_position',
	},
]

export default defineComponent({
	components: {
		Card,
		Modal,
		DataTable,
	},
	setup() {
		const user_id = inject('user_id')
		const user = useUser()
		const store = useHomeStore()

		const comparativeStore = useComparative()

		const { coor_user_id, menu, coor_menu_selected } = toRefs(store)
		const table = ref<any>(null)
		// 控制弹窗县隐藏
		const visible = ref(false)
		// 控制对比抽屉
		const drawerVisible = ref(false)

		const visibleTable = ref(false)
		// const currentIndex = ref(1)
		const topBoxRef = ref<any>(null)
		const data = ref<any>([])
		const router = useRouter()
		const basicMessage = ref<any>([])
		const cadreMessage = ref<any>([])
		const quotaList = ref<any>([])
		const term = ref<any>(false) //查询条件
		const modalTable = ref<any>(null)
		const search = ref<any>({
			name: '',
			current_position: '',
		}) //搜索对象
		const dataSource = shallowRef<any>([]) // 窗口数据
		const state = reactive<any>({
			selectedRowKeys: [],
		}) // 选中的key
		const blur = ref(false)
		const columns = [
			{
				key: 'rank',
				align: 'center',
				width: '6%',
				title: '排名',
			},
			{
				key: 'name',
				align: 'center',
				width: '9%',
				title: '姓名',
				// colClass: blur.value ? 'filter-style' : '',
				colClass: 'cursor-pointer',
				colClick: (data: any, event: any) => {
					event.stopPropagation()

					onLocation(data)
				},
			},
			// {
			// 	key: 'portrait',
			// 	align: 'center',
			// 	width: '9%',
			// 	title: '干部画像',
			// 	// colClass: blur.value ? 'filter-style' : '',
			// },
			{
				key: 'current_position',
				align: 'left',
				width: '25%',
				title: '职务',
				// colClass: blur.value ? 'filter-style' : '',
			},
			{
				key: 'birthday',
				align: 'center',
				width: '12%',
				title: '出生年月',
				// colClass: blur.value ? 'filter-style' : '',
			},
			{
				key: 'school',
				align: 'left',
				width: '18%',
				title: '毕业院校及专业',
				// colClass: blur.value ? 'filter-style' : '',
			},
			// {
			// 	key: 'specialty',
			// 	align: 'center',
			// 	width: '12%',
			// 	title: '专业',
			// 	// colClass: blur.value ? 'filter-style' : '',
			// },
			{
				key: 'cadre_index',
				align: 'center',
				width: '12%',
				title: '干部指数',
			},
			{
				key: 'same_rank',
				dataIndex: 'same_rank',
				align: 'center',
				width: '10%',
				title: '干部指数序列排名',
			},
			{
				key: 'operation',
				align: 'center',
				width: '12%',
				title: '操作',
			},
			// {
			// 	key: 'achievement',
			// 	align: 'center',
			// 	width: '8%',
			// 	title: '业绩',
			// },
			// {
			// 	key: 'ability',
			// 	align: 'center',
			// 	width: '8%',
			// 	title: '能力',
			// },
			// {
			// 	key: 'praise',
			// 	align: 'center',
			// 	width: '8%',
			// 	title: '口碑',
			// },
			// {
			// 	key: 'politics',
			// 	align: 'center',
			// 	width: '8%',
			// 	title: '政治',
			// 	customization: 'CustomBlock',
			// },
		]
		const columns_modal = [
			{
				key: 'rank',
				align: 'center',
				width: '6%',
				title: '排名',
			},

			{
				key: 'name',
				align: 'center',
				width: '10%',
				title: '姓名',
				// colClass: blur.value ? 'filter-style' : '',
				colClass: 'cursor-pointer',
				colClick: (data: any, event: any) => {
					event.stopPropagation()

					onLocation(data)
				},
			},
			{
				key: 'head_url',
				align: 'center',
				width: '10%',
				title: '头像',
			},
			// {
			// 	key: 'portrait',
			// 	align: 'center',
			// 	width: '9%',
			// 	title: '干部画像',
			// 	// colClass: blur.value ? 'filter-style' : '',
			// },
			{
				key: 'current_position',
				align: 'left',
				width: '25%',
				title: '职务',
				// colClass: blur.value ? 'filter-style' : '',
			},
			{
				key: 'birthday',
				align: 'center',
				width: '12%',
				title: '出生年月',
				// colClass: blur.value ? 'filter-style' : '',
			},
			{
				key: 'school',
				align: 'left',
				width: '18%',
				title: '毕业院校及专业',
				// colClass: blur.value ? 'filter-style' : '',
			},
			// {
			// 	key: 'specialty',
			// 	align: 'center',
			// 	width: '12%',
			// 	title: '专业',
			// 	// colClass: blur.value ? 'filter-style' : '',
			// },
			{
				key: 'cadre_index',
				align: 'center',
				width: '12%',
				title: '干部指数',
			},
			// {
			// 	key: 'achievement',
			// 	align: 'center',
			// 	width: '8%',
			// 	title: '业绩',
			// },
			// {
			// 	key: 'ability',
			// 	align: 'center',
			// 	width: '8%',
			// 	title: '能力',
			// },
			// {
			// 	key: 'praise',
			// 	align: 'center',
			// 	width: '8%',
			// 	title: '口碑',
			// },
			// {
			// 	key: 'politics',
			// 	align: 'center',
			// 	width: '8%',
			// 	title: '政治',
			// 	customization: 'CustomBlock',
			// },
		]
		// watchEffect(() => {
		// 	if (table.value && topBoxRef.value) {
		// 		// setInterval(() => {
		// 		const height = table.value.getElementTopById(95)
		// 		topBoxRef.value.scrollTo(0, height)
		// 		// }, 1000)
		// 	}
		// })
		// let preId: any = null
		// 在表格内点击行不触发滚动
		let emitScroll = true
		watch(coor_user_id, (newVal, oldVal) => {
			if (!emitScroll) {
				emitScroll = true
			} else if (oldVal !== newVal) {
				table.value?.scrollToElementById(newVal)
			}
			table.value?.rowHighLight(newVal)
			modalTable.value?.rowHighLight(newVal)
		})

		// onMounted(() => {
		// 	const { user_id } = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
		// 	table.value?.scrollToElementById(user_id)
		// })

		const loadData = async () => {
			if (!user_id) return

			const { promotion_type } = user.detail

			const flag = promotion_type === 4 ? 4 : coor_menu_selected.value

			const res = await getCadreIndex({ user_id, flag })
			if (res.code === 0) {
				data.value = res.data.index_ranks
				if (coor_menu_selected.value !== 1) {
					blur.value = true
				} else {
					blur.value = false
				}
			}
		}

		const title = computed(() => {
			return `${user.detail.promotion_type === 4 ? '（新提任）' : ''}`
		})

		// 监听currentIndex变化 更新图表数据
		watch(
			coor_menu_selected,
			debounce(() => {
				store.updateCoorUserId('-1')
				loadData()
			}, 500),
			{
				immediate: true,
			}
		)
		const colorList = ['#15EAC2', '#24c5e5', '#FDA22C']

		const rowColor = (_record: any, index: number) => {
			return colorList[index]
		}
		// 点击menu菜单
		const onMenu = (key: number) => {
			// currentIndex.value = key
			store.updateCoorMenuSelected(key)
		}
		const onClose = () => {
			drawerVisible.value = false
		}
		// 弹窗
		const onModal = () => {
			// router.push({
			// 	path: '/comparison',
			// })
			// sessionStorage.setItem('comparisonUser', JSON.stringify(data.value))
			// window.open('/comparison')
			// return
			visible.value = !visible.value
		}
		const onTableModal = () => {
			// visibleTable.value = !visibleTable.value
			// if (!visibleTable.value && modalTable.value) {
			// 	modalTable.value.scrollToElementById(-1)
			// }

			sessionStorage.setItem('cadre_data', JSON.stringify(data.value))
			historyPush('/cadre-index')
		}
		// 对比设置中搜索功能

		//TODO
		// const rowSelection: TableProps['rowSelection'] = {
		// 	onChange: (selectedRowKeys: any, selectedRows: any) => {
		// 		quotaList.value = [...quotaList.value, ...selectedRows]
		// 		state.selectedRowKeys = selectedRows
		// 	},
		// }
		//
		const unique = (arr: any) => {
			let result: any[] = []
			for (let i = 0; i < arr.length; i++) {
				if (!result.includes(arr[i])) {
					result.push(arr[i])
				}
			}
			return result
		}
		let _selectedRowKeys: any = []

		const onSelect = (record: any, selected: any, selectedRows: any) => {
			// if (searchStatus) {
			const index = _selectedRowKeys.findIndex((item: any) => {
				return item === record.user_id
			})
			if (index == -1 && _selectedRowKeys.length < 10) {
				_selectedRowKeys.push(record.user_id)
				quotaList.value.push(record)
			} else {
				if (index == -1) {
					return void 0
				}
				_selectedRowKeys.splice(index, 1)
				quotaList.value.splice(index, 1)
			}
			state.selectedRowKeys = [..._selectedRowKeys]

			// } else {
			// 	if (selectedRowKeys.length < _selectedRowKeys.length) {
			// 		state.selectedRowKeys = selectedRowKeys
			// 	} else {
			// 		// 增加
			// 		state.selectedRowKeys = selectedRowKeys
			// 		_selectedRowKeys = selectedRowKeys
			// 	}
			// }
		}

		const onAddCompare = (data: any) => {
			comparativeStore.updateComparative(data)
		}

		onMounted(() => {
			getRadio()
		})
		const config_ids = ref([])
		const getRadio = async () => {
			const params = {}
			const { data, code } = await getDimensionConfig(params)
			if (code == 0) {
				const ids: any = []
				data.map((item: any) => {
					if (item.name == '基础信息') {
						item.children.forEach((item: any) => {
							ids.push(item.config_id)
						})
					} else {
						item.children.forEach((item: any) => {
							ids.push(item.config_id)
						})
					}
				})

				config_ids.value = ids
			}
		}

		const judgeStatus = (item: any) => {
			if (item.is_select == 1) {
				return true
			} else {
				return false
			}
		}

		const lookUp = () => {
			term.value = true
			let list: any[] = []
			data.value.map((item: { name: any; current_position: any }) => {
				if (search.value.name == item.name || search.value.current_position == item.current_position) {
					list.push(item)
				} else if (search.value.name == '' && search.value.current_position == '') {
					list = data.value
				}
				dataSource.value = list
			})
		}

		const deleteName = (index: number) => {
			comparativeStore.comparative.splice(index, 1)
		}

		const basicsChange = (item: any) => {
			console.log(item, '基础')
		}

		const cadreChange = (item: any) => {
			console.log(item, '干部')
		}

		const skipTo = () => {
			if (comparativeStore.comparative.length < 2) {
				message.error('最少选择2人')
				return
			}
			let user_id: any[] = []

			// let config_ids: any[] = []
			comparativeStore.comparative.forEach((element: any) => {
				user_id.push(element.user_id)
			})

			console.log(comparativeStore.config_ids.join(','))
			const path = `/comparison-results?user_id=${encodeURIComponent(user_id.join(','))}&config_ids=${encodeURIComponent(
				comparativeStore.config_ids.join(',')
			)}`

			historyPush({
				path: `/comparison-results`,
				query: {
					user_id: user_id.join(','),
					config_ids: comparativeStore.config_ids.join(','),
				},
			})

			// router.push({
			// 	path: '/comparison-results',
			// 	query: {
			// 		user_id: user_id.join(','),
			// 		config_ids: config_ids.join(','),
			// 	},
			// })
		}

		const onTableRowClick = (data: any) => {
			// if (blur.value) return
			emitScroll = false
			store.updateCoorUserId(data.user_id)
		}
		const onLocation = (data: any, event?: any) => {
			const userInfo = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
			userInfo.user_id = data.user_id
			const user_info = Base64.encode(JSON.stringify(userInfo))
			historyPush({
				path: `/cadre-portrait/home`,
				query: {
					...urlParams,
					user_id: data.user_id,
				},
			})
		}
		const onReset = () => {
			dataSource.value = data.value
			search.value = { name: '', current_position: '' }
		}

		const onClear = () => {
			comparativeStore.updateComparative([])
		}

		const menuComputed = computed(() => {
			const type = user.detail.promotion_type
			if (type === 2) {
				return store.menuFilterSame
			} else if (type === 3) {
				return store.menuFilterSpecial
			} else {
				return store.menuList
			}
		})

		return {
			title,
			user,
			store,
			comparativeStore,
			table,
			columns,
			columns_modal,
			rowColor,
			CDN_URL,
			data,
			topBoxRef,
			coor_menu_selected,
			onMenu,
			onModal,
			menu,
			visible,
			visibleTable,
			// TODO
			state,
			search,
			dataSource,
			contrast_columns,
			// rowSelection,
			basicMessage,
			cadreMessage,
			quotaList,
			modalTable,
			menuComputed,
			basicsChange,
			cadreChange,
			judgeStatus,
			lookUp,
			onReset,
			deleteName,
			skipTo,
			onClose,
			onTableRowClick,
			onSelect,
			onLocation,
			onTableModal,
			onAddCompare,
			onClear,
		}
	},
})
</script>

<style scoped lang="less">
.personal-analysis {
	display: flex;
	flex-direction: column;
	// margin-top: 16px;
	overflow: auto;
	padding: 0px 0px;
	max-height: 414.5px;
	// 隐藏滚动条
	&::-webkit-scrollbar {
		display: none;
	}

	.box-content__line {
		height: 414.5px;
	}

	.box-content__table {
		flex: 1;
	}
	.collection-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0px auto;
		width: 72px;
		height: 32px;
		background: #ffffff;
		border-radius: 4px 4px 4px 4px;
		border: 1px solid #e5251b;

		font-weight: 400;
		font-size: 18px;
		color: #e5251b;
		line-height: 21px;
	}
	.gray-status {
		border: 1px solid rgba(0, 0, 0, 0.25);
		color: #999999;
	}
}

.data-menu {
	// padding-right: 21px;
	display: flex;
	align-items: center;

	.menu-item {
		margin-left: 12px;
		font-size: 18px;
		font-family: PingFang SC-Medium, PingFang SC;
		font-weight: 500;
		color: #666666;
		// font-size: 14px;
		// line-height: 28px;
		// font-weight: 400;
		// color: #9e9e9e;
		cursor: pointer;
		text-align: center;
	}

	.menu-active {
		color: #e5231a;
		position: relative;
		font-weight: bold;
	}
	.menu-active::after {
		content: '';
		display: inline-block;
		// width: 40px;
		width: 70%;
		height: 2px;
		background: #ff2121;
		border-radius: 2px 2px 2px 2px;
		opacity: 1;
		position: absolute;
		bottom: -5px;
		left: 50%;
		transform: translateX(-50%);
	}

	.contrast-button {
		margin-left: 17px;

		.button-box {
			padding: 0px 11px;
			text-align: center;
			font-size: 14px;
			font-family: Source Han Sans CN;
			font-weight: 400;
			border-radius: 4px;
			line-height: 19px;
			cursor: pointer;
			border: none;
		}
		button:not([disabled]) {
			color: #ffffff;
			background: #e5251b;
		}
	}
	.data-btn {
		margin-left: 37px;
	}
}

.contrast-box {
	width: 1200px;
	min-height: 100%;
	// overflow-y: auto;
	// height: 1215px;
	// border: 1px solid #23dbfc;
	::v-deep(.card-box) {
		padding: 0;
		.title {
			font-size: 20px;
		}
	}
	.chosse-box {
		padding: 24px;
		background-color: #ffffff;
		.choose {
			margin-top: 24px;
			display: flex;
			justify-content: space-between;
		}
		.contrast-seek {
			width: 706px;
			display: flex;
			flex-direction: column;
			margin-right: 12px;

			.seek-table {
				height: 500px;
				::v-deep(.ant-table-thead) {
					.ant-table-cell {
						background-color: #f3f3f3;
					}
				}
				::v-deep(.ant-table-tbody) {
					.ant-table-cell {
						font-size: 16px;
					}
				}
			}

			.seek-title {
				padding: 21px 20px;
				display: flex;
				align-items: center;
				margin-bottom: 17px;
				width: 100%;
				height: 76px;
				background: #f7f8fa;
				border-radius: 4px 4px 4px 4px;
				.input-box {
					display: flex;
					align-items: center;
					height: 40px;
					span {
						font-size: 16px;
						font-family: Source Han Sans CN;
						font-weight: 400;
						color: #222222;
						white-space: nowrap;
					}
					input {
						width: 160px;
					}
				}
				.look-up {
					width: 80px;
					height: 36px;
					background: #e5231a;
					border-radius: 4px 4px 4px 4px;
					opacity: 1;
					font-size: 16px;
					line-height: 16px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #ffffff;
					outline: none;
				}
				.reset {
					width: 80px;
					height: 36px;
					background: #ededed;
					border-radius: 4px 4px 4px 4px;
					font-size: 16px;
					line-height: 16px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #666666;
					opacity: 1;
					outline: none;
				}
				button {
					margin-right: 10px;
					width: 80px;
					height: 36px;
					font-size: 16px;
					font-weight: 400;
				}
				span {
					margin-right: 11px;
				}

				div {
					margin-right: 24px;
				}
				// button {
				// 	outline: none;
				// }
			}

			// input {
			// 	background: #fff;
			// 	border: 1px solid #00d2ff;
			// 	border-radius: 2px;
			// 	width: 120px;
			// 	height: 24px;
			// 	color: #fff;
			// }

			// button {
			// 	width: 62px;
			// 	height: 24px;
			// 	border: 1px solid #00d2ff;
			// 	border-radius: 4px;
			// 	padding: 0px;
			// 	font-size: 14px;
			// 	font-family: Source Han Sans CN;
			// 	font-weight: 400;
			// 	color: #00d2ff;
			// }
		}

		.already-have {
			width: 408px;
			display: flex;
			flex-direction: column;

			.have-nubmer {
				display: flex;
				align-items: center;
				padding: 28px 20px;
				width: 408px;
				height: 76px;
				background: #f7f8fa;
				border-radius: 4px 4px 4px 4px;
				opacity: 1;
				font-size: 14px;
				font-weight: 400;
				color: #00d2ff;
				.label {
					font-size: 16px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #222222;
					line-height: 24px;
				}
				.select-number {
					margin-left: 10px;
					font-size: 16px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #2462ff;
				}
			}

			.have-name {
				margin-top: 20px;
				padding: 20px;
				height: 551px;
				background: #f7f8fa;
				border-radius: 4px 4px 4px 4px;
				opacity: 1;
				.select-box {
					display: flex;
					justify-content: space-between;
				}
				div {
					margin-bottom: 33px;
					cursor: pointer;
				}

				span {
					font-size: 16px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #222222;
				}

				.have-delete {
					width: 24px;
					height: 24px;
					display: inline-block;
					background-image: url('@/assets/images/delete.png');
					background-size: 100% 100%;
					margin-left: 15px;
					vertical-align: middle;
				}
			}
		}
	}

	.veidoo-box {
		margin-top: 24px;
		padding: 24px;
		width: 100%;
		background: #ffffff;
		.veidoo {
			margin-top: 24px;
			width: 100%;
			.top-content {
				display: flex;
				margin-top: 49px;
				margin-bottom: 31px;

				.label {
					display: flex;
					align-items: center;
					font-size: 18px;
					font-weight: bold;
					color: #00fff6;
					line-height: 18px;
					text-shadow: 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2), 0 0 4px rgba(35, 219, 252, 0.2),
						0 0 4px rgba(35, 219, 252, 0.2);
					background: linear-gradient(0deg, #3bdeff 6.1279296875%, #d1fbff 55.4443359375%, #ddf9ff 100%);
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;

					&::after {
						margin-left: 7px;
						content: '';
						display: inline-block;
						width: 24px;
						height: 16px;
						background: url('@/assets/images/label-icon.png') no-repeat center / cover;
					}
				}
			}
			.veidoo-item {
				display: flex;
				border: 1px solid #ebebeb;
				.title {
					display: flex;
					align-items: center;
					justify-content: center;

					width: 100px;
					background: #f3f3f3;
					border-radius: 0px 0px 0px 0px;
					opacity: 1;

					font-size: 16px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #222222;
					line-height: 24px;
				}

				.data {
					flex: 1;
					padding: 28px 44px;

					display: flex;
					flex-wrap: wrap;
					.check-item {
						width: 25%;
						white-space: nowrap;
						::v-deep(.ant-checkbox-wrapper) {
							font-size: 16px;
							font-family: Source Han Sans CN-Regular, Source Han Sans CN;
							font-weight: 400;
							color: #222222;
						}
					}
				}

				.two-title {
					display: flex;
					width: 169px;
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 68px 0;
				}

				div {
					font-size: 14px;
					font-family: Source Han Sans CN;
					font-weight: 500;
					color: #00d2ff;
				}
			}
		}
	}
	::v-deep(.ant-checkbox-checked) {
		.ant-checkbox-inner {
			background-color: #0a58f6;
			border-color: #0a58f6;
		}
	}
	.start-analyse {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 16px;
		width: 1200px;
		height: 88px;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		button {
			width: 160px;
			height: 48px;
			background: #e5231a;
			border-radius: 4px 4px 4px 4px;
			opacity: 1;
			font-size: 16px;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: #ffffff;
			outline: none;
			border: 0;
			&:active {
				opacity: 0.7;
			}
		}
	}
}

// .start-analyse {
// 	display: flex;
// 	justify-content: center;
// 	margin: 51px 0 36px 0;

// 	.analyse {
// 		background: rgba(21, 25, 105, 0.4);
// 		border: 1px solid #00fff6;
// 		box-shadow: inset 0 0 25px rgba(0, 255, 246, 0.4);
// 		border-radius: 4px;
// 		font-size: 16px;
// 		font-family: Source Han Sans CN;
// 		font-weight: 400;
// 		color: #ffffff;
// 		height: unset;
// 		padding: 10px 53px;
// 	}
// }

// ::v-deep(.ant-table-thead th) {
// 	background: #1a1e72;
// 	font-size: 14px;
// 	font-family: Source Han Sans CN;
// 	font-weight: 400;
// 	color: #00d2ff;
// 	border-bottom: 1px solid #333aa2 !important;
// }

::v-deep(.ant-table-thead .ant-table-selection) {
	display: none !important;
}

// ::v-deep(.ant-table) {
// 	max-height: 551px;
// 	// height: 100%;
// 	height: 551px;
// 	background: #1a1e72;
// 	max-height: 551px;
// }

::v-deep(.ant-table::-webkit-scrollbar) {
	display: none;
}

// ::v-deep(.ant-table-tbody td) {
// 	background: #1a1e72 !important;
// 	border-bottom: 1px solid #333aa2 !important;
// 	border: 0px;
// 	font-size: 14px;
// 	font-family: Source Han Sans CN;
// 	font-weight: 400;
// 	color: #00d2ff;
// }

// ::v-deep(.ant-checkbox-wrapper span) {
// 	font-size: 14px;
// 	font-family: Source Han Sans CN;
// 	font-weight: 400;
// 	color: #00d2ff;
// }

::v-deep(.ant-checkbox-wrapper .ant-checkbox-inner) {
	// width: 10px !important;
	// height: 10px !important;
	// top: -2px;
}

::v-deep(.ant-checkbox-checked .ant-checkbox-inner::after) {
	// left: 0;
}

::v-deep(.ant-checkbox-wrapper) {
	width: 25%;
	margin-left: 0;
	margin-bottom: 14px;
	position: relative;
}

::v-deep(.ant-table-tbody .ant-checkbox-wrapper) {
	position: relative;
}

::v-deep(.ant-table-tbody .ant-checkbox) {
	position: absolute;
	top: 10px;
}

::v-deep(.filter-style) {
	position: relative;
	user-select: none;
	filter: blur(7px);
	&::after {
		content: '';
		position: absolute;
		inset: 0;
	}
}
::v-deep(.table) {
	user-select: none;
}
::v-deep(.modal-content) {
	height: 90%;
	overflow-y: auto;
	// 隐藏滚动条
	::-webkit-scrollbar {
		display: none;
	}
}

.rank-icon {
	display: inline-block;
	width: 28px;
	height: 28px;
	vertical-align: middle;
}
.rank-1 {
	background: url('@/assets/images/rank-1.png') no-repeat center / contain;
}
.rank-2 {
	background: url('@/assets/images/rank-2.png') no-repeat center / contain;
}
.rank-3 {
	background: url('@/assets/images/rank-3.png') no-repeat center / contain;
}
.custom-portrait {
	display: inline-block;
	width: 27.43px;
	height: 27.43px;
	background: url('@/assets/images/portrait.png') no-repeat center center;
	background-size: 100%, 100%;
	cursor: pointer;
	font-size: 0px;
	vertical-align: middle;
}
::v-deep(.cursor-pointer) {
	cursor: pointer;
}
.table-modal {
	height: 100%;
	::v-deep(.modal-content) {
		// height: auto;
		// min-height: 50%;
		// max-height: 90%;
		background-color: #ffffff;
	}
	.data-modal {
		height: 100%;
		width: 1800px;
		.avatar {
			width: 80px;
			height: 100px;
			object-fit: contain;
		}
	}
}
.footer-box {
	height: 155px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
	button {
		width: 540px;
		height: 62px;
		&:nth-child(1) {
			background-color: #e5251b;
			color: #ffffff;
			font-size: 26px;
		}
		&:nth-child(2) {
			background: #fff2f1;
			color: #e5251b;
			font-size: 26px;
			border: none;
		}
	}
}
</style>
<style lang="less">
.comparative-drawer {
	.ant-drawer-content-wrapper {
		width: 603px !important;
		.ant-drawer-title {
			font-weight: 500;
			font-size: 24px;
			color: #000000;
		}
		.select-box {
			padding: 18px 0px 24px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1px solid rgba(0, 0, 0, 0.08);

			.icon {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 20px;
				color: rgba(0, 0, 0, 0.9);
				line-height: 23px;
				width: 100px;
				&::before {
					display: inline-block;
					margin-right: 12px;
					content: '';
					width: 11px;
					height: 11px;
					border-radius: 50%;
					background: #e5251b;
				}
			}
			.position {
				flex: 1;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 18px;
				color: rgba(0, 0, 0, 0.65);
				line-height: 21px;
			}
			.have-delete {
				width: 20px;
				height: 20px;
				display: inline-block;
				background-image: url('@/assets/images/delete-2.png');
				background-size: 100% 100%;
				margin-left: 15px;
				vertical-align: middle;
				cursor: pointer;
			}
		}
	}
}
</style>
