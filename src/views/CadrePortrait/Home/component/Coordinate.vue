<template>
	<Card title="坐标分布" :sub-title="title" header size-type="2" class="first-coord">
		<template v-slot:right>
			<div class="data-menu" v-if="user.detail.promotion_type !== 4">
				<div
					:class="['menu-item', coor_menu_selected === item.key && 'menu-active']"
					v-for="item in menuComputed"
					:key="item.key"
					@click="onMenu(item.key)"
				>
					{{ item.label }}
				</div>
			</div>
		</template>
		<div class="echarts-box">
			<div class="echarts">
				<v-chart :option="option" autoresize ref="echartsRef"></v-chart>
			</div>
			<div class="type-selection">
				<a-select :bordered="false" :dropdownMatchSelectWidth="false" v-model:value="selectValue">
					<a-select-option v-for="item in selectOption" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
					<template #suffixIcon><span class="select-icon"></span></template>
				</a-select>
			</div>
		</div>
	</Card>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, toRefs, inject, shallowRef } from 'vue'
import { getPointData } from '@/apis/cadre-portrait/home'
import useUser from '@/store/user'
import { useHomeStore } from '@/store/home'
import { debounce, convertPxToRem } from '@/utils/utils'
// 0-全县, 1-本单位, 2-同序列, 3-全部乡镇/部门
const menu = [
	{
		label: '本单位',
		key: 1,
	},
	{
		label: '同序列',
		key: 2,
	},
	{
		label: '乡镇/部门',
		key: 3,
	},
	{
		label: '全县',
		key: 0,
	},
]
const selectOption = [
	{
		label: '能力+口碑',
		value: '1',
	},
	{
		label: '能力',
		value: '2',
	},
	{
		label: '口碑',
		value: '3',
	},
]
// const self = [91, 28.8]
export default defineComponent({
	name: 'Coordinate',
	setup() {
		const user_id = inject('user_id')
		const user = useUser()
		const echartsRef = ref<any>(null)
		const store = useHomeStore()
		const { menu, coor_menu_selected, coor_user_id } = toRefs(store)
		// const currentIndex = ref(1)
		const selectValue = ref('1')

		const data = shallowRef<any>({
			mine: {
				x: 0,
				y: 0,
				user_id: 0,
				name: '',
			},
			others: [],
			x_avg: 0,
			y_avg: 0,
			type: undefined,
		})

		const title = computed(() => {
			return `${data.value.type === 1 ? '（县管正职）' : '（县管副职）'}${user.detail.promotion_type === 4 ? '(新提任)' : ''}`
		})
		const dictMap: any = {
			'1': 'x',
			'2': 'x1',
			'3': 'x2',
		}
		const dictAvgMap: any = {
			'1': 'x_avg',
			'2': 'x1_avg',
			'3': 'x2_avg',
		}
		const dictLabelMap: any = {
			'1': '能力+口碑',
			'2': '能力',
			'3': '口碑',
		}
		const refreshOption = ref()
		window.addEventListener(
			'resize',
			debounce(() => {
				refreshOption.value = Math.random()
			}, 100)
		)
		const option = computed(() => {
			{
				refreshOption.value
			}
			let min = 0
			let max = 0
			let keyName: any = dictMap[selectValue.value]
			const { mine, others } = data.value
			if (!mine) return {}

			const selfData: any = [mine[keyName], mine.y, mine.name, mine.user_id]
			const x_avg = data.value[dictAvgMap[selectValue.value]]

			// 处理数据
			let otherData: any = []
			otherData = otherData.concat(others, [mine]).map((item: any) => {
				let x = Number(item[dictMap[selectValue.value]])
				if (min === 0) {
					min = x
				}
				if (max === 0) {
					max = x
				}
				if (x < min) {
					min = Number(x)
				}
				if (x > max) {
					max = x
				}
				return [x, item.y, item.name, item.user_id]
			})
			min && (min = Number(min - 1))
			min <= 0 && (min = 0)
			max && (max = max + 1)
			max >= 100 && (max = 100)

			const _option = {
				// color: ['#'],
				grid: {
					top: '9%',
					left: '4%',
					right: '5%',
					bottom: '2%',
					containLabel: true,
					show: false,
				},
				tooltip: {
					trigger: 'item',
					showDelay: 0,
					show: true,
					formatter: function (name: any) {
						const { value } = name
						return `
							<div class="tooltips-box">
								<div class="data-name">${value[2]}</div>
								<div class="data-item">业绩：${value[1]}</div>
								<div class="data-item">${dictLabelMap[selectValue.value]}：${value[0]}</div>
							</div>
						`
					},
				},
				xAxis: {
					// 名称配置为函数
					// name: '能力/口碑',
					type: 'value',
					// scale: true,
					// interval: 0,
					axisLabel: {
						formatter: (value: any) => {
							value = Number(value)
							return value > 100 ? '' : value.toFixed(2)
						},
						color: '#666666',
						fontSize: convertPxToRem(16),
					},
					// boundaryGap: ['10%', '10%'],
					splitLine: {
						//x轴网格线
						show: true,
						lineStyle: {
							color: '#EEEEEE',
							type: 'dotted',
						},
					},
					axisLine: {
						show: true,
						lineStyle: {
							color: '#666666',
						},
						symbol: [
							'none',
							// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
							'arrow',
						],
						symbolOffset: 7,
						symbolSize: [7, 10],
					},
					axisTick: {
						show: false,
					},
					nameTextStyle: {
						color: '#000000',
						// align: 'right',
						padding: [0, 0, 0, convertPxToRem(-50)],
					},
					min: (value: any) => (value.min === 0 ? 0 : value.min - 0.01),
					max: (value: any) => value.max + 0.01,
					// max,
					// splitNumber: 7,
				},
				yAxis: {
					name: '业绩',
					type: 'value',
					scale: true,
					axisLabel: {
						color: '#666666',
						formatter: (value: any) => {
							return Number(value).toFixed(2)
						},
						fontSize: convertPxToRem(16),
						fontFamily: 'PingFang SC',
						showMaxLabel: false,
					},
					splitLine: {
						lineStyle: {
							type: 'dashed',
							color: '#EEEEEE',
						},
					},
					axisLine: {
						show: true,
						lineStyle: {
							color: '#666666',
						},
						symbol: [
							'none',
							// 'path://M533.333333 465.066667L358.4 640 298.666667 580.266667l234.666666-234.666667 234.666667 234.666667-64 59.733333-170.666667-174.933333z',
							'arrow',
						],
						symbolOffset: 7,
						symbolSize: [7, 10],
					},
					// min: 20,
					min: (value: any) => (value.min === 0 ? 0 : value.min - 0.1),
					max: (value: any) => value.max + 0.1,
					// splitNumber: 7,
					// 刻度线
					axisTick: {
						show: false,
					},
					nameTextStyle: {
						color: '#000000',
						// align: 'left',
						padding: [0, 0, 0, -50],
						verticalAlign: 'top',
						fontSize: convertPxToRem(18),
					},
				},
				series: [
					{
						name: '业绩-能力',
						type: 'scatter',
						data: otherData,
						symbol: (value: Array<number>) => {
							if (value[0] === selfData[0] && value[1] === selfData[1]) {
								return 'none'
							} else {
								return 'circle'
							}
						},
						itemStyle: {
							color: (value: any) => {
								const [_x, _y, _name, _user_id] = value.data

								if (_user_id === coor_user_id.value) {
									return '#FE533A'
								} else {
									return '#60CA71'
								}
								return '#17EBC7'
							},
						},
						markLine: {
							symbol: 'none',
							label: { show: false },
							lineStyle: {
								//x/y轴平均值线
								color: '#FE533A',
								type: 'dashed',
							},
							silent: true,
							data: [
								{
									type: 'average',
									name: '平均值',
								},
								{
									xAxis: x_avg,
								},
							],
						},
					},
					{
						name: '业绩-能力',
						type: 'scatter',
						// 分别为原点，自己，x轴
						data: selfData[2] ? [selfData] : [],
						// symbol: (value: Array<number>) => {
						// 	if (value[0] === 0 && value[1] === 0) {
						// 		return 'none'
						// 	} else {
						// 		return 'circle'
						// 	}
						// },
						itemStyle: {
							color: (value: any) => {
								const [_x, _y, _name, _user_id] = value.data
								if (coor_user_id.value == '-1' || coor_user_id.value === selfData[3]) {
									return '#FE533A'
								} else {
									return '#60CA71'
								}
								return '#FFBA55'
							},
						},
					},
				],
			}
			coor_user_id.value
			return _option
		})
		const onMenu = (key: number) => {
			// currentIndex.value = key
			store.updateCoorMenuSelected(key)
		}
		/**
		 * @description: 加载数据
		 * @return {*}
		 */
		const loadData = async () => {
			if (!user_id) return

			const { org_id, _oid } = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
			// 0-全县, 1-本单位, 2-同序列, 3-全部乡镇/部门 4 新提拔
			const { promotion_type } = user.detail

			const flag = promotion_type === 4 ? 4 : coor_menu_selected.value
			const res = await getPointData({ user_id, flag, org_id: org_id || _oid })
			if (res.code === 0 && res.data) {
				data.value = res.data
			}
		}
		const menuComputed = computed(() => {
			const type = user.detail.promotion_type
			if (type === 2) {
				return store.menuFilterSame
			} else if (type === 3) {
				return store.menuFilterSpecial
			} else {
				return store.menu
			}
		})
		// loadData()
		// 监听currentIndex变化 更新图表数据
		watch(
			coor_menu_selected,
			debounce(() => {
				loadData()
			}, 500),
			{
				immediate: true,
			}
		)
		// echarts 绑定点击事件
		watch(echartsRef, () => {
			echartsRef.value.chart.on('click', (params: any) => {
				store.updateCoorUserId(params.data?.[3])
			})
		})
		return {
			user,
			menu,
			title,
			option,
			echartsRef,
			selectValue,
			selectOption,
			coor_menu_selected,
			menuComputed,
			onMenu,
		}
	},
})
</script>

<style scoped lang="less">
// .first-coord {
// 	padding-bottom: 45px;
// 	position: relative;
// 	.type-selection {
// 		position: absolute;
// 		bottom: 0px;
// 		right: 0px;
// 	}
// }
.data-menu {
	display: flex;
	align-items: center;
	.menu-item {
		margin-left: 12px;
		font-size: 18px;
		line-height: 18px;
		font-weight: 500;
		color: #666666;
		// line-height: 28px;
		cursor: pointer;
		line-height: 28px;
	}
	.menu-active {
		color: #333333;
		position: relative;
		font-weight: bold;
	}
	.menu-active::after {
		content: '';
		display: inline-block;
		// width: 40px;
		width: 70%;
		height: 2px;
		background: #ff2121;
		border-radius: 2px 2px 2px 2px;
		position: absolute;
		bottom: -5px;
		left: 50%;
		transform: translateX(-50%);
	}
}
.echarts-box {
	.echarts {
		width: 100%;
		height: 385px;
	}
}
::v-deep(.ant-select-arrow) {
	width: 16px;
	height: 16px;
}
.select-icon {
	margin-left: 4px;
	display: inline-block;
	width: 16px;
	height: 16px;
	background: url('@/assets/images/select.png') no-repeat center / 100%;
}
::v-deep(.ant-select-selection-item) {
	min-width: 130px;
	color: #000000;
	font-size: 20px;
	text-align: right;
}
.pupop-custom {
	width: 200px;
}
.type-selection {
	padding-right: 20px;
	// margin-bottom: 20px;
	display: flex;
	justify-content: flex-end;
}
</style>
