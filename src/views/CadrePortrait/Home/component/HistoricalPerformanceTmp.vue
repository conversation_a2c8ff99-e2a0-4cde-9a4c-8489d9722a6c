<template>
	<Card title="业绩分析" header size-type="3">
		<div class="histoical-performance">
			<!-- <div class="box-content__table">
				<div class="title">历史业绩</div>
			</div> -->
			<div class="box-content__line margin-top-30">
				<v-chart :option="option" autoresize></v-chart>
			</div>
			<!-- <div class="box-content__table">
				<DataTable :data-source="performanceTable.datasource" :columns="performanceTable.columns" />
			</div> -->
			<div class="box-content__table">
				<div class="title">年度考核</div>
				<DataTable :data-source="assessment.datasource" :columns="assessment.columns" />
			</div>
			<div class="box-content__table">
				<div class="title"><span>表彰表扬</span> <span class="label-icon" @click="openRuleModal(3)"></span></div>
				<DataTable :data-source="honorary.datasource" :columns="honorary.columns" />
			</div>
		</div>
	</Card>
</template>

<script lang="ts">
import { getAchievement, getEvalData, getGloryData } from '@/apis/cadre-portrait/home'
import DataTable from '@/components/Table.vue'
import { convertPxToRem } from '@/utils/utils'
import { computed, defineComponent, inject, reactive } from 'vue'
import { createMarkLine, transformDataToValue, transformXlabelData } from '../utils/echarts'
export default defineComponent({
	name: 'HistoricalPerformance',
	components: {
		DataTable,
	},
	setup() {
		const { user_id } = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
		const color = ['#6AAEFB', '#FE533A']
		const apiData = reactive<any>({
			echarts: {
				achievement: [],
				xAxis: [],
				average: [],
				tableData: [],
				colums: [],
			},
			evalData: {
				year: [],
				achievement: [],
				rank: [],
				tabelData: [],
			},
			gloryData: [],
			negativeData: [],
		})

		const dynamicTableHeader = (list: Array<any>, color?: any, title = '') => {
			const columns: Array<any> = []

			list.forEach((item: any, index: number) => {
				const key = `name`
				if (index === 0) {
					columns[index] = {
						key,
						title,
						width: '8%',
						color,
					}
				}
				columns[index + 1] = {
					key: `${key}${index + 1}`,
					title: item,
					width: 100 / (list.length + 1) + '%',
					align: 'center',
					color,
				}
			})
			return columns
		}
		/**
		 * @description: 折线图数据
		 * @param {*} computed
		 * @return {*}
		 */
		const option = computed(() => {
			const { xAxis, average, achievement } = apiData.echarts
			// x轴数据
			const XName = xAxis
			// 折线图数据
			const data1 = [achievement, average]
			// legend数据
			const Line = ['业绩', '平均值']
			// 每条线的数据
			const innerColor = ['#ffffff', '#ffffff']
			//
			const datas: any[] = []
			const position = [19, 43, 67, 90]
			const positionMap = transformXlabelData(position, XName)
			const markLine = createMarkLine(positionMap)
			Line.map((item, index) => {
				datas.push({
					symbolSize: 11,
					symbol: 'circle',
					name: item,
					type: 'line',
					yAxisIndex: 1,
					data: transformDataToValue(position, data1[index]), // 数据
					itemStyle: {
						borderWidth: 4,
						borderColor: color[index],
						color: innerColor[index],
					},
					lineStyle: {
						type: 'solid',
						color: color[index],
					},
					z: 10,
					markLine: index === 0 ? markLine : {},
				})
			})

			const option = {
				grid: {
					left: '5%',
					top: '20%',
					bottom: '1%',
					containLabel: true,
				},
				legend: {
					show: true,
					top: 0,
					left: 'center',
					icon: 'circle',
					itemWidth: convertPxToRem(12),
					itemHeight: convertPxToRem(12),
					textStyle: {
						color: '#333333',
						fontSize: convertPxToRem(18),
					},
					itemStyle: {
						borderWidth: convertPxToRem(4),
					},
					itemGap: convertPxToRem(60),
				},
				yAxis: [
					{
						type: 'value',
						name: '业绩',
						position: 'left',
						inverse: true,
						nameTextStyle: {
							color: '#000000',
							fontSize: convertPxToRem(16),
						},
						nameLocation: 'end',
						// scale: true,
						max: (value: any) => {
							return value.max + 6
						},
						min: 1,
						splitLine: {
							lineStyle: {
								type: 'dashed',
								color: '#EEEEEE',
							},
						},
						axisLine: {
							show: false,
							lineStyle: {
								color: '#333333',
							},
							symbol: ['none', 'arrow'],
							symbolOffset: 7,
							symbolSize: [7, 10],
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							color: '#666666',
							fontSize: convertPxToRem(14),
							formatter: (value: any) => {
								value = Number(value)
								return value
							},
						},
					},
					{
						type: 'value',
						name: '平均值',
						nameTextStyle: {
							color: '#000000',
							fontSize: convertPxToRem(16),
						},
						position: 'right',
						splitLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLine: {
							show: false,
						},
						min: 1,
						max: (value: any) => value.max + 5,
						inverse: true,
						axisLabel: {
							show: true,

							textStyle: {
								color: '#000000',
								fontSize: convertPxToRem(14),
							},
						},
						nameLocation: 'end',
					},
				],
				xAxis: [
					{
						type: 'category',
						axisTick: {
							show: false,
						},
						axisLine: {
							show: true,
							lineStyle: {
								color: '#333333',
							},
							symbol: ['none', 'arrow'],
							onZero: false,
							symbolOffset: 7,
							symbolSize: [7, 10],
						},
						splitLine: {
							show: false,
						},
						axisLabel: {
							inside: false,
							textStyle: {
								color: '#666666', // x轴颜色
								fontWeight: 'normal',
								fontSize: convertPxToRem(16),
								lineHeight: convertPxToRem(22),
							},
						},
						position: 'bottom',
						data: XName,
					},
				],
				series: datas,
			}

			return option
		})
		/**
		 * @description: 历史业绩表格数据
		 * @param {*} computed
		 * @return {*}
		 */
		const performanceTable = computed(() => {
			const { xAxis } = apiData.echarts

			const columns = dynamicTableHeader(xAxis, '', '年度')

			const datasource = apiData.echarts.tableData
			const rowColor = (_item: any, index: number) => {
				return color[index]
			}

			return { columns, datasource, rowColor }
		})
		/**
		 * @description: 年度考核表格数据
		 * @param {*} computed
		 * @return {*}
		 */
		const assessment = computed(() => {
			// const columns = [
			// 	{
			// 		key: 'name',
			// 		align: 'center',
			// 		width: '8%',
			// 		title: '年度',
			// 		color,
			// 	},
			// 	{
			// 		key: 'name1',
			// 		align: 'center',
			// 		sort: true,
			// 		width: '8%',
			// 		title: '2022',
			// 		color,
			// 	},
			// 	{
			// 		key: 'name2',
			// 		align: 'center',
			// 		sort: true,
			// 		width: '8%',
			// 		title: '2021',
			// 		color,
			// 	},
			// 	{
			// 		key: 'name3',
			// 		align: 'center',
			// 		sort: true,
			// 		width: '8%',
			// 		title: '2020',
			// 		color,
			// 	},
			// 	{
			// 		key: 'name4',
			// 		align: 'center',
			// 		sort: true,
			// 		width: '8%',
			// 		title: '2019',
			// 		color,
			// 	},
			// 	{
			// 		key: 'name5',
			// 		align: 'center',
			// 		sort: true,
			// 		width: '8%',
			// 		title: '2018',
			// 		color,
			// 	},
			// 	{
			// 		key: 'name6',
			// 		align: 'center',
			// 		sort: true,
			// 		width: '8%',
			// 		title: '2017',
			// 		color,
			// 	},
			// 	{
			// 		key: 'name7',
			// 		align: 'center',
			// 		sort: true,
			// 		width: '8%',
			// 		title: '2016',
			// 		color,
			// 	},
			// 	{
			// 		key: 'name8',
			// 		align: 'center',
			// 		sort: true,
			// 		width: '8%',
			// 		title: '2015',
			// 		color,
			// 	},
			// 	{
			// 		key: 'name9',
			// 		align: 'center',
			// 		sort: true,
			// 		width: '8%',
			// 		title: '2014',
			// 		color,
			// 	},
			// 	{
			// 		key: 'name10',
			// 		align: 'center',
			// 		sort: true,
			// 		width: '8%',
			// 		title: '2013',
			// 		color,
			// 	},
			// ]
			// 年度考核表格header
			//根据年份动态生成表格header
			const xAxis = apiData.evalData.year
			const datasource = apiData.evalData.tableData
			// 动态表格数据颜色
			const color = (value: any) => {
				return value === '优秀' ? '#60CA71' : ''
			}
			// 动态表格header

			const columns = dynamicTableHeader(xAxis, color, '年度')
			return { columns, datasource }
		})
		/**
		 * @description: 荣誉表彰
		 * @param {*} computed
		 * @return {*}
		 */
		const honorary = computed(() => {
			const color = () => '#E5231A'
			const columns = [
				{
					key: 'type',
					align: 'center',
					width: '30%',
					title: '类型',
				},
				{
					key: 'score',
					align: 'center',
					width: '15%',
					title: '加减分',
					color,
				},
				{
					key: 'item',
					align: 'center',
					width: '55%',
					title: '事项',
				},
			]

			const datasource = apiData.gloryData
			return { columns, datasource }
		})
		/**
		 * @description: 将数据转换为table所需的数据格式
		 * @param {*} origin_data
		 * @param {*} title
		 * @return {*}
		 */
		const createData = (origin_data: any[], title: string) => {
			const data: any = {}
			title && (data.name = title)
			for (let i = 0; i < origin_data.length; i++) {
				data[`name${i + 1}`] = origin_data[i]
			}

			return data
		}
		// 业绩分析接口
		const loadAchieveData = async () => {
			const res = await getAchievement({ user_id })
			if (res.code === 0) {
				const { year, achievement, avg } = res.data
				apiData.echarts = {
					xAxis: year,
					achievement,
					average: avg,
					tableData: [],
				}
				apiData.echarts.tableData.push(createData(achievement, '业绩'), createData(avg, '平均值'))
			}
		}
		// 考核数据接口
		const loadEvalData = async () => {
			const res = await getEvalData({ user_id })
			if (res.code === 0) {
				const { year, achievement, rank } = res.data
				const tableData = [/**createData(year, '年度'),*/ createData(achievement, '结果'), createData(rank, '排名')]
				apiData.evalData = {
					tableData,
					year,
					rank,
					achievement,
				}
			}
		}
		// 荣誉表彰接口
		const loadGlory = async () => {
			const res = await getGloryData({ user_id })
			if (res.code === 0) {
				apiData.gloryData = res.data
			}
		}
		/**
		 * @description: 加载数据
		 * @return {*}
		 */
		const loadData = () => {
			loadAchieveData()
			loadEvalData()
			loadGlory()
		}
		loadData()

		const openRuleModal: any = inject('openRuleModal')

		return { option, performanceTable, assessment, honorary, apiData, openRuleModal }
	},
})
</script>

<style scoped lang="less">
.histoical-performance {
	height: 100%;
}
.box-content__line {
	height: 298px;
}
.box-content__table {
	margin-top: 25px;
	flex: 1;
	.title {
		display: flex;
		align-items: center;
		font-size: 20px;
		font-family: PingFang SC-Heavy, PingFang SC;
		font-weight: 800;
		color: #333333;
		line-height: 24px;
		padding: 7px 0 20px;

		&::before {
			margin-right: 17px;
			content: '';
			display: inline-block;
			width: 16px;
			height: 16px;
			background: #ec4224;
			opacity: 1;
			border-radius: 50%;
		}
	}
}
.label-icon {
	margin-left: 11px;
	display: inline-block;
	width: 22px;
	height: 22px;
	line-height: 1;
	background: url('@/assets/images/question-mark.png') no-repeat center / 100% 100%;
	cursor: pointer;
}
// .margin-top-30 {
// 	margin-top: -30px;
// }
</style>
