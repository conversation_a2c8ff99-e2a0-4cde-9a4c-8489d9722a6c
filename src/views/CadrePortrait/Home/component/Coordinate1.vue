<template>
	<Card title="坐标分布" :sub-title="title" header size-type="2">
		<template v-slot:right>
			<div class="data-menu">
				<div :class="['menu-item', coor_menu_selected === item.key && 'menu-active']" v-for="item in menu" :key="item.key" @click="onMenu(item.key)">
					{{ item.label }}
				</div>
			</div>
		</template>
		<v-chart :option="option" autoresize ref="echartsRef"></v-chart>
	</Card>
</template>

<script lang="ts">
import { CoordinateDataType } from '@/types/user'
import { defineComponent, ref, computed, watch, toRefs } from 'vue'
import { getPointData } from '@/apis/cadre-portrait/home'
import { useHomeStore } from '@/store/home'
import { debounce } from '@/utils/utils'
// 0-全县, 1-本单位, 2-同序列, 3-全部乡镇/部门
const menu = [
	{
		label: '本单位',
		key: 1,
	},
	{
		label: '同序列',
		key: 2,
	},
	{
		label: '乡镇（部门）',
		key: 3,
	},
	{
		label: '全县',
		key: 0,
	},
]
// const self = [91, 28.8]
export default defineComponent({
	name: 'Coordinate',
	setup() {
		const echartsRef = ref<any>(null)
		const store = useHomeStore()
		const { menu, coor_menu_selected } = toRefs(store)
		// const currentIndex = ref(1)
		const data = ref<CoordinateDataType>({
			mine: {
				x: 0,
				y: 0,
				user_id: 0,
				name: '',
			},
			others: [],
			x_avg: 0,
			y_avg: 0,
			type: undefined,
		})

		const title = computed(() => {
			if (data.value.type === 1) {
				return '（县管正职）'
			} else {
				return '（县管副职）'
			}
		})
		const option = computed(() => {
			const { mine, others, x_avg, y_avg: _y_avg } = data.value
			let otherData: any = []
			const selfData: any = [mine.x, mine.y, mine.name, mine.user_id]

			// 处理数据
			otherData = otherData.concat(others, [mine]).map((item: any) => {
				return [item.x, item.y, item.name, item.user_id]
			})

			const _option = {
				color: ['#17EBC7'],
				grid: {
					top: '7%',
					left: '5%',
					right: '5%',
					bottom: '3%',
					containLabel: true,
					show: false,
				},
				tooltip: {
					trigger: 'item',
					showDelay: 0,
					show: true,
					formatter: function (name: any) {
						const { value } = name
						return `
							<div class="tooltips-box">
								<div class="data-name">${value[2]}</div>
								<div class="data-item">业绩：${value[0] || ''}</div>
								<div class="data-item">能力：${value[1] || ''}</div>
							</div>
						`
					},
				},
				xAxis: {
					name: '业绩',
					type: 'value',
					scale: true,
					axisLabel: {
						color: '#2898E5',
						formatter: (value: any) => Number(value).toFixed(2),
						showMaxLabel: false,
					},
					axisLine: {
						show: true,
						lineStyle: {
							color: '#163F91',
						},
					},
					splitLine: {
						show: false,
					},
					// min: 0,
					// max: 96,
					// splitNumber: 7,
					// 刻度线
					axisTick: {
						show: true,
					},
					nameTextStyle: {
						color: '#23DBFC',
						// align: 'left',
						padding: [7, 0, 0, -40],
						verticalAlign: 'top',
					},
				},
				yAxis: {
					name: '能力',
					type: 'value',
					scale: true,
					axisLabel: {
						color: '#2898E5',
						formatter: (value: any) => Number(value).toFixed(2),
						showMaxLabel: false,
					},
					splitLine: {
						show: true,
						lineStyle: {
							color: '#163F91',
							type: 'dashed',
						},
					},
					axisLine: {
						show: true,
						lineStyle: {
							color: '#163F91',
						},
					},
					axisTick: {
						show: false,
					},
					nameTextStyle: {
						color: '#23DBFC',
						// align: 'right',
						padding: [0, 20, -20, -20],
					},
					// min: 28,
					// max: 29.4,
					splitNumber: 7,
				},
				series: [
					{
						name: '业绩-能力',
						type: 'scatter',
						data: otherData,
						symbol: (value: Array<number>) => {
							if (value[0] === selfData[0] && value[1] === selfData[1]) {
								return 'none'
							} else {
								return 'circle'
							}
						},
						markLine: {
							symbol: 'none',
							label: { show: false },
							silent: true,
							data: [
								{
									type: 'average',
									name: '平均值',
								},
								{
									xAxis: x_avg,
								},
								// [
								// 	{
								// 		coord: [0, 0],
								// 		lineStyle: {
								// 			width: 1,
								// 			type: 'solid',
								// 			color: '#FFBA55',
								// 		},
								// 	},
								// 	{
								// 		coord: [91, 28.5],
								// 		lineStyle: {
								// 			width: 1,
								// 			type: 'solid',
								// 			color: '#FFBA55',
								// 		},
								// 	},
								// ],
								// [
								// 	{
								// 		coord: [0, 28.5],
								// 		lineStyle: {
								// 			width: 1,
								// 			type: 'solid',
								// 			color: '#FFBA55',
								// 		},
								// 	},
								// 	{
								// 		coord: [91, 28.5],
								// 		lineStyle: {
								// 			width: 1,
								// 			type: 'solid',
								// 			color: '#FFBA55',
								// 		},
								// 	},
								// ],
								// [
								// 	{
								// 		coord: [91, 0],
								// 		lineStyle: {
								// 			width: 1,
								// 			type: 'solid',
								// 			color: '#FFBA55',
								// 		},
								// 	},
								// 	{
								// 		coord: [91, 28.5],
								// 		lineStyle: {
								// 			width: 1,
								// 			type: 'solid',
								// 			color: '#ccc',
								// 		},
								// 	},
								// ],
							],
						},
					},
					{
						name: '业绩-能力',
						type: 'scatter',
						// 分别为原点，自己，x轴
						data: [selfData],
						// symbol: (value: Array<number>) => {
						// 	if (value[0] === 0 && value[1] === 0) {
						// 		return 'none'
						// 	} else {
						// 		return 'circle'
						// 	}
						// },
						itemStyle: {
							color: '#FFBA55',
						},
						markLine: {
							symbol: 'none',
							label: { show: false },
							silent: true,
							data: [
								[
									{
										coord: [0, 0],
										lineStyle: {
											width: 1,
											type: 'solid',
											color: '#FFBA55',
										},
									},
									{
										coord: selfData,
										lineStyle: {
											width: 2,
											type: 'solid',
											color: '#FFBA55',
										},
									},
								],
								[
									{
										coord: [0, selfData[1]],
										lineStyle: {
											width: 1,
											type: 'solid',
											color: '#FFBA55',
										},
									},
									{
										coord: selfData,
										lineStyle: {
											width: 1,
											type: 'solid',
											color: '#FFBA55',
										},
									},
								],
								[
									{
										coord: [selfData[0], 0],
										lineStyle: {
											width: 1,
											type: 'solid',
											color: '#FFBA55',
										},
									},
									{
										coord: selfData,
										lineStyle: {
											width: 1,
											type: 'solid',
											color: '#ccc',
										},
									},
								],
							],
						},
					},
				],
			}

			return _option
		})
		const onMenu = (key: number) => {
			// currentIndex.value = key
			store.updateCoorMenuSelected(key)
		}
		/**
		 * @description: 加载数据
		 * @return {*}
		 */
		const loadData = async () => {
			const { user_id, org_id, _oid } = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
			// 0-全县, 1-本单位, 2-同序列, 3-全部乡镇/部门
			const res = await getPointData({ user_id, flag: coor_menu_selected.value, org_id: org_id || _oid })
			if (res.code === 0 && res.data) {
				data.value = res.data
			}
		}

		// loadData()
		// 监听currentIndex变化 更新图表数据
		watch(
			coor_menu_selected,
			debounce(() => {
				loadData()
			}, 500),
			{
				immediate: true,
			}
		)
		// echarts 绑定点击事件
		watch(echartsRef, () => {
			echartsRef.value.chart.on('click', (params: any) => {
				store.updateCoorUserId(params.data?.[3])
			})
		})
		return {
			menu,
			title,
			option,
			echartsRef,
			coor_menu_selected,
			onMenu,
		}
	},
})
</script>

<style scoped lang="less">
.data-menu {
	display: flex;
	.menu-item {
		margin-left: 15px;
		font-size: 14px;
		font-weight: 400;
		color: #9e9e9e;
		cursor: pointer;
	}
	.menu-active {
		color: #00eaff;
	}
}
</style>
