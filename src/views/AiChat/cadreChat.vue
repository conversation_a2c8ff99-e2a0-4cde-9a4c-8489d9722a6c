<template>
	<div class="ai-chat">
		<!-- 顶部标题栏 -->
		<div class="chat-header">
			<div class="title">查询助手</div>
			<div class="close-btn" @click.stop="handleClose">
				<div class="close-icon"></div>
			</div>
		</div>

		<!-- 聊天内容区域 -->
		<div class="chat-content" ref="chatContent">
			<template v-if="!isEmptyMessages">
				<div v-for="(msg, index) in messages" :key="msg.id" :class="['message', 'message-warper', msg.type]">
					<div class="message-row">
						<!-- AI 的头像 -->
						<div class="avatar" v-if="msg.type === 'ai'">
							<div class="avatar-icon"></div>
						</div>
						<!-- 消息内容 -->
						<div class="message-content" :class="{ 'user-content': msg.type === 'user', 'ai-content': msg.type === 'ai' }">
							<!-- 加载状态 -->
							<template v-if="msg.status === 'loading'">
								<div class="loading-icon"></div>
								<div class="loading-text">正在加载</div>
							</template>
							<!-- 成功或错误状态 -->
							<template v-else>
								<div v-if="msg.img_status === 'success'" class="success-icon"></div>
								<div v-else-if="msg.img_status === 'error'" class="error-icon"></div>
								<!-- 消息文本 -->
								<template v-if="msg.type === 'ai'">
									<div class="text-content" v-html="msg.content"></div>
								</template>
								<template v-else>
									<div v-html="msg.content"></div>
								</template>
							</template>
						</div>
					</div>
				</div>
			</template>
		</div>

		<!-- 底部输入区域 -->
		<div class="chat-input">
			<div :class="[isRecording ? 'wake-active' : 'chat-input-speak']">
				<!-- 如果是被唤醒状态或正在录音  -->
				<template v-if="isRecording">
					<div class="recording-screen">
						<div class="recording-instruction">正在录音中...</div>
						<div class="recording-wave-input">
							<div class="recording-wave">
								<div class="wave"></div>
							</div>
							<div class="stop-recording-btn" @click="endLongPress">
								<div class="stop-icon"></div>
							</div>
						</div>
					</div>
				</template>
				<!-- 如果既不是被唤醒状态，也不是正在录音 -->
				<template v-else>
					<div class="chat-input-bg" @click="startLongPress">
						<div class="chat-input-speak-icon"></div>
					</div>

					<input
						type="text"
						v-model="inputMessage"
						@keyup.enter="sendMessage"
						placeholder="随时向我提问..."
						class="text-input"
						v-if="!isProcessing"
					/>
					<div v-else class="processing-message">正在处理语音内容，请稍等...</div>
					<div class="send-btn" @click="sendMessage">
						<div class="send-icon"></div>
					</div>
				</template>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useComparative } from '@/store/comparative'
import { message } from 'ant-design-vue'
import axios from 'axios'
import { marked } from 'marked'
import { computed, nextTick, onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { MIME_TYPE_MAP } from './type'
//#region ====================== 定义变量 ======================
// API配置  工作流
const API_URL = import.meta.env.VITE_API_URL
const API_KEY = import.meta.env.VITE_API_KEY
const WORKFLOW_API_KEY = import.meta.env.VITE_WORKFLOW_API_KEY
const props = defineProps({
	totalElements: {
		type: Number,
		default: 0,
	},
})

const emits = defineEmits(['close', 'updateForm'])
interface Message {
	id: string
	content: string
	type: 'user' | 'ai'
	img_status?: 'success' | 'error'
	status?: 'loading' | 'loaded'
	nameResultData?: any[] // 每条消息单独存储查询结果
}
const messages = ref<Message[]>([]) // 聊天记录
const inputMessage = ref('') // 用户输入的消息
const chatContent = ref<HTMLElement | null>(null) // 聊天内容区域
const currentStreamingMessage = ref<Message | null>(null)
const recordingTimeout = ref<ReturnType<typeof setTimeout> | null>(null) // 录音定时器
const isRecording = ref(false) // 是否正在录音
const recordingFile = ref(null) as any //录音文件
const longPressTimer = ref<ReturnType<typeof setTimeout> | null>(null) // 录音定时器
const isWaitingForResponse = ref(false)
const router = useRouter()
const comparative = useComparative() // 用于存储对比结果
const isEmptyMessages = computed(() => messages.value.length === 0)
const isProcessing = ref(false) // 新增处理状态
//#endregion
//#region ====================== 用户语音或文字输入发送消息 ======================
// 发送消息
const sendMessage = async () => {
	if (!inputMessage.value.trim()) return
	if (isWaitingForResponse.value) {
		message.warning('正在等待 AI 回复，请稍后再试。')
		return
	}

	isWaitingForResponse.value = true
	isProcessing.value = false // 开始处理

	// 添加用户消息到聊天记录
	messages.value.push({
		id: `user-${Date.now()}`,
		content: inputMessage.value,
		type: 'user',
	})

	// 创建一个新的 AI 消息占位，并显示"正在加载"状态
	currentStreamingMessage.value = {
		id: `ai-${Date.now()}`,
		content: '',
		type: 'ai',
		status: 'loading',
		img_status: undefined,
		nameResultData: undefined, // 初始化为 undefined，确保连续查询时正确重置
	}
	messages.value.push(currentStreamingMessage.value)

	// 清空输入框
	const question = inputMessage.value
	inputMessage.value = ''
	scrollToBottom()

	try {
		// 发起请求
		const response = await axios.post(
			`${API_URL}/v1/workflows/run`,
			{
				inputs: {
					query: question,
				},
				response_mode: 'blocking',
				user: `user-${Date.now()}`,
			},
			{
				headers: {
					Authorization: `Bearer ${WORKFLOW_API_KEY}`,
					'Content-Type': 'application/json',
				},
			}
		)

		// 解析响应数据
		const data = response.data.data
		let aiResponse = ''
		if (data.status == 'succeeded' && data.outputs.success == 1) {
			const result = data.outputs.result
			if (Object.keys(result).length === 0) {
				aiResponse += '未查询到当前的干部'
			} else {
				aiResponse += processResult(result)
			}
		} else {
			aiResponse += '查询失败'
		}
		const htmlContent = marked(aiResponse)
		// 更新当前 AI 消息的内容和状态
		currentStreamingMessage.value.status = 'loaded'
		currentStreamingMessage.value.img_status = aiResponse.includes('失败') || aiResponse.includes('未找到') ? 'error' : 'success'
		currentStreamingMessage.value.content = htmlContent
	} catch (error) {
		console.error('AI response error:', error)
		currentStreamingMessage.value.status = 'loaded'
		currentStreamingMessage.value.img_status = 'error'
		currentStreamingMessage.value.content = error.message || '请求失败'
	} finally {
		isWaitingForResponse.value = false
		scrollToBottom()
		isProcessing.value = false
	}
}

const processResult = (result: any) => {
	// 发出事件给父组件，触发跳转到查询结果页面
	emits('updateForm', result)

	// 格式化查询结果为可读内容，支持多轮对话
	let content = ''

	if (!result || Object.keys(result).length === 0) {
		content = '未查询到符合条件的干部信息，请尝试调整查询条件。'
	} else {
		// 优先使用从父组件传递的totalElements，如果没有则计算
		let totalCount = props.totalElements
		console.log("🚀 ~ totalCount:", totalCount)
		if (totalCount === 0) {
			totalCount = Object.values(result).reduce((sum: number, arr: any) => {
				return sum + (Array.isArray(arr) ? arr.length : 0)
			}, 0)
		}

		content = `为您找到 ${totalCount} 位符合条件的干部，正在为您跳转到查询结果页面...`
	}

	return content
}

// 滚动到底部
const scrollToBottom = async () => {
	await nextTick()
	if (chatContent.value) {
		chatContent.value.scrollTop = chatContent.value.scrollHeight
	}
}

// 关闭对话
const handleClose = () => {
	emits('close')
}

// 开始录音
const startLongPress = () => {
	if (!window.globalJsKit) {
		message.error('语音功能初始化中，请稍后重试')
		return
	}
	if (isWaitingForResponse.value) {
		isProcessing.value = false
		message.warning('正在等待 AI 回复，请稍后再试。')
		return
	}
	if (isRecording.value) return

	const isStartRecord = 500
	longPressTimer.value = setTimeout(() => {
		isRecording.value = true
		window.globalJsKit.startRecord()
		isProcessing.value = true

		const maxRecordingTime = 10000 // 10秒
		const stopRecordingTimer = setTimeout(() => {
			if (isRecording.value) {
				endLongPress() // 自动停止录音
			}
		}, maxRecordingTime)

		recordingTimeout.value = stopRecordingTimer
	}, isStartRecord)
}

const clearTimers = (...timers: (NodeJS.Timeout | null)[]) => {
	timers.forEach((timer) => timer && clearTimeout(timer))
}
const getFileExtension = (filename: string) => filename.split('.').pop()?.toLowerCase() || 'wav'
const createAudioFile = async (fileData: string | Blob, extension: string) => {
	if (typeof fileData === 'string') {
		const response = await axios.get(fileData, { responseType: 'blob' })
		return new File([response.data], `recording.${extension}`, {
			type: MIME_TYPE_MAP[extension] || 'audio/wav',
		})
	}
	return fileData
}
const handleSpeechRecognition = async (file: File) => {
	const formData = new FormData()
	formData.append('file', file)
	try {
		const response = await axios.post(`${API_URL}/v1/audio-to-text`, formData, {
			headers: {
				Authorization: `Bearer ${API_KEY}`,
			},
		})
		return response.data?.text || ''
	} catch (error) {
		throw new Error('语音识别请求失败')
	}
}
const endLongPress = () => {
	clearTimers(longPressTimer.value, recordingTimeout.value)
	longPressTimer.value = null
	recordingTimeout.value = null
	if (!isRecording.value) return Promise.resolve()
	// 锁定初始录音状态
	let currentRecordingFile: any = null
	return new Promise((resolve, reject) => {
		// 第一阶段：停止录音
		Promise.resolve()
			.then(() => {
				isProcessing.value = true
				isRecording.value = false
				window.globalJsKit.stopRecord()
			})
			// 第二阶段：等待文件就绪
			.then(
				() =>
					new Promise<void>((res, rej) => {
						const checkInterval = setInterval(() => {
							if (recordingFile.value) {
								currentRecordingFile = recordingFile.value // 锁定文件引用
								clearInterval(checkInterval)
								res()
							}
						}, 100)

						// 增加超时保护
						setTimeout(() => {
							clearInterval(checkInterval)
							rej(new Error('等待录音文件超时'))
						}, 5000)
					})
			)
			// 第三阶段：处理文件
			.then(() => {
				console.log('获取到录音文件:', currentRecordingFile)

				if (!currentRecordingFile) {
					throw new Error('录音文件不存在')
				}

				const fileExtension = getFileExtension(currentRecordingFile)
				console.log('文件扩展名:', fileExtension)
				return createAudioFile(currentRecordingFile, fileExtension)
			})
			// 第四阶段：语音识别
			.then((file) => {
				console.log('创建文件对象完成:', file)
				if (!(file instanceof File)) {
					throw new Error('文件对象创建失败')
				}
				return handleSpeechRecognition(file)
			})
			// 第五阶段：处理结果
			.then((rawText) => {
				console.log('原始识别结果:', rawText)
				const result = window.globalJsKit.processChineseText(rawText)

				if (result?.startsWith('error')) {
					throw new Error('语音处理错误: ' + result)
				}

				inputMessage.value = result
				isProcessing.value = true // 确保在发送消息前保持“即将发送消息，请稍等”的状态
				console.log('最终处理结果:', inputMessage.value)
				sendMessage()
			})
			// 最终处理
			.then(resolve)
			.catch((error) => {
				isProcessing.value = false
				message.error(error instanceof Error ? error.message : '语音识别失败，请重试')
				reject(error)
			})
			.finally(() => {
				isProcessing.value = false
				recordingFile.value = null
				currentRecordingFile = null // 释放引用
			})
	})
}
//#endregion
//#region ====================== 初始化 ======================
// 组件加载时添加初始消息
onMounted(() => {
	messages.value.push({
		id: `ai-${Date.now()}`,
		content: '帮您查询(例如:90后女性干部，3年以上乡镇工作经验或者2年以上乡镇领导经验，大专以上学历。)',
		type: 'ai',
		status: 'loaded',
		img_status: undefined,
		nameResultData: undefined,
	})
	// 轮询检查 js_kit 是否注入
	const maxAttempts = 20
	let attempts = 0
	const checkJsKit = () => {
		if (typeof window.globalJsKit === 'undefined') {
			attempts++
			if (attempts < maxAttempts) {
				setTimeout(checkJsKit, 500)
			} else {
				message.error('语音功能初始化失败')
			}
		} else {
			registerCallbacks()
		}
	}
	checkJsKit()
})
const registerCallbacks = () => {
	window.recordStartCallback = (_filePath: string) => {
		isRecording.value = true
		scrollToBottom()
	}
	window.recordFinishCallback = (filePath: string) => {
		recordingFile.value = filePath
		isRecording.value = false
	}
}
// 组件卸载时清理
onUnmounted(async () => {
	// 清理录音回调
	window.recordStartCallback = undefined
	window.recordFinishCallback = undefined
	if (isRecording.value) {
		try {
			await window.globalJsKit.stopRecord()
			isRecording.value = false
		} catch (error) {
			console.error('Error stopping recording:', error)
		}
	}
})
//#endregion
defineExpose({
	registerCallbacks,
})
</script>

<style scoped lang="less">
// 弹窗样式
.ai-chat {
	display: flex;
	height: 50%;
	background-color: #f5f5f5;
	width: 480px;
	transform: translateZ(0);
	position: fixed;
	top: 65px; /* 保持距离顶部间距 */
	right: 20px; /* 右侧间距加大到20px */
	bottom: 40px; /* 保持底部间距 */
	border-radius: 12px;
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); /* 添加立体阴影 */
	z-index: 999;
	flex-direction: column; /* 纵向布局 */
	overflow: hidden; /* 内部元素超出隐藏 */
	transition: all 0.3s ease; /* 添加过渡动画 */
	opacity: 1;
	/* 可选边框样式 */
	border: 1px solid #ebeef5;
	@media (max-width: 550px) {
		border: 1px solid #f5f5f5;
		height: 100vh;
	}
	&.wake-mode {
		transform: translateY(-10px);
		box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
		border-color: #4285f4;
		animation: wakeUpAnimation 0.5s forwards;
	}
	.chat-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16px 15px;
		background-color: #3e6efd;
		color: white;
		cursor: move;
		.title {
			font-size: 18px;
			margin: 0px auto;
			font-weight: bold;
		}

		.close-btn {
			font-size: 24px;
			cursor: pointer;
			padding: 0 5px;
			.close-icon {
				width: 24px;
				height: 24px;
				background: url('@/assets/images/ai-list/ai_close.webp') no-repeat center center / 100% 100%;
			}
		}
	}

	.chat-content {
		flex: 1;
		overflow-y: auto;
		padding: 15px;
		background-color: #ffff;
		height: 100%;
		position: relative;
		box-sizing: border-box;
		.message {
			display: flex;
			margin-bottom: 15px;
			// align-items: center;
			&.user {
				justify-content: flex-end;
				flex-direction: row; // 修改为行布局
				align-items: center; // 垂直居中对齐
				.message-content {
					background: #f5f5f5;
					border-radius: 24px;
					font-size: 18px;
					display: flex;
					margin-left: auto;
					align-items: center;
					padding: 8px 12px;
					max-width: calc(100% - 60px);
					word-wrap: break-word;
				}
			}

			&.ai {
				justify-content: flex-start;
				flex-direction: column; // 修改为行布局
				align-items: center; // 垂直居中对齐
				.message-content {
					margin-right: auto; // 保持右侧对齐
					background: #f5f5f5;
					border-radius: 24px;
					font-size: 18px;
					display: flex;
					justify-content: flex-start;
					padding: 8px 12px; // 添加内边距
					max-width: calc(100% - 60px); // 确保文字不会超出内容框，减去图片的宽度
					word-wrap: break-word; // 自动换行
				}
			}
			.message-row {
				display: flex;
				align-items: center;
				margin-bottom: 15px;
				width: 100%;
				.avatar {
					margin-right: 8px;

					.avatar-icon {
						width: 40px;
						height: 40px;
						background: url('@/assets/images/ai-list/ai_avatar_right.webp') no-repeat center center / 100% 100%;
						cursor: pointer;
					}
				}
				.loading-icon {
					display: inline-block;
					width: 20px;
					height: 20px;
					border: 3px solid transparent;
					border-top-color: #3e6efd;
					border-radius: 50%;
					animation: spin 1s linear infinite;
				}
				.loading-text {
					display: inline-block;
					margin-left: 8px; /* 与加载图标保持一定间距 */
					font-size: 18px;
				}
			}
			.success-icon {
				margin-right: 5px; // 保持图片和文字的间距
				width: 30px;
				height: 30px;
				display: inline-block;
				background: url('@/assets/images/ai-list/ai_check.webp') no-repeat center center / 100% 100%;
				background-size: contain;
			}

			.error-icon {
				margin-right: 5px;
				width: 30px;
				height: 30px;
				display: inline-block;
				background: url('@/assets/images/ai-list/ai_error.webp') no-repeat center center / 100% 100%;
				background-size: contain;
			}
		}
		.message-warper {
			display: flex;
		}
		.overlay {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			// background-color: rgba(255, 255, 255, 0.8);
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;
			z-index: 10;
			padding: 0 45px;
			&-avatar {
				padding: 8px;
				.avatar-icon {
					width: 100px;
					height: 100px;
					background: url('@/assets/images/ai-list/ai_avatar_right.webp') no-repeat center center / 100% 100%;
				}
			}
			&-name {
				font-size: 25px;
				color: #000000;
				font-weight: bold;
				padding: 15px;
				letter-spacing: 1px; /* 字母间距 */
				word-spacing: 2px; /* 单词间距 */
			}
			&-text {
				font-size: 20px;
				color: #404040;
				text-align: center;
				letter-spacing: 1px; /* 字母间距 */
				word-spacing: 2px; /* 单词间距 */
			}
		}
	}

	.chat-input {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 10px;
		background-color: white;
		border-top: 1px solid #f5f5f5;

		.chat-input-speak {
			width: 100%;
			height: 150px;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			.chat-input-bg {
				width: 50px;
				height: 50px;
				background-color: #f5f5f5;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;

				.chat-input-speak-icon {
					width: 24px;
					height: 24px;
					background: url('@/assets/images/ai-list/ai_speaker.webp') no-repeat center center / contain;
				}
			}
			.recording-status {
				margin-left: 10px; /* 与图标保持一定间距 */
				font-size: 16px;
				color: #3e6efd; /* 使用主题颜色 */
			}
		}

		.wake-active {
			width: 100%;
			height: 150px;
			background-color: rgba(255, 255, 255, 0.95);
			.recording-screen {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				.recording-instruction {
					font-size: 18px;
					color: #666666;
					margin-bottom: 10px;
					height: 100%;
				}
				.recording-wave-input {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 0 10px; /* 左右内边距 */
					.recording-wave {
						width: calc(100% - 50px); /* 减去按钮的宽度和间距 */
						height: 70px;
						background-color: #3b82f6;
						border-radius: 25px;
						overflow: hidden;
						padding: 8px;
						.wave {
							width: 100%;
							height: 100%;
							background: url('@/assets/images/ai-list/ai_wave_white.webp') repeat center center / contain;
							animation: wave 1.5s ease-in-out infinite;
						}
					}
					.stop-recording-btn {
						width: 40px;
						height: 40px;
						//background-color: red;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						cursor: pointer;
						margin-left: 10px; /* 与波形之间的间距 */
						.stop-icon {
							width: 24px;
							height: 24px;
							background-image: url('@/assets/images/ai-list/ai_stop.webp'); /* 替换为实际的停止图标路径 */
							background-size: cover;
						}
					}
				}
			}
		}

		.text-input {
			flex: 1;
			padding: 14px;
			border: 1px solid #ccc;
			border-radius: 20px;
			margin: 0 10px;
			outline: none;
			background: #f5f5f5;
			display: block;
			position: relative;
			z-index: 100;
			-webkit-user-select: text; /* 允许选择文本 */
			user-select: text;
			pointer-events: auto !important;
			touch-action: manipulation !important;
			-webkit-appearance: none;
			-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
			user-select: text;
		}
		.processing-message {
			flex: 1;
			padding: 14px;
			border: 1px solid #ccc;
			border-radius: 20px;
			margin: 0 10px;
			outline: none;
			background: #f5f5f5;
			color: #999;
			display: block;
			opacity: 1;
			z-index: 23;
			position: relative;
		}
		.send-btn {
			width: 50px;
			height: 50px;
			background-color: #4285f4;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;

			.send-icon {
				width: 24px;
				height: 24px;
				background: url('@/assets/images/ai-list/ai_top.webp') no-repeat center center / contain;
			}
		}
	}
}

@keyframes wake-up-pulse {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.2);
	}
	100% {
		transform: scale(1);
	}
}
@keyframes wakeUpAnimation {
	0% {
		transform: scale(1); /* 初始状态 */
		opacity: 0.5; /* 初始透明度 */
	}
	50% {
		transform: scale(1.1); /* 中间放大 */
		opacity: 1; /* 完全不透明 */
	}
	100% {
		transform: scale(1); /* 最终恢复原状 */
		opacity: 1; /* 完全不透明 */
	}
}
.profile-text {
	width: 100%;
	font-size: 18px;
	color: #333; /* 根据你的主题调整颜色 */
}
.text-content {
	width: 350px;
	:deep(p) {
		margin: unset;
	}
}

.profile-wrapper {
	width: 80%;
	.profile-detail {
		margin-top: 8px;
		padding: 10px;
		background-color: #fff;
		border-radius: 5px;
		font-size: 18px;
		cursor: pointer;
		&:hover {
			background-color: #f5f5f5;
		}
		&-name {
			color: #262626;
		}
		&-job {
			color: #333333;
		}
	}
}
@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
@keyframes pulse {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.1);
	}
	100% {
		transform: scale(1);
	}
}
@keyframes wave {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.1);
		opacity: 0.8;
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}
</style>
