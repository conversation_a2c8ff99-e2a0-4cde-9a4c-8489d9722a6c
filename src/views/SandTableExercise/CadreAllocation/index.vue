<template>
	<div class="smart-selection">
		<header-back title="干部配置" />
		<div class="content">
			<!-- <SelectMenu @change="onSelectMenuChange" :menu="menuList" :tab-active-key="tabActiveKey" />
			<template v-if="tabActiveKey === 5">
				<div class="search-page">
					<condition />
				</div>
			</template> -->
			<!-- <div class="controll">
				<label class="checkbox-custom"> <a-checkbox v-model:checked="checked"></a-checkbox><span class="select-label">屏蔽处分影响期</span> </label>
				<div class="panne-c">
					<div :class="`tile ${panneType === panneMenu[0].key ? 'panne-active' : ''}`" @click="onPanneChange(panneMenu[0].key)">
						<img :src="panneType === panneMenu[0].key ? panneMenu[0].active_icon : panneMenu[0].icon" alt="" srcset="" />
						<span class="label-text">{{ panneMenu[0].label }}</span>
					</div>
					<div class="divied"></div>
					<div :class="`tile ${panneType === panneMenu[1].key ? 'panne-active' : ''}`" @click="onPanneChange(panneMenu[1].key)">
						<img :src="panneType === panneMenu[1].key ? panneMenu[1].active_icon : panneMenu[1].icon" alt="" srcset="" />
						<span class="label-text">{{ panneMenu[1].label }}</span>
					</div>
				</div>
			</div> -->
			<div class="table-box" v-memo="[datasource, tableScrollY, loading]" v-if="!isTile">
				<position-table
					:columns="columns"
					:data-source="datasource"
					:scroll="{ y: tableScrollY }"
					:pagination="false"
					:loading="loading"
					:on-location="onLocation"
					:cdn-url="CDN_URL"
					:table-scroll-y="tableScrollY"
					@dismissChange="onDismissChange"
					@aspireChange="onAsaspireChange"
					@drop-change="
						(keys, records) => {
							onDropChange(keys, records, 1)
						}
					"
				/>
			</div>
		</div>
		<div class="confirm">
			<a-button type="primary" @click="onSubmit">提交</a-button>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, inject } from 'vue'
import { useRoute } from 'vue-router'
import { CDN_URL } from '@/config/env'
import { historyPush } from '@/utils/history'
import { getUserList } from '@/apis/sand-table-exercise'
import { useSubmit } from '../mixins/event'
import { getUserInfoItem } from '@/utils/utils'
import { onDismissChange, onAsaspireChange, onDropChange } from '../mixins/event'
import useMock from '@/store/sandbox'
import { message } from 'ant-design-vue'
import PositionTable from '../components/PositionTable.vue'

import tilePng from '../images/tile.png'
import titleActive from '../images/tile-active.png'
import listPng from '../images/list.png'
import listActive from '../images/list-active.png'

type UserData = {
	userId: number
	userName: string
	headUrl?: string
	currentJob?: string
	currentJobTime?: string
	birthday?: string
	age?: number
	initDegree?: string
	initSchool?: string
	specialty?: string
	cadreIndex?: string
	cadreIndexSort?: string
	userType?: number // 1 - 市管领导，2 - 县管领导，3 - 中层干部
	highestDegree?: string
}

const panneMenu = [
	{
		label: '平铺',
		icon: tilePng,
		active_icon: titleActive,
		key: '1',
	},
	{
		label: '列表',
		icon: listPng,
		active_icon: listActive,
		key: '2',
	},
]

const menuList = [
	{
		label: '优中选优',
		key: 1,
	},
	{
		label: '指数领先',
		key: 2,
	},
	{
		label: '一贯优秀',
		key: 3,
	},
	{
		label: '当下优秀',
		key: 4,
	},
	{
		label: '条件查询',
		key: 5,
	},
]

const columns = [
	{
		key: 'headUrl',
		dataIndex: 'headUrl',
		align: 'center',
		width: '7%',
		title: '照片',
	},
	{
		key: 'userName',
		dataIndex: 'userName',
		align: 'center',
		width: '7%',
		title: '姓名',
		// colClass: blur.value ? 'filter-style' : '',
		colClass: 'cursor-pointer',
		colClick: (_data: any, event: any) => {
			event.stopPropagation()
		},
	},

	// {
	// 	key: 'portrait',
	// 	align: 'center',
	// 	width: '9%',
	// 	title: '干部画像',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'currentJob',
		dataIndex: 'currentJob',
		align: 'left',
		width: '20%',
		title: '现任职务',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'birthday',
		dataIndex: 'birthday',
		align: 'center',
		width: '10%',
		title: '出生年月（年龄）',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'currentJobTime',
		dataIndex: 'currentJobTime',
		align: 'center',
		width: '10%',
		title: '任现职务时间',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'initDegree',
		dataIndex: 'initDegree',
		align: 'center',
		width: '10%',
		title: '全日制学历',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'initSchool',
		dataIndex: 'initSchool',
		align: 'left',
		width: '10%',
		title: '毕业院校及专业',
		// colClass: blur.value ? 'filter-style' : '',
	},
	// {
	// 	key: 'specialty',
	// 	align: 'center',
	// 	width: '12%',
	// 	title: '专业',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'cadreIndex',
		dataIndex: 'cadreIndex',
		align: 'center',
		width: '6%',
		title: '干部指数',
	},
	{
		key: 'cadreIndexSort',
		dataIndex: 'cadreIndexSort',
		align: 'center',
		width: '6%',
		title: '序列排名',
	},
	{
		key: 'operate',
		dataIndex: 'operate',
		align: 'center',
		width: '15%',
		title: '操作',
	},
	// {
	// 	key: 'achievement',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '业绩',
	// },
	// {
	// 	key: 'ability',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '能力',
	// },
	// {
	// 	key: 'praise',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '口碑',
	// },
	// {
	// 	key: 'politics',
	// 	align: 'center',
	// 	width: '8%',
	// 	title: '政治',
	// 	customization: 'CustomBlock',
	// },
]
const mockData: UserData[] = Array.from({ length: 10 }, (_, index) => ({
	userId: index + 1,
	userName: `用户${index + 1}`,
	headUrl: `https://example.com/avatar${index + 1}.png`,
	pmsJobId: 101 + index,
	jobName: `职务${index + 1}`,
	currentJob: `现任职务${index + 1}`,
	currentJobTime: `2023-01-01`,
	birthday: `1990-01-01`,
	age: 30 + index,
	initDegree: `学历${index + 1}`,
	initSchool: `学校${index + 1}`,
	specialty: `专业${index + 1}`,
	cadreIndex: `干部指数${index + 1}`,
	cadreIndexSort: `干部指数同序列排名${index + 1}`,
	userType: (index % 3) + 1, // Alternating between 1, 2, 3
}))

const smartSelection = JSON.parse(sessionStorage.getItem('smart_selection') || '[]') as UserData[]

const submit = useSubmit()

const mock = useMock()
//
const route = useRoute()
//
const { mock_id } = route.query

const loading = ref(false)
//
const checked = ref(false)
//
const adminId = getUserInfoItem('_uid')
//
const mockId: any = ref(mock_id || mock.mockId)
//
const mockName = inject('mockName')
// 表格滚动值
const scrollTop = ref(0)
// 平铺 、列表切换
const panneType = ref(panneMenu[1].key)
// 表格可滚动
const tableScrollY = ref(0)
// 顶部tab切换
const tabActiveKey = ref(menuList[0].key)
// 数据源
const datasource = ref<Array<UserData>>(smartSelection)
// 屏蔽处分期影响
const filter = computed(() => {
	return checked.value ? '1' : ''
})

// 是否为平铺
const isTile = ref(panneType.value === panneMenu[0].key)

const onLocation = (data: any) => {
	// keepalive.push()

	historyPush(`/cadre-portrait/home?user_id=${data.userId}`)
}

const onPanneChange = (key: any) => {
	panneType.value = key
	setTimeout(() => {
		isTile.value = panneType.value === panneMenu[0].key
	}, 100)
}

const onSelectMenuChange = (key: any) => {
	tabActiveKey.value = key

	loadData()
}
const onSubmit = () => {
	const hasSelect: any = datasource.value.find((item: any) => {
		return item.operation === undefined
	})

	if (!!hasSelect) {
		return message.error(`${hasSelect.userName}未选择调整类型`)
	}

	submit({
		datasource: datasource.value,
		mockId: mockId.value,
		mockName,
	})
	console.log('🚀 ~ file: index.vue:319 ~ onSubmit ~ mockId:', mockId)
}

const loadData = async () => {
	const res: any = await getUserList(tabActiveKey.value as any, filter.value as any)
	if (res.code === 0) {
		datasource.value = res.data
	}
}

// loadData()

onMounted(() => {
	const head = document.querySelector('.ant-table-thead')
	const body = document.querySelector('.ant-table-body')
	const table = document.querySelector('.table-box')

	tableScrollY.value = table?.offsetHeight - head?.offsetHeight

	body?.addEventListener('scroll', (e: any) => {
		scrollTop.value = e.target.scrollTop
	})
})
</script>

<style lang="less" scoped>
.smart-selection {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.content {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		flex: 1;
		padding: 24px;
		background-color: #ffffff;
		overflow: hidden;
		.search-page {
			flex: 1;
			width: 100%;
			overflow: auto;
			background-color: #ffffff;
		}

		.controll {
			display: flex;
			justify-content: space-between;
			overflow-y: auto;
			.checkbox-custom {
				display: flex;
				align-items: center;
				.select-label {
					margin-left: 8px;
					font-size: 20px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: rgba(0, 0, 0, 0.85);
					line-height: 20px;
				}
			}
			.panne-c {
				display: flex;
				align-items: center;
				height: 27px;
				overflow: hidden;
				.divied {
					margin: 0px 25px;
					width: 1px;
					height: 18px;
					background: rgba(0, 0, 0, 0.4);
					border-radius: 9px 9px 9px 9px;
					opacity: 1;
				}
				.tile {
					display: flex;
					align-items: center;
					img {
						width: 27px;
						height: 27px;
						object-fit: contain;
					}
					.label-text {
						font-size: 20px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.9);
						line-height: 20px;
					}
				}
				.panne-active .label-text {
					color: #008eff;
				}
			}
		}

		.table-box {
			margin-top: 18px;
			flex: 1;
		}
	}
	.confirm {
		margin-top: 28px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;
		width: 100%;
		height: 102px;
		button {
			font-size: 21px;
			padding: 0px;
			width: 150px;
			height: 54px;
			background: #008eff;
			border-radius: 3px 3px 3px 3px;
			opacity: 1;
		}
	}
}
</style>
