<template>
	<a-config-provider :locale="zhCN">
		<a-drawer
			class="select-user-drawer"
			title="岗找人"
			:visible="visible"
			@close="onClose"
			@afterVisibleChange="afterVisibleChange"
			:closable="false"
			size="large"
		>
			<div class="inner">
				<SelectMenu @change="onSelectMenuChange" :menu="menu" :tab-active-key="tabKeys" />
				<div class="search-page" v-show="isSearch">
					<condition-component ref="condition" />
				</div>
				<template v-if="!isSearch">
					<div class="controll">
						<div class="check-box">
							<label class="checkbox-custom" v-show="tabKeys !== 5">
								<a-checkbox v-model:checked="checkValue.checked1" @change="loadUserList"></a-checkbox><span class="select-label">屏蔽处分影响期</span>
							</label>
							<label class="checkbox-custom" v-show="tabKeys !== 5">
								<a-checkbox v-model:checked="checkValue.checked2" @change="loadUserList"></a-checkbox
								><span class="select-label">主要社会关系任职回避</span>
							</label>
						</div>
						<!-- <label class="checkbox-custom">
							<a-checkbox v-model:checked="checked" @change="loadData"></a-checkbox><span class="select-label">屏蔽处分影响期</span>
						</label> -->
						<div class="panne-c">
							<div :class="`tile ${panneType === panneMenu[0].key ? 'panne-active' : ''}`" @click="onPanneChange(panneMenu[0].key)">
								<img :src="panneType === panneMenu[0].key ? panneMenu[0].active_icon : panneMenu[0].icon" alt="" srcset="" />
								<span class="label-text">{{ panneMenu[0].label }}</span>
							</div>
							<div class="divied"></div>
							<div :class="`tile ${panneType === panneMenu[1].key ? 'panne-active' : ''}`" @click="onPanneChange(panneMenu[1].key)">
								<img :src="panneType === panneMenu[1].key ? panneMenu[1].active_icon : panneMenu[1].icon" alt="" srcset="" />
								<span class="label-text">{{ panneMenu[1].label }}</span>
							</div>
						</div>
					</div>
					<div class="list-box" v-memo="[filterData, tableScrollY, loading]" v-if="!isTile">
						<position-table
							row-key="userId"
							:columns="columns"
							:datasource="filterData"
							:pagination="false"
							:loading="loading"
							:on-location="onLocation"
							:cdn-url="CDN_URL"
							:table-scroll-y="tableScrollY"
							:customRow="
									(record: any) => {
										return {
											class: record.select ? 'select-row' : '',
											onClick: () => {
												onSelectUserInfo(record)
											}, // 点击行
										}
									}
								"
						/>
					</div>
					<div class="tile-box" v-else ref="tileList">
						<user-info-box
							v-for="item in filterData"
							:key="item.userId"
							:user-info="item"
							:selected="!!item.select"
							@click="onSelectUserInfo(item)"
						/>
					</div>
				</template>
			</div>

			<template #footer>
				<div class="footer">
					<template v-if="isSearch">
						<a-button @click="onReset" type="default" class="reset">重置</a-button>
						<a-button type="primary" @click="onSearch" class="search">搜索</a-button>
					</template>
					<a-button type="primary" @click="onConfirm" v-else>确认</a-button>
				</div>
			</template>
		</a-drawer>
	</a-config-provider>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, nextTick, reactive } from 'vue'
import { CDN_URL } from '@/config/env'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import { historyPush } from '@/utils/history'
import { MeScrollInit, functionMap } from '@/utils/utils'
import { getUserList } from '@/apis/sand-table-exercise'
import { getPmsLeader } from '@/apis/search'

import PositionTable from './PositionTable.vue'
import SelectMenu from './SelectMenu.vue'
import ConditionComponent from '@/components/Condition.vue'
import UserInfoBox from './UserInfoBox.vue'

import tilePng from '../images/tile.png'
import titleActive from '../images/tile-active.png'
import listPng from '../images/list.png'
import listActive from '../images/list-active.png'

type JobInfo = {
	pmsJobId: number
	jobName: string
	select?: boolean
}

const props = defineProps({
	selectJobList: {
		type: Array,
		default: () => [],
	},
	// 同一组织下存在多个空缺岗位时，两个岗位不能出现同一个人
	canNotSelectUser: {
		type: Array,
		default: () => [],
	},
})
// 复选框
const checkValue = reactive({
	checked1: false,
	checked2: false,
})
const emits = defineEmits(['close', 'confirm'])

const visible = ref(true)

// 关闭
const onClose = () => {
	visible.value = false
}
// 确认
const onConfirm = () => {
	visible.value = false
	const selectUsers = datasource.value.filter((item: any) => {
		return item.select
	})
	emits('confirm', selectUsers)
}
// 过度结束后触发
const afterVisibleChange = (visible: boolean) => {
	visible || emits('close')
}

type UserData = {
	userId: number
	userName: string
	headUrl?: string
	currentJob?: string
	currentJobTime?: string
	birthday?: string
	age?: number
	initDegree?: string
	initSchool?: string
	specialty?: string
	cadreIndex?: string
	cadreIndexSort?: string
	userType?: number // 1 - 市管领导，2 - 县管领导，3 - 中层干部
	highestDegree?: string
	select?: boolean
}

const panneMenu = [
	{
		label: '平铺',
		icon: tilePng,
		active_icon: titleActive,
		key: '1',
	},
	{
		label: '列表',
		icon: listPng,
		active_icon: listActive,
		key: '2',
	},
]

const menu = [
	{
		label: '优中选优',
		key: 1,
	},
	{
		label: '指数领先',
		key: 2,
	},
	{
		label: '一贯优秀',
		key: 3,
	},
	{
		label: '当下优秀',
		key: 4,
	},
	{
		label: '条件查询',
		key: 5,
	},
]

// const route = useRoute()
// const { mock_id } = route.query as any
// 搜索
const isSearch = ref(false)
//
const loading = ref(false)
//
const checked = ref(false)
//
const condition = ref<any>({})
// 表格滚动值
const scrollTop = ref(0)
// 平铺 、列表切换
const panneType = ref(panneMenu[0].key)
// 表格可滚动
const tableScrollY = ref(undefined)
// 顶部tab切换
const tabKeys = ref(menu[0].key)
// 数据源
const datasource = ref<Array<UserData>>([])
// 勾选的数据
const selectData: any = ref([])
// 滚动容器
const mescroll1 = ref<any>({})
// 滚动加载
const currentPage = ref(1)
// 屏蔽处分期影响
const filter = computed(() => {
	const filter = []
	if (checkValue.checked1) {
		filter.push(1)
	}
	if (checkValue.checked2) {
		filter.push(2)
	}
	return filter.join(',')
})

// 是否为平铺
const isTile = ref(panneType.value === panneMenu[0].key)
// 根据props.selectJobList获取用户列表
const filterData = computed(() => {
	return datasource.value.filter((item: any) => {
		return (
			!props.selectJobList.find((user: any) => {
				return user.userId === item.userId
			}) && !props.canNotSelectUser.find((user: any) => user.userId === item.userId)
		)
	})
})

const onLocation = (data: any) => {
	// keepalive.push()

	historyPush(`/cadre-portrait/home?user_id=${data.userId}`)
}

const onPanneChange = (key: any) => {
	panneType.value = key

	isTile.value = panneType.value === panneMenu[0].key

	if (tabKeys.value === menu[4].key) {
		resetStatus()

		setTimeout(() => {
			initUpScroll(key)
		})
	}

	if (key === panneMenu[1].key) {
		setTimeout(() => {
			settableScroll()
		})
	}
}

const onSelectMenuChange = (key: any) => {
	tabKeys.value = key

	isSearch.value = tabKeys.value === 5
	// 切换时先清空数据
	datasource.value = []

	!isSearch.value && loadData()
}
const onSelect = (record: any, selected: any, selectedRows: any, nativeEvent: any) => {
	if (selected) {
		selectData.value.push(record)
	} else {
		const findIndex = selectData.value.findIndex((item: any) => {
			return item.userId === record.userId
		})

		selectData.value.splice(findIndex, 1)
	}
}

const columns = [
	{
		key: 'headUrl',
		dataIndex: 'headUrl',
		align: 'center',
		width: '7%',
		title: '照片',
	},
	{
		key: 'userName',
		dataIndex: 'userName',
		align: 'center',
		width: '10%',
		title: '姓名',
		// colClass: blur.value ? 'filter-style' : '',
		colClass: 'cursor-pointer',
		colClick: (_data: any, event: any) => {
			event.stopPropagation()
		},
	},

	// {
	// 	key: 'portrait',
	// 	align: 'center',
	// 	width: '9%',
	// 	title: '干部画像',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'currentJob',
		dataIndex: 'currentJob',
		align: 'left',
		width: '20%',
		title: '现任职务',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'birthday',
		dataIndex: 'birthday',
		align: 'center',
		width: '10%',
		title: '出生年月（年龄）',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'currentJobTime',
		dataIndex: 'currentJobTime',
		align: 'center',
		width: '10%',
		title: '任现职务时间',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'initDegree',
		dataIndex: 'initDegree',
		align: 'center',
		width: '10%',
		title: '全日制学历',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'initSchool',
		dataIndex: 'initSchool',
		align: 'left',
		width: '10%',
		title: '毕业院校及专业',
		// colClass: blur.value ? 'filter-style' : '',
	},
	// {
	// 	key: 'specialty',
	// 	align: 'center',
	// 	width: '12%',
	// 	title: '专业',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'cadreIndex',
		dataIndex: 'cadreIndex',
		align: 'center',
		width: '10%',
		title: '干部指数',
	},
	{
		key: 'cadreIndexSort',
		dataIndex: 'cadreIndexSort',
		align: 'center',
		width: '10%',
		title: '序列排名',
	},
]
// 选中个数
const selectNum = ref(props.selectJobList?.length || 0)

const onSelectUserInfo = (record: any) => {
	// 超过三个不可选
	if (!record.select && selectNum.value === 3) {
		return
	}
	record.select = !record.select

	if (record.select) {
		selectNum.value++
		selectData.value.push(record)
	} else {
		const findIndex = selectData.value.findIndex((item: any) => {
			return item.userId === record.userId
		})

		selectNum.value--
		selectData.value.splice(findIndex, 1)
	}
}
// 条件查询重置
const onReset = () => {
	condition.value.resetForm()
}

// 条件查询
const onSearch = () => {
	resetStatus()

	formState.value = condition.value.formState

	isSearch.value = false

	setTimeout(() => {
		initUpScroll(panneType.value)
	})
	// query(++currentPage.value)
}

const resetStatus = () => {
	currentPage.value = 1

	datasource.value = []

	mescroll1.value?.destroy?.()

	mescroll1.value = null
}

// formState
const formState = ref<any>({})

const initUpScroll = (type: string) => {
	let ele = type === '1' ? document.querySelector('.tile-box') : document.querySelector('.ant-table-body')

	console.log('🚀 ~ file: index.vue:668 ~ initUpScroll ~ ele:', ele)

	mescroll1.value = MeScrollInit(ele as any, {
		auto: true,
		// loadFull: { use: true, delay: 500 },
		callback: async () => {
			const res: any = await query(currentPage.value++)
			console.log('🚀 ~ file: index.vue:674 ~ callback: ~ totalPages:', res)
			if (res.code === 0) {
				const { content, totalPages }: any = res.data || {}
				console.log('🚀 ~ file: index.vue:674 ~ callback: ~ totalPages:', totalPages)

				mescroll1.value.endByPage(content, totalPages)
			}
		},
	})
}

const query = (page: number) =>
	new Promise((resolve) => {
		const _formState = formState.value
		const params: any = { ..._formState, page, page_size: 40 }

		if (checkValue.checked1) {
			params.punishment_flag = 1
		}

		if (checkValue.checked2) {
			params.relationship_flag = 1
		}

		if (params.birthday) {
			params.birthday_start = params.birthday[0]
			params.birthday_end = params.birthday[1]
			delete params.birthday
		}
		if (params.join_time) {
			params.join_time_start = params.join_time[0]
			params.join_time_end = params.join_time[1]
			delete params.join_time
		}
		if (params.join_time) {
			params.join_time_start = params.join_time[0]
			params.join_time_end = params.join_time[1]
			delete params.join_time
		}
		if (params.information) {
			params.information = params.information.value
		}
		if (params.responsibilities) {
			params.responsibilities = params.responsibilities.value
		}
		if (params.speciality) {
			params.speciality = params.speciality.value
		}
		if (params.label) {
			params.label = params.label.value
		}
		const _params: any = {}
		Object.entries(params).forEach(([key, value]) => {
			if (Array.isArray(value)) {
				if (value[0] !== undefined) {
					_params[key] = value
				}
				return false
			}
			_params[key] = value
		})
		getPmsLeader(_params).then((res: any) => {
			if (res.code === 0) {
				const { content }: any = res.data || {}

				if (content) {
					datasource.value.push(...content.map((item: any) => functionMap.transformData(item)))
				}

				resolve(res)
			}
		})
	})

const settableScroll = () => {
	const head = document.querySelector('.ant-table-thead')
	// const body = document.querySelector('.ant-table-body')
	const table = document.querySelector('.list-box')

	tableScrollY.value = table?.offsetHeight - head?.offsetHeight
	console.log('🚀 ~ file: SelectUserDrawer.vue:480 ~ settableScroll ~ tableScrollY.value:', tableScrollY.value)
}

const loadData = async () => {
	const res: any = await getUserList(tabKeys.value as any, filter.value as any)
	if (res.code === 0) {
		datasource.value = res.data
	}
}

loadData()

const loadUserList = () => {
	resetStatus()

	if (isSearch.value) {
		onSearch()
	} else {
		loadData()
	}
}

onMounted(() => {
	// body?.addEventListener('scroll', (e: any) => {
	// 	scrollTop.value = e.target.scrollTop
	// })
})
</script>

<style lang="less">
.select-user-drawer {
	.ant-drawer-content-wrapper {
		width: 70% !important;
	}
	.ant-drawer-header {
		height: 85px;
		flex-shrink: 0;
		.ant-drawer-title {
			font-size: 24px;
			line-height: 24px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #222222;
		}
	}

	.ant-drawer-body {
		padding: 0;
	}

	.inner {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		height: 100%;
		padding: 24px;
		background-color: #ffffff;
		overflow: hidden;
		.search-page {
			flex: 1;
			width: 100%;
			overflow-y: auto;
			background-color: #ffffff;
		}

		.controll {
			display: flex;
			justify-content: space-between;
			.check-box {
				display: flex;
			}
			.checkbox-custom {
				display: flex;
				align-items: center;
				.select-label {
					margin-left: 8px;
					font-size: 20px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: rgba(0, 0, 0, 0.85);
					line-height: 20px;
				}
			}
			.panne-c {
				display: flex;
				align-items: center;
				height: 27px;
				overflow: hidden;
				.divied {
					margin: 0px 25px;
					width: 1px;
					height: 18px;
					background: rgba(0, 0, 0, 0.4);
					border-radius: 9px 9px 9px 9px;
					opacity: 1;
				}
				.tile {
					display: flex;
					align-items: center;
					img {
						width: 27px;
						height: 27px;
						object-fit: contain;
					}
					.label-text {
						font-size: 20px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.9);
						line-height: 20px;
					}
				}
				.panne-active .label-text {
					color: #008eff;
				}
			}
		}

		.list-box {
			margin-top: 18px;
			flex: 1;
			overflow: hidden;
		}
		.tile-box {
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			flex: 1;
			width: 100%;
			// display: grid;
			// grid-template-columns: 1fr 1fr 1fr;
			gap: 20px;
			overflow-y: scroll;
			.userinfo-box {
				width: 32%;
			}
		}
	}
	.ant-drawer-footer {
		border-top-width: 12px;
		.footer {
			display: flex;
			justify-content: center;
			button {
				font-size: 21px;
				padding: 0px;
				width: 150px;
				height: 54px;
				background: #008eff;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
			}
			.reset {
				margin-right: 20px;
				width: 111px;
				height: 42px;
				background: #e8f4fd;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				font-size: 20px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #008eff;
			}
			.search {
				width: 111px;
				height: 42px;
				background: #008eff;
				border-radius: 3px 3px 3px 3px;
				opacity: 1;

				font-size: 20px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #ffffff;
			}
		}
	}

	.confirm {
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;
		width: 100%;
		height: 102px;
	}
}
.select-row {
	background-color: rgba(237, 247, 255, 0.9);
}
</style>
