<template>
	<div class="look-for">
		<div class="look-for-box">
			<div class="user-box">
				<div class="left">
					<header-back :title="$route.query.org_name" />
					<div class="controll">
						<div class="top-box">
							<div class="group">
								<span class="sub-title">班子队伍</span>
								<!-- <span class="link-button refresh">刷新</span> -->
							</div>
						</div>
						<div class="bottom-box">
							<div class="group">
								应配：{{ userInfoList.deserve }}人，缺配：<span class="yellow-text">{{ userInfoList.vacancy }}</span
								>人
							</div>
							<div class="group">班子结构预警项：<span class="yellow-text"> 年龄、学历、专业</span></div>

							<span class="link-button" @click="onStructure"> 班子结构对比 &gt; </span>
						</div>
					</div>
					<div class="lf-user-box">
						<div
							:class="`user-item ${selectUser.userId && selectUser.userId === item.userId ? 'active-select' : ''} ${
								selectUser.index === index ? 'active-select' : ''
							}`"
							v-for="(item, index) in userInfoList.users"
							:key="item.userId"
							@click="onUserSelect(item, index)"
						>
							<img :src="`${item.headUrl ? `${CDN_URL}/fr_img/${item.headUrl}` : defaultAvatar}`" class="avatar" />
							<div class="user-name">{{ item.userName || '---' }}</div>
							<div class="user-position">{{ item.jobName }}</div>
						</div>
						<!-- <div :class="`user-item ${pageStatus.newAdd ? 'active-select' : ''}`" @click="onAdd" v-if="userInfoList.vacancy">
							<div class="img-box">
								<img src="../images/vacancy.png" class="vacancy" />
							</div>
							<div class="user-name">---</div>
							<div class="user-position">---</div>
						</div> -->
					</div>
					<div class="lf-button-box">
						<!-- <a-button @click="onConfirm">返回上一步</a-button> -->
						<a-button @click="onRefreshPage">重置</a-button>
						<a-button type="primary" @click="onConfirm">确认调整方案</a-button>
					</div>
				</div>
			</div>
			<div class="right">
				<div class="smart-title">一键智选 <span class="tips-text">(注：查找选用干部，选中即替换左侧班子队伍中选中的干部)</span></div>
				<div class="top-box">
					<select-menu :tab-active-key="tabKeys" :menu="menu" @change="onTabChange" />
					<div class="controll">
						<div class="check-box">
							<label class="checkbox-custom" v-show="tabKeys !== 5">
								<a-checkbox v-model:checked="checkValue.checked1" @change="loadUserList"></a-checkbox><span class="select-label">屏蔽处分影响期</span>
							</label>
							<label class="checkbox-custom" v-show="tabKeys !== 5">
								<a-checkbox v-model:checked="checkValue.checked2" @change="loadUserList"></a-checkbox
								><span class="select-label">主要社会关系任职回避</span>
							</label>
						</div>
						<div class="panne-c">
							<div :class="`tile ${panneType === panneMenu[0].key ? 'panne-active' : ''}`" @click="onPanneChange(panneMenu[0].key)">
								<img :src="panneType === panneMenu[0].key ? panneMenu[0].active_icon : panneMenu[0].icon" alt="" srcset="" />
								<span class="label-text">{{ panneMenu[0].label }}</span>
							</div>
							<div class="divied"></div>
							<div :class="`tile ${panneType === panneMenu[1].key ? 'panne-active' : ''}`" @click="onPanneChange(panneMenu[1].key)">
								<img :src="panneType === panneMenu[1].key ? panneMenu[1].active_icon : panneMenu[1].icon" alt="" srcset="" />
								<span class="label-text">{{ panneMenu[1].label }}</span>
							</div>
						</div>
					</div>
				</div>
				<div :class="`bottom-box ${isSearch ? 'search-overflow-hidden' : ''}`">
					<div class="tile-box" v-if="isTile" ref="tileList">
						<user-info-box
							v-for="item in filterUser"
							:key="item.userId"
							:user-info="item"
							:selected="!!waitSelectUser.userId && item.userId === waitSelectUser.userId"
							@click="onWaitUserSelect(item)"
						/>
					</div>
					<div class="list-box" v-else>
						<position-table
							class="list-box__table"
							:columns="columns"
							:datasource="filterUser"
							:cdn-url="CDN_URL"
							:customRow="
									(record: any) => {
										return {
											onClick: () => {
												onWaitUserSelect(record)
											}, // 点击行
										}
									}
								"
						>
						</position-table>
					</div>
					<div class="search-box" v-if="isSearch">
						<condition ref="condition" />
					</div>
				</div>
				<div class="button-box" v-if="isSearch">
					<a-button @click="onReset">重置</a-button>
					<a-button type="primary" @click="onSearch">查询</a-button>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { computed, reactive, ref, unref, inject, nextTick, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { CDN_URL } from '@/config/env'
import SelectMenu from '../components/SelectMenu.vue'
import UserInfoBox from '../components/UserInfoBox.vue'
import PositionTable from '../components/PositionTable.vue'
import Condition from '@/components/Condition.vue'
import { confirmModal, compareModal } from '../components/Drawer'
import { getOrgTeam, getUserList } from '@/apis/sand-table-exercise'
import { getPmsLeader } from '@/apis/search'
import { getUserInfoItem, MeScrollInit } from '@/utils/utils'
import tilePng from '../images/tile.png'
import titleActive from '../images/tile-active.png'
import listPng from '../images/list.png'
import listActive from '../images/list-active.png'
import defaultAvatar from '@/assets/images/avatar-width.png'
type OrgInfo = {
	orgId?: number
	orgName?: string
	deserve: number
	vacancy: number
	users: UserInfo[]
}

type UserInfo = {
	userId: number
	userName: string
	headUrl?: string
	pmsJobId: number
	jobName: string
	currentJob?: string
	currentJobTime?: string
	birthday?: string
	age?: number
	initDegree?: string
	initSchool?: string
	specialty?: string
	cadreIndex?: string
	cadreIndexSort?: string
	userType?: number // 1 - 市管领导，2 - 县管领导，3 - 中层干部
	index?: number
}

type UserData = {
	userId: number
	userName: string
	headUrl?: string
	currentJob?: string
	currentJobTime?: string
	birthday?: string
	age?: number
	initDegree?: string
	initSchool?: string
	specialty?: string
	cadreIndex?: string
	cadreIndexSort?: string
	userType?: number // 1 - 市管领导，2 - 县管领导，3 - 中层干部
	highestDegree?: string
}

const panneMenu = [
	{
		label: '平铺',
		icon: tilePng,
		active_icon: titleActive,
		key: '1',
	},
	{
		label: '列表',
		icon: listPng,
		active_icon: listActive,
		key: '2',
	},
]

const menu = [
	{
		label: '优中选优',
		key: 1,
	},
	{
		label: '指数领先',
		key: 2,
	},
	{
		label: '一贯优秀',
		key: 3,
	},
	{
		label: '当下优秀',
		key: 4,
	},
	{
		label: '条件查询',
		key: 5,
	},
]

const columns = [
	{
		key: 'headUrl',
		dataIndex: 'headUrl',
		align: 'center',
		width: '7%',
		title: '照片',
	},
	{
		dataIndex: 'userName',
		title: '姓名',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'birthday',
		title: '出生年月',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'cadreIndex',
		title: '干部指数',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'cadreIndexSort',
		title: '序列排名',
		width: '10%',
		align: 'center',
	},
	{
		dataIndex: 'currentJob',
		title: '现任职务',
		width: '10%',
		align: 'center',
	},
]
// 路由
const route = useRoute()

const { org_id, mock_id, job_id, user_id } = route.query as any

const router = useRouter()
//
const mockId = inject('mockId')
//
const onRefreshPage: any = inject('refreshPage')
// 登录用户id
const adminId = getUserInfoItem('_uid')
// 条件查询
const condition = ref()
// tabkeys
const tabKeys = ref(menu[0].key)
// 搜索功能
const isSearch = ref(false)
// 平铺 、列表切换
const panneType = ref(panneMenu[1].key)
// 是否为平铺
const isTile = ref(panneType.value === panneMenu[0].key)
// 选中的用户
const selectUser = ref<UserInfo>({} as UserInfo)
// 右侧选中用户
const waitSelectUser = ref<UserData>({} as UserData)

// 滚动加载列表
const tileList = ref()
// 复选框
const checkValue = reactive({
	checked1: false,
	checked2: false,
})
// 页面状态管理
const pageStatus = reactive({
	waitUserCheck: false, // 右侧用户池能否点击
	newAdd: false, // 新增用户
})

const mockData: UserInfo[] = Array.from({ length: 10 }, (_, index) => ({
	userId: index + 1,
	userName: `用户${index + 1}`,
	headUrl: `https://example.com/avatar${index + 1}.png`,
	pmsJobId: 101 + index,
	jobName: `职务${index + 1}`,
	currentJob: `现任职务${index + 1}`,
	currentJobTime: `2023-01-01`,
	birthday: `1990-01-01`,
	age: 30 + index,
	initDegree: `学历${index + 1}`,
	initSchool: `学校${index + 1}`,
	specialty: `专业${index + 1}`,
	cadreIndex: `干部指数${index + 1}`,
	cadreIndexSort: `干部指数同序列排名${index + 1}`,
	userType: (index % 3) + 1, // Alternating between 1, 2, 3
}))

const userInfoList = ref<OrgInfo>({
	orgId: undefined,
	orgName: `机构`,
	deserve: 0, //应配
	vacancy: 0, // 缺配
	users: [],
})
// Generate ten mock data entries
const mockUserData1: UserData[] = Array.from({ length: 10 }, (_, index) => ({
	userId: index + 1,
	userName: `用户${index + 1}`,
	headUrl: `https://example.com/avatar${index + 1}.png`,
	currentJob: `现任职务${index + 1}`,
	currentJobTime: `2023-01-01`,
	birthday: `1990-01-01`,
	age: 30 + index,
	initDegree: `全日制学历${index + 1}`,
	initSchool: `全日制学校${index + 1}`,
	specialty: `专业${index + 1}`,
	cadreIndex: `干部指数${index + 1}`,
	cadreIndexSort: `干部指数同序列排名${index + 1}`,
	userType: (index % 3) + 1, // Alternating between 1, 2, 3
	highestDegree: `最高学历${index + 1}`,
}))
// 右边查询列表
const searchList = ref<Array<UserData>>([])
// 待配池子
const waitMached = ref<UserData[]>([])
// 待配表格数据
const waitMacheTableData = ref<UserData[]>([])
// 调配列表
const changeList = ref<any>([])
// formState
const formState = ref<any>({})
// 右侧显示人员过滤
const filterUser = computed(() =>
	searchList.value.filter((item: any) => {
		return (
			userInfoList.value.users?.findIndex((item1: any) => {
				return item.userId === item1.userId
			}) === -1
		)
	})
)

// tab切换
const onTabChange = (key: any) => {
	tabKeys.value = key

	if (key === menu[4].key) {
		isSearch.value = true
	} else {
		isSearch.value = false
	}

	if (key !== menu[4].key) {
		loadUserList()
	}
}

const onPanneChange = (key: any) => {
	panneType.value = key

	isTile.value = panneType.value === panneMenu[0].key

	if (tabKeys.value === menu[4].key) {
		resetStatus()

		setTimeout(() => {
			initUpScroll(key)
		})
	}
}
/**
 * @description: 用户选择
 * @param {*} user
 * @return {*}
 */
const onUserSelect = (user: any) => {
	pageStatus.waitUserCheck = true

	selectUser.value = user

	waitSelectUser.value = {} as UserData
}
const onWaitUserSelect = (user: UserData) => {
	if (pageStatus.newAdd) {
		pageStatus.newAdd = false

		userInfoList.value.users?.push(user as any)

		return
	}
	// 左侧职务没选中时右侧选人不能点击
	if (pageStatus.waitUserCheck) {
		// 代配目录

		// orgId	number	是	机构id
		// userId	number	是	用户id
		// operation	number	否	操作（-1-其他 0-提拔 1-进一步使用 2-交流 3-试用期满转正 4-任免兼挂职 5-不再担任领导职务）
		// onPmsJobId	number	否	任职职务id
		// onProposedAppointJobSupple	Stirng	否	任职职务-其他
		// offPmsJobId	number	否	免职职务id
		// offProposedAppointJobSupple	Stirng	否	免职职务-其他
		// primaryUserId	number	否	在岗找人界面被替换的用户id
		// 将右侧数据替换到左侧

		const _userInfoList = unref(userInfoList)
		// 用户信息
		const { userName, userId, headUrl } = user
		const { pmsJobId, userId: primaryUserId, jobName, index } = selectUser.value

		const baseInfo = {
			userName,
			userId,
		}
		const jobInfo = {
			jobName,
			pmsJobId,
		}
		// 新替换的用户
		const replaceUserInfo = Object.assign({ headUrl, index }, baseInfo, jobInfo)
		// 是否在
		const selectIndex = waitMached.value.findIndex((item: any) => item.index === selectUser.value.index)

		if (selectIndex === -1) {
			// 空缺职位，修改对应字段
			waitMached.value.push(selectUser.value)
		}
		// 找到原数据的位置
		// const index = _userInfoList.users?.findIndex((item) => {
		// 	if (primaryUserId) {
		// 		return item.userId === primaryUserId
		// 	} else {
		// 		console.log('🚀 ~ file: index.vue:415 ~ index ~ item.pmsJobId === pmsJobId', item.pmsJobId, pmsJobId)
		// 		return item.pmsJobId === pmsJobId
		// 	}
		// })
		console.log('🚀 ~ file: index.vue:418 ~ index ~ index:', index)

		if (index !== undefined && index > -1) {
			// 替换
			userInfoList.value.users?.splice(index, 1, replaceUserInfo)

			const newUserInfo = {
				...baseInfo,
				jobName,
				onPmsJobId: pmsJobId,
				operation: undefined,
				onProposedAppointJobSupple: undefined,
				primaryUserId, // 被替换掉的用户id
				...user,
				index,
			}

			waitSelectUser.value = user
			// changeList中是否存在
			const _index = changeList.value.findIndex((item: any) => {
				console.log(
					'🚀 ~ file: index.vue:450 ~ const_index=changeList.value.findIndex ~ item.onPmsJobId === pmsJobId && item.index === index:',
					item.onPmsJobId,
					pmsJobId,
					item.index,
					index
				)
				return item.onPmsJobId === pmsJobId && item.index === index
			})
			// 替换过的用户再次被替换
			if (_index !== -1) {
				changeList.value.splice(_index, 1, newUserInfo)
			} else {
				changeList.value.push(newUserInfo)
			}
		}
	}
}
const functionMap = {
	determineUserType(job: string): number {
		return 1
	},
	transformData(input: any) {
		const userData: UserData = {
			userId: input.user_id,
			userName: input.username,
			headUrl: input.head_url,
			currentJob: input.current_job,
			birthday: input.birthday,
			age: parseInt(input.age),
			initDegree: input.diploma,
			initSchool: input.school,
			cadreIndex: input.cadre_index,
			userType: functionMap.determineUserType(input.current_job),
			highestDegree: input.diploma, // Adjust as needed based on your data
		}
		return userData
	},
}
const onConfirm = () => {
	confirmModal.openModal({
		orgId: org_id,
		mockId: mock_id,
		adminId,
		beforeData: changeList.value,
		afterData: waitMached.value,
	})
}

const onStructure = () => {
	const userIds = userInfoList.value.users
		.filter((item: any) => item.userId)
		.map((item: any) => item.userId)
		.join(',')

	compareModal.openModal({
		orgId: org_id,
		mockId: mock_id,
		userIds,
	})
}
// 条件查询重置
const onReset = () => {
	condition.value.resetForm()
}
// 条件查询
const onSearch = () => {
	resetStatus()

	formState.value = condition.value.formState

	isSearch.value = false

	setTimeout(() => {
		initUpScroll(panneType.value)
	})
	// query(++currentPage.value)
}

const query = (page: number) =>
	new Promise((resolve) => {
		const _formState = formState.value
		const params: any = { ..._formState, page }

		if (checkValue.checked1) {
			params.punishment_flag = 1
		}

		if (checkValue.checked2) {
			params.relationship_flag = 1
		}

		if (params.birthday) {
			params.birthday_start = params.birthday[0]
			params.birthday_end = params.birthday[1]
			delete params.birthday
		}
		if (params.join_time) {
			params.join_time_start = params.join_time[0]
			params.join_time_end = params.join_time[1]
			delete params.join_time
		}
		if (params.join_time) {
			params.join_time_start = params.join_time[0]
			params.join_time_end = params.join_time[1]
			delete params.join_time
		}
		if (params.information) {
			params.information = params.information.value
		}
		if (params.responsibilities) {
			params.responsibilities = params.responsibilities.value
		}
		if (params.speciality) {
			params.speciality = params.speciality.value
		}
		if (params.label) {
			params.label = params.label.value
		}
		const _params: any = {}
		Object.entries(params).forEach(([key, value]) => {
			if (Array.isArray(value)) {
				if (value[0] !== undefined) {
					_params[key] = value
				}
				return false
			}
			_params[key] = value
		})
		getPmsLeader(_params).then((res: any) => {
			if (res.code === 0) {
				const { content }: any = res.data || {}

				if (content) {
					searchList.value.push(...content.map((item: any) => functionMap.transformData(item)))
				}

				resolve(res)
			}
		})
	})
// 数据初始化
const loadOrgTeam = async () => {
	const res: any = await getOrgTeam(org_id, mock_id)
	if (res.code === 0) {
		res.data.users = res.data.users.map((item: any, index: number) => {
			const user = {
				...item,
				index,
			}
			// 表示空缺
			if (job_id && Number(job_id) === item.pmsJobId && !item.userId) {
				onUserSelect(user)
			}
			// 点击预警用户进入
			if (user_id && Number(user_id) === item.userId) {
				onUserSelect(user)
			}
			return user
		})

		userInfoList.value = res.data
	}
}

loadOrgTeam()

const loadUserList = async () => {
	const filter = []
	if (checkValue.checked1) {
		filter.push(1)
	}
	if (checkValue.checked2) {
		filter.push(2)
	}
	const res: any = await getUserList(String(tabKeys.value), filter.join(','))

	if (res.code === 0) {
		searchList.value = res.data
	}
}
loadUserList()
/**
 * @description: 上拉加载更多
 * @return {*}
 */
const mescroll1 = ref()

const currentPage = ref(1)

const initUpScroll = (type: string) => {
	let ele = type === '1' ? document.querySelector('.tile-box') : document.querySelector('.list-box')

	mescroll1.value = MeScrollInit(ele as any, {
		auto: true,
		loadFull: { use: true },
		callback: async () => {
			const res: any = await query(currentPage.value++)
			console.log('🚀 ~ file: index.vue:674 ~ callback: ~ totalPages:', res)
			if (res.code === 0) {
				const { content, totalPages }: any = res.data || {}
				console.log('🚀 ~ file: index.vue:674 ~ callback: ~ totalPages:', totalPages)

				mescroll1.value.endByPage(content, totalPages)
			}
		},
	})
}

const resetStatus = () => {
	currentPage.value = 1

	searchList.value = []

	mescroll1.value?.destroy?.()

	mescroll1.value = null
}
const tableScrollY = ref()

onMounted(() => {
	nextTick(() => {
		const head: any = document.querySelector('.ant-table-thead')
		const body: any = document.querySelector('.ant-table-body')
		const table: any = document.querySelector('.list-box')

		tableScrollY.value = table?.clientHeight - head?.offsetHeight
	})
})
</script>

<style lang="less" scoped>
.flex-direction-column {
	display: flex;
	flex-direction: column;
}
.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}
.gray-bg {
	background: #f5f5f5;
}
.white-bg {
	background: #ffffff;
}
.look-for {
	width: 100%;
	height: 100%;
	.flex-direction-column;
	.look-for-box {
		.gray-bg;
		display: flex;
		flex: 1;
		width: 100%;
		overflow: hidden;
		.sub-header {
			.white-bg;
			padding: 24px 25px;
			font-size: 24px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #222222;
			line-height: 24px;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			border-bottom: 1px solid #d9d9d9;
		}
		.user-box {
			// flex: 1;
			// display: flex;
			// overflow: hidden;
			.left {
				.flex-direction-column;
				margin-right: 20px;
				height: 100%;
				width: 876px;
				overflow: hidden;
				.controll {
					padding: 24px 25px 0px;
					.white-bg;
					.top-box {
						display: flex;
						justify-content: space-between;
						.group {
							display: flex;
							align-items: center;
							.sub-title {
								display: flex;
								align-items: center;
								font-size: 24px;
								font-family: Source Han Sans CN, Source Han Sans CN;
								font-weight: bold;
								color: #222222;
								line-height: 28px;
								&::before {
									margin-right: 12px;
									content: '';
									display: inline-block;
									width: 6px;
									height: 28px;
									background: url('../images/title-1.png') no-repeat center / cover;
								}
							}

							.reset {
								&::before {
									background-image: url('../images/reset.png');
								}
							}
							.refresh {
								&::before {
									background-image: url('../images/refresh.png');
								}
							}
						}
					}
					.bottom-box {
						padding: 0px 15px;
						margin-top: 24px;
						display: flex;
						align-items: center;
						height: 50px;
						background: #fcf6ec;
						border-radius: 3px 3px 3px 3px;
						opacity: 1;
						.group {
							display: flex;
							align-items: center;
							font-size: 21px;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							color: #000000;
							&:nth-child(2) {
								margin-left: 27px;
							}
							&::before {
								content: '';
								margin-right: 9px;
								display: inline-block;
								width: 9px;
								height: 9px;
								background: #e6a23c;
								border-radius: 50%;
								opacity: 1;
							}
							.yellow-text {
								color: #e6a23c;
							}
						}

						.link-button {
							flex: 1;
							text-align: right;
							font-size: 21px;
							line-height: 21px;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							color: #008eff;
							cursor: pointer;
						}
					}
				}
				.lf-user-box {
					.white-bg;
					display: flex;
					flex-wrap: wrap;
					align-content: flex-start;
					padding: 14px 20px;
					flex: 1;
					overflow-y: auto;
					gap: 0px 29px;

					.user-item {
						.white-bg;
						padding: 12px 0px;
						display: flex;
						align-items: center;
						flex-direction: column;
						width: 183px;
						height: 314px;
						// overflow: hidden;
						border-radius: 3px 3px 3px 3px;
						opacity: 1;
						.avatar {
							width: 122px;
							height: 152px;
							object-fit: fill;
						}
						.img-box {
							.flex-center;
							width: 100%;
							height: 140px;
							background-color: #eeeeee;
							.vacancy {
								width: 41px;
								height: 41px;
								object-fit: contain;
							}
						}
						.user-name {
							margin-top: 10px;
							text-align: center;
							font-size: 23px;
							line-height: 26px;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 500;
							color: #000000;
						}
						.user-position {
							margin-top: 10px;
							text-align: center;
							font-size: 20px;
							line-height: 27px;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							color: rgba(0, 0, 0, 0.9);
							// 超过两行省略
							display: -webkit-box;
							text-overflow: -o-ellipsis-lastline;
							overflow: hidden;
							text-overflow: ellipsis;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 3;
						}
					}
					.active-select {
						background: #edf7ff;
						.user-position {
							color: #008eff;
						}
						.user-name {
							color: #008eff;
						}
					}
				}
				.lf-button-box {
					.white-bg;
					margin-top: 12px;
					display: flex;
					align-items: center;
					justify-content: center;
					height: 71px;
					border-radius: 0px 0px 0px 0px;
					opacity: 1;
					gap: 30px;
					.ant-btn {
						padding: 0px;
						width: 141px;
						height: 42px;
						border-radius: 3px 3px 3px 3px;
						opacity: 1;
						.ant-btn-primary {
						}
					}
				}
			}
		}
		.right {
			flex: 1;
			height: 100%;
			display: flex;
			flex-direction: column;
			overflow: hidden;
			background: #ffffff;
			.smart-title {
				padding-left: 24px;
				display: flex;
				align-items: center;
				font-size: 24px;
				height: 89px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				color: #222222;
				line-height: 28px;
				border-bottom: 1px solid #d9d9d9;
				&::before {
					margin-right: 12px;
					content: '';
					display: inline-block;
					width: 6px;
					height: 28px;
					background: url('../images/title-1.png') no-repeat center / cover;
				}

				.tips-text {
					margin-left: 15px;
					font-weight: 400;
					font-size: 21px;
					color: rgba(0, 0, 0, 0.45);
					line-height: 25px;
					text-align: left;
					font-style: normal;
					text-transform: none;
				}
			}
			.top-box {
				.white-bg;
				padding: 25px 24px 18px;
				.controll {
					display: flex;
					justify-content: space-between;
					overflow-y: auto;
					.check-box {
						display: flex;
						.checkbox-custom {
							margin-right: 37px;
							display: flex;
							align-items: center;
							.select-label {
								margin-left: 8px;
								font-size: 20px;
								font-family: Source Han Sans CN, Source Han Sans CN;
								font-weight: 400;
								color: rgba(0, 0, 0, 0.85);
								line-height: 20px;
							}
						}
					}

					.panne-c {
						display: flex;
						align-items: center;
						height: 27px;
						overflow: hidden;
						.divied {
							margin: 0px 25px;
							width: 1px;
							height: 18px;
							background: rgba(0, 0, 0, 0.4);
							border-radius: 9px 9px 9px 9px;
							opacity: 1;
						}
						.tile {
							display: flex;
							align-items: center;
							img {
								width: 27px;
								height: 27px;
								object-fit: contain;
							}
							.label-text {
								font-size: 20px;
								font-family: Source Han Sans CN, Source Han Sans CN;
								font-weight: 400;
								color: rgba(0, 0, 0, 0.9);
								line-height: 20px;
							}
						}
						.panne-active .label-text {
							color: #008eff;
						}
					}
				}
			}
			.bottom-box {
				position: relative;
				flex: 1;
				width: 100%;
				overflow: hidden;
				.tile-box {
					.white-bg;
					display: flex;
					align-content: flex-start;
					flex-wrap: wrap;
					gap: 15px 18px;
					padding: 18px;
					width: 100%;
					height: 100%;
					overflow-y: scroll;
				}
				.list-box {
					.white-bg;
					padding: 18px;
					width: 100%;
					height: 100%;
					overflow-y: auto;
				}
				:deep(.userinfo-box) {
					width: 49%;
				}
				.search-box {
					.white-bg;
					padding: 18px;
					position: absolute;
					inset: 0px;
					overflow-y: auto;
				}
				@media screen and (max-width: 750px) {
					:deep(.userinfo-box) {
						width: 100%;
					}
				}
			}
			.button-box {
				.flex-center;
				.white-bg;
				margin-top: 12px;
				gap: 62px;
				width: 100%;
				height: 71px;
				border-radius: 0px 0px 0px 0px;
				opacity: 1;
				button {
					font-size: 20px;
					padding: 0px;
					width: 111px;
					height: 42px;
					border-radius: 3px 3px 3px 3px;
					opacity: 1;
				}
			}
			// .search-overflow-hidden {
			// 	overflow: hidden;
			// 	height: 100%;
			// }
		}
	}
}
</style>
