<template>
	<div class="page1">
		<a-radio-group v-model:value="type" :options="options" @change="onRadioChange" />
		<div class="tips">
			本次干部调整共涉及单位 {{ data.count.orgCount }} 个，涉及干部 {{ data.count.userCount }} 人。其中提拔 {{ data.count.promoteCount }} 人（女性
			{{ data.count.femaleCount }} 人，70后 {{ data.count.age70Count }} 人，80后 {{ data.count.age80Count }} 人，90后
			{{ data.count.age90Count }} 人，35岁以下 {{ data.count.ageUnder35Count }} 人，36岁-40岁 {{ data.count.age36To39Count }} 人，40岁-50岁
			{{ data.count.age40To49Count }} 人，50岁以上 {{ data.count.ageOver50Count }} 人。全日制大学 {{ data.count.undergraduateCount }} 人，全日制研究生
			{{ data.count.graduateCount }} 人）；进一步使用 {{ data.count.furtherUseCount }} 人；交流
			{{ data.count.communicationCount }} 人；不再担任领导职务 {{ data.count.removeCount }} 人。
		</div>
		<div class="table-box">
			<row-table :columns="columns" :datasource="dataSource">
				<template #title="{ value }">
					<span>{{ value.type }}</span>
				</template>
				<template #jobVacancy="{ value }">
					<span>缺配：{{ value.data.jobVacancy }}人</span>
				</template>
				<template #ageStructure="{ value }">
					<div>
						<div>
							<span> 平均年龄：{{ value.data.ageAvg }} </span>
						</div>
						<div class="age-box">
							<span>
								35岁以下：{{ value.data.ageUnder35Percent }}%
								<i v-if="value.data.ageUnder35" :class="`${value.data.ageUnder35 === 1 ? 'status-icon-up' : 'status-icon-down'}`"></i
							></span>
							<span>
								35~40岁：{{ value.data.age35To39Percent }}%<i
									v-if="value.data.age35To39"
									:class="`${value.data.age35To39 === 1 ? 'status-icon-up' : 'status-icon-down'}`"
								></i>
							</span>
							<span>
								40~45岁：{{ value.data.age40To44Percent }}%
								<i v-if="value.data.age40To44" :class="`${value.data.age40To44 === 1 ? 'status-icon-up' : 'status-icon-down'}`"></i
							></span>
							<span>
								45~55岁：{{ value.data.age45To55Percent }}%
								<i v-if="value.data.age45To55" :class="`${value.data.age45To55 === 1 ? 'status-icon-up' : 'status-icon-down'}`"></i
							></span>
							<span>
								55岁以上：{{ value.data.ageOver55Percent }}%
								<i v-if="value.data.ageOver55" :class="`${value.data.ageOver55 === 1 ? 'status-icon-up' : 'status-icon-down'}`"></i
							></span>
						</div>
					</div>
				</template>
				<template #genderStructure="{ value }">
					<div>
						<span style="margin-right: 30px">男：{{ value.data.menPercent }}%</span>
						<span>女：{{ value.data.menPercent }}%</span>
					</div>
				</template>
				<template #educationStructure="{ value }">
					<span
						>本科及以上：{{ value.data.degreePercent }}%

						<i v-if="value.data.degree" :class="`${value.data.degree === 1 ? 'status-icon-up' : 'status-icon-down'}`"></i>
					</span>
				</template>
				<template #politicalOutlook="{ value }">
					<span
						>中共党员：{{ value.data.partyMemberPercent }}%
						<i v-if="value.data.partyMember" :class="`${value.data.partyMember === 1 ? 'status-icon-up' : 'status-icon-down'}`"></i
					></span>
				</template>
				<template #ethnicStructure="{ value }">
					<span
						>少数民族：{{ value.data.minorityPercent }}%
						<i v-if="value.data.minority" :class="`${value.data.minority === 1 ? 'status-icon-up' : 'status-icon-down'}`"></i>
					</span>
				</template>
			</row-table>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, inject } from 'vue'
import { planAnalyze } from '@/apis/sand-table-exercise'
import RowTable from '@/components/RowTable.vue'

interface UserData {
	orgCount: number
	userCount: number
	promoteCount: number
	furtherUseCount: number
	communicationCount: number
	removeCount: number
	femaleCount: number
	age70Count: number
	age80Count: number
	age90Count: number
	ageUnder35Count: number
	age36To39Count: number
	age40To49Count: number
	ageOver50Count: number
	undergraduateCount: number
	graduateCount: number
}
interface JobStatistics {
	jobVacancy: number
	ageAvg: string
	ageUnder35Percent: number
	age35To39Percent: number
	age40To44Percent: number
	age45To55Percent: number
	ageOver55Percent: number
	menPercent: number
	womenPercent: number
	degreePercent: number
	partyMemberPercent: number
	minorityPercent: number
	ageUnder35: number
	age35To39: number
	age40To44: number
	age45To55: number
	ageOver55: number
	men: number
	women: number
	degree: number
	partyMember: number
	minority: number
}

interface ResponseType {
	count: UserData
	before: JobStatistics
	after: JobStatistics
}
const options = [
	{ label: '全部 ', value: '11' },
	{ label: '县管正职 ', value: '12' },
	{ label: '县管副职 ', value: '13' },
]

const type = ref(options[0].value)
// 生成假数据
const yourData = {
	jobVacancy: 100,
	ageAvg: '30',
	ageUnder35Percent: 20,
	age35To39Percent: 30,
	age40To44Percent: 25,
	age45To55Percent: 15,
	ageOver55Percent: 10,
	menPercent: 40,
	womenPercent: 60,
	degreePercent: 70,
	partyMemberPercent: 15,
	minorityPercent: 5,
	ageUnder35: 1,
	age35To39: 0,
	age40To44: 2,
	age45To55: 1,
	ageOver55: 0,
	men: 1,
	women: 2,
	degree: 0,
	partyMember: 1,
	minority: 0,
}
const data = ref<ResponseType>({
	count: {} as UserData,
	before: {} as JobStatistics,
	after: {} as JobStatistics,
})

const mock_id: any = inject('mock_id')

const columns = [
	{
		title: '方案分析',
		dataIndex: 'title',
		key: 'title',
		align: 'center',
		rowClass: 'table-title-gray',
	},
	{
		title: '职数配置情况',
		dataIndex: 'jobVacancy',
		key: 'jobVacancy',
		align: 'center',
	},
	{
		title: '年龄结构',
		dataIndex: 'ageStructure',
		key: 'ageStructure',
		align: 'left',
	},
	{
		title: '性别结构',
		dataIndex: 'genderStructure',
		key: 'genderStructure',
		align: 'center',
	},
	{
		title: '学历结构',
		dataIndex: 'educationStructure',
		key: 'educationStructure',
		align: 'center',
	},
	{
		title: '政治面貌',
		dataIndex: 'politicalOutlook',
		key: 'politicalOutlook',
		align: 'center',
	},
	{
		title: '民族结构',
		dataIndex: 'ethnicStructure',
		key: 'ethnicStructure',
		align: 'center',
	},
]

const dataSource: any = ref([])

function classifyData(data: any, title: any) {
	const result = {
		title: {
			type: title,
		},
		jobVacancy: {
			type: '职数配置情况',
			data: {
				jobVacancy: data.jobVacancy,
			},
		},
		ageStructure: {
			type: '年龄结构',
			data: {
				ageAvg: data.ageAvg,
				ageUnder35Percent: data.ageUnder35Percent,
				age35To39Percent: data.age35To39Percent,
				age40To44Percent: data.age40To44Percent,
				age45To55Percent: data.age45To55Percent,
				ageOver55Percent: data.ageOver55Percent,
				ageUnder35: data.ageUnder35,
				age35To39: data.age35To39,
				age40To44: data.age40To44,
				age45To55: data.age45To55,
				ageOver55: data.ageOver55,
			},
		},
		genderStructure: {
			type: '性别结构',
			data: {
				menPercent: data.menPercent,
				womenPercent: data.womenPercent,
				men: data.men,
				women: data.women,
			},
		},
		educationStructure: {
			type: '学历结构',
			data: {
				degreePercent: data.degreePercent,
				degree: data.degree,
			},
		},
		politicalOutlook: {
			type: '政治面貌',
			data: {
				partyMemberPercent: data.partyMemberPercent,
			},
		},
		ethnicStructure: {
			type: '民族结构',
			data: {
				minorityPercent: data.minorityPercent,
			},
		},
		// 可根据需要添加更多分类
	}
	return result
}
const onRadioChange = () => {
	loadData()
}
// 加载数据
const loadData = async () => {
	// dataSource.value = [classifyData(yourData), classifyData(yourData)]

	const res: any = await planAnalyze('1', type.value, mock_id)

	if (res.code === 0) {
		const data = res.data as ResponseType
		const { before, after } = data
		dataSource.value = [classifyData(before, '人员调配前'), classifyData(after, '人员调配后')]
	}
}

loadData()
</script>

<style lang="less" scoped>
.tips {
	margin-top: 31px;
	font-size: 17px;
	font-family: Source Han Sans CN, Source Han Sans CN;
	font-weight: 400;
	color: #333333;
}
.table-box {
	margin-top: 18px;
	margin-left: 38px;
	flex: 1;
	:deep(.ant-table) {
		.ant-table-thead {
			.ant-table-cell {
				background-color: #e9e9e9;
			}
		}
		.ant-table-row {
			.ant-table-cell:nth-child(1) {
				background-color: #e9e9e9;
			}
		}
	}
	.age-box {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		span {
			display: inline-block;
			width: 33.3%;
		}
	}
	.status-icon-up {
		display: inline-block;
		width: 15px;
		height: 15px;
		background: url('../../images/up.png') no-repeat center / cover;
	}
	.status-icon-down {
		display: inline-block;
		width: 15px;
		height: 15px;
		background: url('../../images/down.png') no-repeat center / cover;
	}
}
</style>
