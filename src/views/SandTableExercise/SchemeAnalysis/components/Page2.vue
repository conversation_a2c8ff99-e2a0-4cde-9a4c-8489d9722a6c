<template>
	<div class="page2">
		<sequence-select :org-list="dataList" @change="onChange" />
		<div class="table-box">
			<row-table :columns="columns" :datasource="dataSource">
				<template #title="{ value }">
					<span>{{ value.type }}</span>
				</template>
				<template #jobVacancy="{ value }">
					<span>缺配：{{ value.data.jobVacancy }}人</span>
				</template>
				<template #ageStructure="{ value }">
					<div>
						<div>
							<span> 平均年龄：{{ value.data.ageAvg }} </span>
						</div>
						<div class="age-box">
							<span>
								35岁以下：{{ value.data.ageUnder35Percent }}%
								<i v-if="value.data.ageUnder35" :class="`${value.data.ageUnder35 === 1 ? 'status-icon-up' : 'status-icon-down'}`"></i
							></span>
							<span>
								35~40岁：{{ value.data.age35To39Percent }}%<i
									v-if="value.data.age35To39"
									:class="`${value.data.age35To39 === 1 ? 'status-icon-up' : 'status-icon-down'}`"
								></i>
							</span>
							<span>
								40~45岁：{{ value.data.age40To44Percent }}%
								<i v-if="value.data.age40To44" :class="`${value.data.age40To44 === 1 ? 'status-icon-up' : 'status-icon-down'}`"></i
							></span>
							<span>
								45~55岁：{{ value.data.age45To55Percent }}%
								<i v-if="value.data.age45To55" :class="`${value.data.age45To55 === 1 ? 'status-icon-up' : 'status-icon-down'}`"></i
							></span>
							<span>
								55岁以上：{{ value.data.ageOver55Percent }}%
								<i v-if="value.data.ageOver55" :class="`${value.data.ageOver55 === 1 ? 'status-icon-up' : 'status-icon-down'}`"></i
							></span>
						</div>
					</div>
				</template>
				<template #genderStructure="{ value }">
					<div>
						<span style="margin-right: 30px">男：{{ value.data.menPercent }}%</span>
						<span>女：{{ value.data.menPercent }}%</span>
					</div>
				</template>
				<template #educationStructure="{ value }">
					<span
						>本科及以上：{{ value.data.degreePercent }}%

						<i v-if="value.data.degree" :class="`${value.data.degree === 1 ? 'status-icon-up' : 'status-icon-down'}`"></i>
					</span>
				</template>
				<template #politicalOutlook="{ value }">
					<span
						>中共党员：{{ value.data.partyMemberPercent }}%
						<i v-if="value.data.partyMember" :class="`${value.data.partyMember === 1 ? 'status-icon-up' : 'status-icon-down'}`"></i
					></span>
				</template>
				<template #ethnicStructure="{ value }">
					<span
						>少数民族：{{ value.data.minorityPercent }}%
						<i v-if="value.data.minority" :class="`${value.data.minority === 1 ? 'status-icon-up' : 'status-icon-down'}`"></i>
					</span>
				</template>
			</row-table>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, inject } from 'vue'
import SequenceSelect from '../../components/SequenceSelect.vue'
import { planAnalyze } from '@/apis/sand-table-exercise'

interface UserData {
	orgCount: number
	userCount: number
	promoteCount: number
	furtherUseCount: number
	communicationCount: number
	removeCount: number
	femaleCount: number
	age70Count: number
	age80Count: number
	age90Count: number
	ageUnder35Count: number
	age36To39Count: number
	age40To49Count: number
	ageOver50Count: number
	undergraduateCount: number
	graduateCount: number
}
interface JobStatistics {
	jobVacancy: number
	ageAvg: string
	ageUnder35Percent: number
	age35To39Percent: number
	age40To44Percent: number
	age45To55Percent: number
	ageOver55Percent: number
	menPercent: number
	womenPercent: number
	degreePercent: number
	partyMemberPercent: number
	minorityPercent: number
	ageUnder35: number
	age35To39: number
	age40To44: number
	age45To55: number
	ageOver55: number
	men: number
	women: number
	degree: number
	partyMember: number
	minority: number
}

interface ResponseType {
	count: UserData
	before: JobStatistics
	after: JobStatistics
}
const mock_id: any = inject('mock_id')

const columns = [
	{
		title: '方案分析',
		dataIndex: 'title',
		key: 'title',
		align: 'center',
		rowClass: 'table-title-gray',
	},
	{
		title: '职数配置情况',
		dataIndex: 'jobVacancy',
		key: 'jobVacancy',
		align: 'center',
	},
	{
		title: '年龄结构',
		dataIndex: 'ageStructure',
		key: 'ageStructure',
		align: 'left',
	},
	{
		title: '性别结构',
		dataIndex: 'genderStructure',
		key: 'genderStructure',
		align: 'center',
	},
	{
		title: '学历结构',
		dataIndex: 'educationStructure',
		key: 'educationStructure',
		align: 'center',
	},
	{
		title: '政治面貌',
		dataIndex: 'politicalOutlook',
		key: 'politicalOutlook',
		align: 'center',
	},
	{
		title: '民族结构',
		dataIndex: 'ethnicStructure',
		key: 'ethnicStructure',
		align: 'center',
	},
]
// 生成假数据
const yourData = {
	jobVacancy: 100,
	ageAvg: '30',
	ageUnder35Percent: 20,
	age35To39Percent: 30,
	age40To44Percent: 25,
	age45To55Percent: 15,
	ageOver55Percent: 10,
	menPercent: 40,
	womenPercent: 60,
	degreePercent: 70,
	partyMemberPercent: 15,
	minorityPercent: 5,
	ageUnder35: 1,
	age35To39: 0,
	age40To44: 2,
	age45To55: 1,
	ageOver55: 0,
	men: 1,
	women: 2,
	degree: 0,
	partyMember: 1,
	minority: 0,
}
const data = ref<ResponseType>({
	count: {} as UserData,
	before: yourData as JobStatistics,
	after: yourData as JobStatistics,
})
const dataList = ref<any>([
	{
		id: 1,
		name: '乡镇街道',
		children: [
			{
				id: 1,
				name: '沿长江产城景融台发展区',
				select: false,
			},
			{
				id: 2,
				name: '北部现代农业示范区',
				select: false,
			},
			{
				id: 3,
				name: '南部高山旅游度假区',
				select: false,
			},
		],
	},
	{
		id: 2,
		name: '部门',
		children: [
			{
				id: 5,
				name: '经济部门',
			},
			{
				id: 4,
				name: '保障部门',
			},
			{
				id: 6,
				name: '执纪执法部门',
			},
			{
				id: 7,
				name: '中央市级在丰单位及重点企业',
			},
			{
				id: 8,
				name: '县属国有企业',
			},
			{
				id: 9,
				name: '学校',
			},
			{
				id: 10,
				name: '医院',
			},
		],
	},
])
const dataSource: any = ref([])

function classifyData(data: any) {
	const result = {
		title: {
			type: '人员调配前',
		},
		jobVacancy: {
			type: '职数配置情况',
			data: {
				jobVacancy: data.jobVacancy,
			},
		},
		ageStructure: {
			type: '年龄结构',
			data: {
				ageAvg: data.ageAvg,
				ageUnder35Percent: data.ageUnder35Percent,
				age35To39Percent: data.age35To39Percent,
				age40To44Percent: data.age40To44Percent,
				age45To55Percent: data.age45To55Percent,
				ageOver55Percent: data.ageOver55Percent,
				ageUnder35: data.ageUnder35,
				age35To39: data.age35To39,
				age40To44: data.age40To44,
				age45To55: data.age45To55,
				ageOver55: data.ageOver55,
			},
		},
		genderStructure: {
			type: '性别结构',
			data: {
				menPercent: data.menPercent,
				womenPercent: data.womenPercent,
				men: data.men,
				women: data.women,
			},
		},
		educationStructure: {
			type: '学历结构',
			data: {
				degreePercent: data.degreePercent,
				degree: data.degree,
			},
		},
		politicalOutlook: {
			type: '政治面貌',
			data: {
				partyMemberPercent: data.partyMemberPercent,
			},
		},
		ethnicStructure: {
			type: '民族结构',
			data: {
				minorityPercent: data.minorityPercent,
			},
		},
		// 可根据需要添加更多分类
	}
	return result
}

const onChange = (type: any) => {
	loadData(type)
}

// 加载数据
const loadData = async (type: any) => {
	// dataSource.value = [classifyData(yourData), classifyData(yourData)]

	const res: any = await planAnalyze('2', type, mock_id)

	if (res.code === 0) {
		const data = res.data as ResponseType
		const { before, after } = data
		dataSource.value = [classifyData(before), classifyData(after)]
	}
}

loadData(11)
</script>

<style lang="scss" scoped>
.page2 {
	display: flex;
	width: 100%;
	height: 100%;
	.table-box {
		margin-left: 38px;
		flex: 1;
		:deep(.ant-table) {
			.ant-table-thead {
				.ant-table-cell {
					background-color: #e9e9e9;
				}
			}
			.ant-table-row {
				.ant-table-cell:nth-child(1) {
					background-color: #e9e9e9;
				}
			}
		}
	}

	:deep(.table-title-gray) {
		background-color: #f5f5f5;
	}
	.status-icon-up {
		display: inline-block;
		width: 15px;
		height: 15px;
		background: url('../../images/up.png') no-repeat center / cover;
	}
	.status-icon-down {
		display: inline-block;
		width: 15px;
		height: 15px;
		background: url('../../images/down.png') no-repeat center / cover;
	}
}
</style>
