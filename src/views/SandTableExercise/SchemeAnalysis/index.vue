<template>
	<div class="smart-selection">
		<header-back title="方案分析" />
		<div class="content">
			<select-menu :tab-active-key="tabKeys" :menu="menu" @change="onTabChange" />
			<div class="page-box">
				<component :is="page[tabKeys]" />
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, provide } from 'vue'
import { useRoute } from 'vue-router'
import SelectMenu from '../components/SelectMenu.vue'
import Page1 from './components/Page1.vue'
import Page2 from './components/Page2.vue'
import Page3 from './components/Page3.vue'

const menu = [
	{
		label: '全县',
		key: 1,
	},
	{
		label: '按序列',
		key: 2,
	},
	{
		label: '班子',
		key: 3,
	},
]

const page: any = {
	1: Page1,
	2: Page2,
	3: Page3,
}
const tabKeys = ref<any>(menu[0].key)

const route = useRoute()

const onTabChange = (key: any) => {
	tabKeys.value = key
}

provide('mock_id', route.query.mock_id)
</script>

<style lang="less" scoped>
.smart-selection {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;

	.content {
		padding: 24px;
		flex: 1;
		background-color: #ffffff;
		display: flex;
		flex-direction: column;
		overflow: hidden;

		.page-box {
			flex: 1;
			width: 100%;
			background-color: #ffffff;
			overflow: hidden;
		}
	}
}

:deep(.table-title-gray) {
	background-color: #f5f5f5;
}
</style>
