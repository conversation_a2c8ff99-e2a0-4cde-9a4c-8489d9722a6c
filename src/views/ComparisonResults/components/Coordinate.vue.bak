<template>
	<div class="coordinate">
		<v-chart :option="option" autoresize ref="echartsRef"></v-chart>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, toRefs } from 'vue'
// 0-全县, 1-本单位, 2-同序列, 3-全部乡镇/部门
const menu = [
	{
		label: '本单位',
		key: 1,
	},
	{
		label: '同序列',
		key: 2,
	},
	{
		label: '乡镇（部门）',
		key: 3,
	},
	{
		label: '全县',
		key: 0,
	},
]
// const self = [91, 28.8]
export default defineComponent({
	name: 'Coordinate',
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	setup(props) {
		const { data } = toRefs(props)
		const echartsRef = ref<any>(null)
		// const data = ref<CoordinateDataType>({
		// 	mine: {
		// 		x: 0,
		// 		y: 0,
		// 		user_id: 0,
		// 		name: '',
		// 	},
		// 	others: [],
		// 	x_avg: 0,
		// 	y_avg: 0,
		// 	type: undefined,
		// })

		const option = computed(() => {
			const color = ['#17EBC7', '#FFBA55', '#37AEFF']
			let x_avg = 0
			let y_avg = 0
			let _data: any = []
			// 处理数据
			let series = data.value?.map((item: any, index: number) => {
				const { mine } = item

				const data = [mine.x, mine.y, mine.name, mine.user_id]
				x_avg += mine.x
				y_avg += mine.y
				_data.push(data)
				return {
					name: mine.name,
					type: 'scatter',
					data: [data],
					itemStyle: {
						color: color[index],
					},
					// markLine: {
					// 	symbol: 'none',
					// 	label: { show: false },
					// 	silent: true,
					// 	data: [
					// 		{
					// 			type: 'average',
					// 			name: '平均值',
					// 		},
					// 		{
					// 			xAxis: x_avg,
					// 		},
					// 	],
					// },
				}
			})
			x_avg = x_avg / data.value.length
			y_avg = y_avg / 3
			const _option = {
				color: ['#17EBC7'],
				grid: {
					top: '13%',
					left: '0%',
					right: '5%',
					bottom: '3%',
					containLabel: true,
					show: false,
				},
				legend: {
					show: true,
					// selectedMode: false,
					textStyle: {
						color: '#ffffff',
						fontSize: 12,
					},
					itemGap: 40,
					itemWidth: 8,
					itemHeight: 8,
				},
				tooltip: {
					trigger: 'item',
					showDelay: 0,
					show: true,
					formatter: function (name: any) {
						const { value } = name
						return `
							<div class="tooltips-box">
								<div class="data-name">${value[2]}</div>
								<div class="data-item">业绩：${value[0] || ''}</div>
								<div class="data-item">能力：${value[1] || ''}</div>
							</div>
						`
					},
				},
				xAxis: {
					name: '业绩',
					type: 'value',
					scale: true,
					axisLabel: {
						color: '#2898E5',
						formatter: (value: any) => Number(value).toFixed(2),
						showMaxLabel: false,
					},
					axisLine: {
						show: true,
						lineStyle: {
							color: '#163F91',
						},
					},
					splitLine: {
						show: false,
					},

					// min: 50,
					max: 100,
					// splitNumber: 4,
					// 刻度线
					axisTick: {
						show: true,
					},
					nameTextStyle: {
						color: '#23DBFC',
						// align: 'left',
						padding: [7, 0, 0, -40],
						verticalAlign: 'top',
					},
				},
				yAxis: {
					name: '能力',
					type: 'value',
					// scale: true,
					axisLabel: {
						color: '#2898E5',
						formatter: (value: any) => Number(value).toFixed(2),
						showMaxLabel: false,
					},
					splitLine: {
						show: true,
						lineStyle: {
							color: '#163F91',
							type: 'dashed',
						},
					},
					axisLine: {
						show: true,
						lineStyle: {
							color: '#163F91',
						},
					},
					axisTick: {
						show: false,
					},
					nameTextStyle: {
						color: '#23DBFC',
						// align: 'right',
						padding: [0, 20, -20, -20],
					},
					// min: 50,
					max: 100,
					splitNumber: 7,
				},
				series: [
					...series,
					{
						name: '',
						type: 'scatter',
						data: _data,
						symbol: () => {
							return 'none'
						},
						markLine: {
							symbol: 'none',
							label: { show: false },
							silent: true,
							data: [
								{
									type: 'average',
									name: '平均值',
								},
								{
									xAxis: x_avg,
								},
							],
						},
					},
				],
				//  [
				// 	{
				// 		name: '杨亚荣',
				// 		type: 'scatter',
				// 		data: otherData,
				// 		symbol: (value: Array<number>) => {
				// 			if (value[0] === selfData[0] && value[1] === selfData[1]) {
				// 				return 'none'
				// 			} else {
				// 				return 'circle'
				// 			}
				// 		},
				// 		markLine: {
				// 			symbol: 'none',
				// 			label: { show: false },
				// 			silent: true,
				// 			data: [
				// 				{
				// 					type: 'average',
				// 					name: '平均值',
				// 				},
				// 				{
				// 					xAxis: x_avg,
				// 				},
				// 			],
				// 		},
				// 	},
				// 	{
				// 		name: '秦苍',
				// 		type: 'scatter',
				// 		data: [selfData],
				// 		itemStyle: {
				// 			color: '#FFBA55',
				// 		},
				// 		markLine: {
				// 			symbol: 'none',
				// 			label: { show: false },
				// 			silent: true,
				// 			data: [
				// 				[
				// 					{
				// 						coord: [0, 0],
				// 						lineStyle: {
				// 							width: 1,
				// 							type: 'solid',
				// 							color: '#FFBA55',
				// 						},
				// 					},
				// 					{
				// 						coord: selfData,
				// 						lineStyle: {
				// 							width: 2,
				// 							type: 'solid',
				// 							color: '#FFBA55',
				// 						},
				// 					},
				// 				],
				// 				[
				// 					{
				// 						coord: [0, selfData[1]],
				// 						lineStyle: {
				// 							width: 1,
				// 							type: 'solid',
				// 							color: '#FFBA55',
				// 						},
				// 					},
				// 					{
				// 						coord: selfData,
				// 						lineStyle: {
				// 							width: 1,
				// 							type: 'solid',
				// 							color: '#FFBA55',
				// 						},
				// 					},
				// 				],
				// 				[
				// 					{
				// 						coord: [selfData[0], 0],
				// 						lineStyle: {
				// 							width: 1,
				// 							type: 'solid',
				// 							color: '#FFBA55',
				// 						},
				// 					},
				// 					{
				// 						coord: selfData,
				// 						lineStyle: {
				// 							width: 1,
				// 							type: 'solid',
				// 							color: '#ccc',
				// 						},
				// 					},
				// 				],
				// 			],
				// 		},
				// 	},
				// ],
			}

			return _option
		})

		return {
			menu,
			option,
			echartsRef,
		}
	},
})
</script>

<style scoped lang="less">
.data-menu {
	display: flex;
	.menu-item {
		margin-left: 15px;
		font-size: 14px;
		font-weight: 400;
		color: #9e9e9e;
		cursor: pointer;
	}
	.menu-active {
		color: #00eaff;
	}
}

.coordinate {
	width: 100%;
	height: 329px;
}
</style>
