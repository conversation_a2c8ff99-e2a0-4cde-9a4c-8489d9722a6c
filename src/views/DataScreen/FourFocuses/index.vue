<template>
  <div :class="{'all-page':true,'page-bottom':isDetail}">
    <div class="title">班子巡察</div>
    <div class="org-title" @click="onBack">
      <ArrowLeftOutlined class="org-icon" />
      <div class="org-name">巡查班子：{{route.query.org_name}}</div>
    </div>
    <div class="line"></div>
    <div class="content">
      <div class="content-tabs">
        <div :class="{'tabs-name':true,'active':index===active}" @click="active = index" v-for="(item,index) in dataList" v-if="dataList.length!==0">{{item.content_optimize}}</div>
      </div>
      <div class="content-topic">
        <div class="topic-item" v-for="(item,index) in dataList[active].inspection_item_entity_list" v-if="dataList[active]&&dataList[active].inspection_item_entity_list&&dataList[active].inspection_item_entity_list.length!==0">
          <div class="topic-title">
            <div class="title-num">{{item.content.slice(0,1)}}</div>
            <div class="title-detail">{{item.content.slice(2)}}</div>
          </div>
          <div class="question-item" v-for="(question,num) in item.inspection_item_entity_list" v-if="item.inspection_item_entity_list&&item.inspection_item_entity_list.length!==0">
            <img class="question-delete" v-if="!question.inspection_item_id&&!isDetail" @click="deleteQuestion(item,num)" src="./images/delete.png"/>
            <div class="question-title" v-if="!isEdit([active,index,num])">{{question.question}}<img v-if="!question.inspection_item_id&&!isDetail" @click="editQuestion([active,index,num])" src="./images/edit.png"></div>
            <div class="patrol add-question" v-show="isEdit([active,index,num])">
              <div class="patrol-label">巡察问题标题：</div>
              <a-input v-model:value="question.question" ref="inputRefs" :disabled="isDetail" @blur="onBlur" :class="{'patrol-input':true,'isError':filterError([active,index,num,'title'])}" placeholder="请输入" />
            </div>
            <div class="patrol add-question">
              <div class="patrol-label">巡察报告具体问题：</div>
              <div class="patrol-input" v-if="isDetail">{{question.specific_issues}}</div>
              <a-input v-model:value="question.specific_issues"  v-if="!isDetail" :class="{'patrol-input':true,'isError':filterError([active,index,num,'question'])}" placeholder="请输入" />
            </div>
            <div class="team-leader">
              <div class="team">
                <div class="team-label">班子扣分：</div>
                <div class="patrol-input" v-if="isDetail">{{question.org_reduce_score}}</div>
                <a-input-number v-model:value="question.org_reduce_score"  v-if="!isDetail" :class="{'patrol-input':true,'isError':filterError([active,index,num,'score'])}" placeholder="请输入" />
              </div>
              <div class="leader">
                <div class="leader-label">主要领导责任：</div>
                <div class="leader-name" v-if="question.primary_user_list" v-for="(leader,leaderIndex) in question.primary_user_list">{{leader.user_name||leader.name}}{{leaderIndex===question.primary_user_list.length-1?'':'、'}}</div>
                <a-button type="primary" @click="selectLeader(question,'primary')" v-if="!isDetail" class="leader-btn">选择</a-button>
              </div>
              <div class="leader">
                <div class="leader-label">重要领导责任：</div>
                <div class="leader-name" v-if="question.important_user_list" v-for="(leader,leaderIndex) in question.important_user_list">{{leader.user_name||leader.name}}{{leaderIndex===question.important_user_list.length-1?'':'、'}}</div>
                <a-button type="primary" @click="selectLeader(question,'important')" v-if="!isDetail" class="leader-btn">选择</a-button>
              </div>
              <div class="leader">
                <div class="leader-label">直接责任：</div>
                <div class="leader-name" v-if="question.direct_user_list" v-for="(leader,leaderIndex) in question.direct_user_list">{{leader.user_name||leader.name}}{{leaderIndex===question.direct_user_list.length-1?'':'、'}}</div>
                <a-button type="primary" @click="selectLeader(question,'direct')" v-if="!isDetail" class="leader-btn">选择</a-button>
              </div>
            </div>
          </div>
          <div class="other-question" v-if="!isDetail" @click="addQuestion(item)">
            <plus-outlined />
            <span>其他问题</span>
          </div>
        </div>
      </div>
      <div class="bottom-btn"  v-if="!isDetail">
        <a-button type="primary" class="submit-topic" :loading="btnLoading" @click="submitQuestion">提交</a-button>
      </div>
    </div>
    <a-drawer
    v-model:visible="visible"
    class="custom-leader"
    title="选择"
    :closable="false"
    @close="closeVisible"
    placement="right"
    >
      <div class="custom-content">
        <div class="leader-checkbox-item">
          <a-checkbox-group v-model:value="leaderList" style="width: 100%">
            <div class="leader-checkbox" v-for="item in orgUserList">
              <a-checkbox :value="item.org_user_team_id" :key="item.org_user_team_id">{{item.user_name}}，{{item.current_job}}</a-checkbox>
            </div>
          </a-checkbox-group>
        </div>
        <a-button type="primary" @click="handleLeader" class="leader-commit">提交</a-button>
      </div>
    </a-drawer>
  </div>
</template>

<script lang="ts" setup>
  import { useRouter, useRoute } from 'vue-router'
  import { reactive, ref, inject, onActivated,onMounted } from 'vue'
  import { ArrowLeftOutlined,PlusOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue'
  import {getFourFocusDetail,addFourFocusDetail,getOrgUserList} from '@/apis/data-screen'
  const active = ref<any>(0)
  const isDetail = ref<any>(false)
  const leaderList = ref<any>([])
  const visible = ref<any>(false)
  const orgUserList = ref<any>([])
  const dataList = <any>ref([])
  const router = useRouter()
  const route = useRoute()
  const errorList = ref<any>([])
  const editList = ref<any>([])
  const inputRefs = ref(null);
  const dataItem = reactive({})
  const leaderType = ref('')
  const btnLoading = ref(false)
  onMounted(() =>{
    console.log(route.query);
    if(route.query.type){
      console.log("jl");
      isDetail.value = true
    }
    getOrgUser()
    loadData()
  })
  // 数据加载
  const loadData = async () => {
    const{year,org_id} = route.query
    const res = await getFourFocusDetail({org_id, year})
    if (res.code === 0) {
      const { data: _data }: any = res
      dataList.value = []
      console.log(_data);
      Object.keys(_data).forEach(key => {
        dataList.value.push(_data[key])
      });
    }
  }
  const getOrgUser = async () =>{
    const{org_id} = route.query
    const res = await getOrgUserList(org_id)
    if(res.code === 0){
      orgUserList.value = []
      orgUserList.value.push(...res.data)
    }
  }
  const filterError = (arr) =>{
    return errorList.value.some(subArray => JSON.stringify(subArray) === JSON.stringify(arr));
  }
  const isEdit = (arr) =>{
    return JSON.stringify(editList.value) === JSON.stringify(arr)
  }
  const addQuestion = (item) =>{
    console.log(item);
    item.inspection_item_entity_list.push({})
  }
  const deleteQuestion = (item,index) =>{
    item.inspection_item_entity_list.splice(index,1)
  }
  const editQuestion = (arr) =>{
    editList.value = [...arr]
    const inputRef = inputRefs.value[arr[2]];
    console.log(inputRef);
    if (inputRef) {
      setTimeout(() =>{
        inputRef.focus();
      },300)
    }
  }
  const selectLeader = (item,type) => {
    visible.value = true
    dataItem.value = item
    leaderType.value = type
    switch (type) {
      case 'primary':{
        leaderList.value = item.primary_user_list?item.primary_user_list.map(item =>item.org_user_team_id):[]
        break;
      }
      case 'important':{
        leaderList.value = item.important_user_list?item.important_user_list.map(item =>item.org_user_team_id):[]
        break;
      }
      case 'direct':{
        leaderList.value = item.direct_user_list?item.direct_user_list.map(item =>item.org_user_team_id):[]
        break;
      }
      default:
        break;
    }
  }
  const handleLeader = () => {
    let filteredObjects = orgUserList.value.filter(obj => leaderList.value.includes(obj.org_user_team_id));
    console.log(filteredObjects);
    switch (leaderType.value) {
      case 'primary':{
        dataItem.value.primary_user_list = filteredObjects
        break;
      }
      case 'important':{
        dataItem.value.important_user_list = filteredObjects
        break;
      }
      case 'direct':{
        dataItem.value.direct_user_list = filteredObjects
        break;
      }
      default:
        break;
    }
    console.log(dataItem);
    visible.value = false
    leaderList.value = []
  }
  const closeVisible = () =>{
    visible.value = false
    leaderList.value = []
  }
  const onBlur = () =>{
    editList.value = []
  }
  const submitQuestion = async () =>{
    const{year,org_id} = route.query
    console.log(dataList.value);
    let result = []
    errorList.value = []
    dataList.value.forEach((item,index) =>{
      item.inspection_item_entity_list.forEach((towItem,towIndex) =>{
        towItem.inspection_item_entity_list.forEach((threeItem,threeIndex)=>{
          // if(!threeItem.question&&!threeItem.content){
          //   errorList.value.push([index,towIndex,threeIndex,'title'])
          // }
          // if(!threeItem.specific_issues){
          //   errorList.value.push([index,towIndex,threeIndex,'question'])
          // }
          // if(!threeItem.org_reduce_score&&threeItem.org_reduce_score!==0){
          //   errorList.value.push([index,towIndex,threeIndex,'score'])
          // }
          result.push({
            org_inspection_id:threeItem.org_inspection_id,
            one_inspection_item_id:item.inspection_item_id,
            two_inspection_item_id:towItem.inspection_item_id,
            inspection_item_id:threeItem.inspection_item_id,
            org_id,
            year,
            org_reduce_score:threeItem.org_reduce_score || 0,
            question:threeItem.question || threeItem.content,
            specific_issues:threeItem.specific_issues,
            direct_user_ids:threeItem.direct_user_list?threeItem.direct_user_list.map(item=>item.org_user_team_id):[],
            primary_user_ids:threeItem.primary_user_list?threeItem.primary_user_list.map(item=>item.org_user_team_id):[],
            important_user_ids:threeItem.important_user_list?threeItem.important_user_list.map(item=>item.org_user_team_id):[],
          })
        })
      })
    })
    console.log(result);
    // if(errorList.value.length!==0){
    //   return message.error('请将数据填写完整')
    // }
    btnLoading.value = true
    const res = await addFourFocusDetail(result)
    btnLoading.value = false
    if(res.code === 0){
      message.success('保存成功')
    }
    else {
      message.error(res.message)
    }
  }
  const onBack = () => {
    router.back()
  }
</script>

<style lang="scss" scoped>
  .title {
    height: 70px;
    background: url("./images/title-bg.png")no-repeat;
    background-size: 100% 100%;
    justify-content: center;
    padding: 0 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
    font-size: 32px;
    line-height: 38px;
    color: #ffff;
    font-style: normal;
    text-transform: none;
    font-family: Source Han Sans CN, Source Han Sans CN;
  }
  .org-title{
    display: flex;
    margin: 20px 0;
    .org-icon{
      font-size: 20px;
      font-weight: bold;
      margin-left: 38px;
      margin-right: 10px;
      line-height: 35px;
      color: #000000;
    }
    .org-name{
      height: 39px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: bold;
      font-size: 26px;
      color: #000000;
      line-height: 30px;
    }
  }
  .line{
    width: 100%;
    height: 16px;
    background-color: #F5F5F5;
  }
  .content{
    margin: 24px 36px 0;
    .content-tabs{
      line-height: 58px;
      display: flex;
      border-bottom: 4px solid #008CFF;
      .tabs-name{
        padding: 0 36px;
        font-size: 24px;
        font-weight: bold;
        color: #666666;
        clip-path: polygon(0 0, 95% 0, 100% 100%, 0 100%, 0% 0%);
      }
      .active{
        color: #ffffff;
        background-color: #008CFF;
      }
    }
    .content-topic{
      .topic-item{
        margin-top: 40px;
        .topic-title{
          font-weight: bold;
          line-height: 32px;
          font-size: 24px;
          display: flex;
          .title-num{
            text-align: center;
            width: 40px;
            height: 32px;
            background: #00C5DB;
            border-radius: 4px 4px 4px 4px;
            color: #ffffff;
            margin-right: 8px;
          }
          .title-detail{
            color: #333333;
          }
        }
        .question-item{
          background: #F7FBFF;
          border-radius: 8px 8px 8px 8px;
          padding:  0px 24px 20px ;
          margin-top: 16px;
          position: relative;
          display: grid;
          .question-delete{
            position: absolute;
            right: 20px;
            top: 20px;
            width: 30px;
          }
          .question-title{
            margin-top: 24px;
            width: 97%;
            font-weight: bold;
            font-size: 22px;
            color: rgba(51,51,51,0.95);
            img{
              width: 35px;
            }
          }
          .add-question{
            margin-top: 10px;
            width: 97%;
          }
          .patrol{
            display: flex;
            margin-top: 24px;
            .patrol-label{
              font-size: 22px;
              font-weight: 400;
              display: flex;
              align-items: center;
              white-space: nowrap;
            }
            .patrol-input{
              flex-grow: 1;
              font-size: 21.5px;
              width: auto;
              background-color: #F7FBFF;
            }
          }
          .team-leader{
            display: flex;
            margin-top: 26px;
            justify-content: space-between;
            .team{
              display: flex;
              .team-label{
                font-size: 22px;
                font-weight: 400;
                display: flex;
                align-items: center;
              }
              .patrol-input{
                width: 120px;
                font-size: 21.5px;
                background-color: #F7FBFF;
              }
            }
            .leader{
              display: flex;
              font-size: 22px;
              font-weight: 400;
              .leader-label{
                font-size: 22px;
                font-weight: 400;
              }
              .leader-name{
                font-weight: 400;
                max-width: 280px;
                white-space: nowrap;      /* 防止文本换行 */
                overflow: hidden;        /* 隐藏超出部分 */
                text-overflow: ellipsis;  /* 超出部分显示省略号 */
              }
              .leader-btn{
                margin-left: 15px;
                border-radius: 5px;
                height: 32px;
                line-height: 24px;
              }
            }
          }
        }
        .other-question{
          cursor: pointer;
          margin-top: 16px;
          width: 100%;
          text-align: center;
          height: 69px;
          background: #F7FBFF;
          border-radius: 8px 8px 8px 8px;
          line-height: 69px;
          span{
            font-size: 22px;
            font-weight: 400;
            color: #008CFF;
          }
        }
      }
    }
    .bottom-btn{
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      background-color: #ffffff;
      height: 180px;
      box-shadow: 0px 0px 11px #e3e3e3;
    }
    .submit-topic{
      margin: 60px auto;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 690px;
      height: 64px;
      font-size: 24px;
      border-radius: 8px 8px 8px 8px;
    }
  }
  .all-page{
    padding-bottom: 200px;
    background: #ffffff;
  }
  .page-bottom{
    padding-bottom: 50px;
  }
</style>
<style  lang="scss">
  .isError{
    border: 1px solid red;
  }
  .custom-leader{
    .ant-drawer-body{
      position: relative;
      .leader-commit{
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 50px;
        width: 90%;
        height: 64px;
        font-weight: 400;
        font-size: 24px;
        color: #FFFFFF;
        border-radius: 4px 4px 4px 4px;
      }
    }
    .ant-drawer-content-wrapper{
      width: 900px !important;
      .ant-drawer-header{
        height: 80px;
        .ant-drawer-title{
          font-weight: bold;
          font-size: 26px;
          color: #020202;
        }
      }
    }
    .custom-content{
      height: 88%;
      overflow-y: auto;
      &::-webkit-scrollbar {
        display: none;
      }
      .leader-checkbox-item{
        margin:  0 20px;
        .leader-checkbox{
          /*height: 70px;*/
          line-height: 40px;
          padding: 15px 0;
          border-bottom: 1px solid #C8C8C8;
          .ant-checkbox-wrapper{
            .ant-checkbox-inner{
              width: 22px;
              height: 22px;
              margin-bottom: 2px;
            }
            span{
              font-weight: 400;
              font-size: 24px;
              color: #020202;
            }
          }
        }
      }
    }
  }
</style>