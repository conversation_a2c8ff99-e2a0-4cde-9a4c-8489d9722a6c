<template>
	<div class="bzxc-home">
		<div class="head">
			<Title :onBack="onBack" />
		</div>

		<div class="main-content">
			<div class="menu">
				<a-menu
					v-model:openKeys="state.openKeys"
					v-model:selectedKeys="state.selectedKeys"
					mode="inline"
					theme="light"
					:inline-collapsed="state.collapsed"
				>
					<a-sub-menu v-for="(menu, index) in menuList" :key="menu.key">
						<!-- <template #icon>
							<component :is="menu.icon" />
						</template> -->
						<template #title>{{ menu.title }}</template>
						<a-menu-item v-for="menu_item in menu.children" @click="onRoute(menu_item)" :disabled="menu_item.disabled" :key="menu_item.path">{{
							menu_item.title
						}}</a-menu-item>
						<!-- <a-menu-item key="3">政治画像</a-menu-item> -->
					</a-sub-menu>
				</a-menu>
			</div>
			<div class="content">
				<!-- <CommunicateLevel /> -->
				<router-view v-slot="{ Component }">
					<keep-alive>
						<component :is="Component" />
					</keep-alive>
				</router-view>
			</div>
		</div>
	</div>
</template>
<script lang="ts">
export default {
	name: 'PartyInspectionDetail',
}
</script>
<script setup lang="ts">
import { watch, reactive } from 'vue'
import { SearchOutlined, SettingOutlined } from '@ant-design/icons-vue'
import Title from './component/Title.vue'
// import CommunicateLevel from './component/CommunicateLevel.vue'
import { useRouter } from 'vue-router'
const router = useRouter()

const state = reactive({
	collapsed: false,
	selectedKeys: [],
	openKeys: [],
	preOpenKeys: [],
})
const menuList = [
	{
		icon: SearchOutlined,
		title: '班子巡察',
		key: '/data-screen/party-inspection-detail',
		children: [
			{
				title: '班子成员管理',
				// disabled: true,
				key: '1',
				path: '/data-screen/party-inspection-detail/org-list',
			},
			{
				title: '四个聚焦',
				key: '2',
				path: '/data-screen/party-inspection-detail/four-focus-home',
			},
      {
        title: '谈话等次',
        key: '3',
        path: '/data-screen/party-inspection-detail/talk-level-home',
      },
		],
	},
]

const onBack = () => {
	router.back()
}

const onRoute = (menu: any) => {
	if (menu.path) {
		router.push(menu.path)
	}
}

watch(
	() => state.openKeys,
	(_val, oldVal) => {
		state.preOpenKeys = oldVal
	}
)
// 根据路径判断是否高亮
watch(
	() => router.currentRoute.value.path,
	(val) => {
		console.log(val)
		// 先清空
		state.selectedKeys = [val]
		const current = menuList.find((item) => val.startsWith(item.key))
		// // 再添加
		state.openKeys = [val]
		current && (state.openKeys = [current.key])
	},
	{
		deep: true,
		immediate: true,
	}
)
</script>

<style lang="scss" scoped>
.bzxc-home {
	width: 100%;
	height: 100%;
	background-color: #f3f3f3;
	display: flex;
	flex-direction: column;
	.head {
		flex: 0 0 inherit;
	}
	.main-content {
		display: flex;
		flex-grow: 1;
		overflow: hidden;

		.menu {
			width: 220px;
			height: 100%;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 16px;
			line-height: 19px;
			font-style: normal;
			text-transform: none;
			background-color: #fff;
			border-right: 1px solid #f0f0f0;
			margin-right: 16px;
			:deep(.ant-menu-item) {
				height: 46px;
			}
			:deep(.ant-menu-item-selected) {
				height: 46px;
			}
			:deep(.ant-menu-item-only-child) {
				height: 46px;
			}
			:deep(.ant-menu-submenu-selected) {
				color: #008eff;
			}
			:deep(.ant-menu-item-selected) {
				color: #008eff;
			}
		}
		.content {
			flex: 1;
			overflow: auto;
			background-color: #fff;
		}
	}
}
</style>

<!-- <template>
	<div class="party-inspection">
		<Title />
		<div class="content">
			<div class="left-box"></div>
			<div class="right-box">
				<div class="focus-communicate-box">
					<button class="four-focus">四个聚焦</button>
					<button
						class="communicate-level"
						@click="
							() => {
								onInspectPage(`/data-screen/party-inspection-detail/CommunicateLevel.vue`)
							}
						"
					>
						谈话等次
					</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">

import { useRouter } from 'vue-router'

const router = useRouter()

const onInspectPage = (path: string) => {
	router.push(path)
}
</script>

<style lang="scss" scoped>
.party-inspection {
	.focus-communicate-box {
		width: 372px;
		line-height: 30px;
		text-align: center;
		color: white;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		font-size: 24px;
		color: #ffff;
		line-height: 28px;
		text-align: center;
		font-style: normal;
		text-transform: none;

		.four-focus {
			width: 170px;
			height: 64px;
			background-color: #008cff;
		}
		.communicate-level {
			width: 170px;
			height: 64px;
			background-color: #f67d14;
			margin-left: 32px;
		}
	}
}
</style> -->
