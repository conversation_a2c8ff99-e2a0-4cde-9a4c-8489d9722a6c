<template>
	<div class="org-list">
		<div class="left-menu">
			<div class="search-box">
				<a-input-search v-model:value="searchValue" placeholder="请输入组织名称" @search="onSearch" allowClear />
			</div>

			<div class="tree-container">
				<div class="tree-node" v-for="node in filteredTreeData" :key="node.key">
					<div class="node-content" :class="{ 'node-selected': selectedKeys.includes(node.key as string) }" @click="handleNodeClick(node)">
						<img
							:src="selectedKeys.includes(node.key as string) ? orgIcon : subOrgIcon"
							class="org-icon"
							:class="{ selected: selectedKeys.includes(node.key as string) }"
						/>
						<span class="node-title">{{ node.title }}</span>
					</div>
				</div>
			</div>
		</div>

		<div class="right-content">
			<div class="header">
				<div class="title">{{ currentOrg?.org_name }}</div>
				<a-button type="primary" @click="showModal">添加用户</a-button>
			</div>

			<a-table :loading="loading" bordered :columns="columns" :dataSource="userList" :pagination="false">
				<template #bodyCell="{ column, record }">
					<template v-if="column.key === 'action'">
						<a-space>
							<!-- <a @click="handleEdit(record)" class="edit-btn">编辑</a> -->
							<!-- <a-divider type="vertical" /> -->
							<a-popconfirm placement="topRight" title="确定要删除该用户吗?" ok-text="确定" cancel-text="取消" @confirm="() => handleDelete(record)">
								<a class="delete-btn">删除</a>
							</a-popconfirm>
						</a-space>
					</template>
				</template>
			</a-table>
		</div>

		<UserModal v-model:visible="modalVisible" :title="modalTitle" :org-id="currentOrg?.org_id" :edit-data="editData" @success="onSuccess" />
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { apiGetTreeData, getOrgUserList, deleteOrgUser } from '@/apis/data-screen'
import subOrgIcon from '@/assets/images/sub-org-icon.png'
import orgIcon from '@/assets/images/sub-org-icon_acitve.png'
import UserModal from './UserModal.vue'

interface TreeNode {
	key: string | number
	title: string
	children?: TreeNode[]
}

const modalVisible = ref(false)
const modalTitle = ref('添加用户')
const editData = ref<any>(null)
const userList = ref([])
const currentOrg = ref<any>(null)
const loading = ref<any>(false)

// 树相关数据
const treeData = ref<TreeNode[]>([])
const selectedKeys = ref<string[]>([])
const searchValue = ref('')

// 表格列配置
const columns = [
	{
		title: '姓名',
		dataIndex: 'user_name',
		key: 'user_name',
		align: 'center',
		width: 200,
	},
	{
		title: '职务',
		dataIndex: 'current_job',
		key: 'current_job',
		align: 'center',
	},
	{
		title: '操作',
		key: 'action',
		align: 'center',
		width: 200,
	},
]

// 加载组织树数据
const loadTreeData = async () => {
	try {
		const { code, data } = await apiGetTreeData([])
		if (code === 0 && data) {
			treeData.value = formatTreeData(data)
			// 默认选中第一个组织
			if (data.length > 0) {
				const firstOrg = data[0]
				handleNodeClick({
					key: firstOrg.org_id,
					title: firstOrg.org_name,
				})
			}
		}
	} catch (err) {
		message.error('获取组织列表失败')
	}
}

// 格式化树数据
const formatTreeData = (data: any[]): TreeNode[] => {
	return data.map((item) => ({
		key: item.org_id,
		title: item.org_name,
		children: item.children ? formatTreeData(item.children) : undefined,
	}))
}

// 处理节点点击
const handleNodeClick = (node: TreeNode) => {
	selectedKeys.value = [node.key as string]
	currentOrg.value = {
		org_id: node.key,
		org_name: node.title,
	}
	loadUserList(node.key as string)
}

// 加载用户列表
const loadUserList = async (orgId: string) => {
	try {
		loading.value = true
		const { code, data = [] } = await getOrgUserList(orgId)
		if (code === 0) {
			userList.value = data
		}
	} catch (err) {
		message.error('获取用户列表失败')
	} finally {
		loading.value = false
	}
}

// 弹窗相关方法
const showModal = () => {
	if (!currentOrg.value?.org_id) {
		message.warning('请先选择组织')
		return
	}
	modalTitle.value = '添加用户'
	editData.value = null
	modalVisible.value = true
}

const handleEdit = (record: any) => {
	modalTitle.value = '编辑用户'
	editData.value = record
	modalVisible.value = true
}

const handleDelete = async (record: any) => {
	try {
		await deleteOrgUser(record.org_user_team_id)
		message.success('删除成功')
		loadUserList(currentOrg.value.org_id)
	} catch (err) {
		message.error('删除失败')
	}
}

const onSuccess = () => {
	modalVisible.value = false
	loadUserList(currentOrg.value.org_id)
}

// 递归搜索树节点
const searchTreeNode = (nodes: TreeNode[], keyword: string): TreeNode[] => {
	return nodes.reduce((acc: TreeNode[], node) => {
		const matchNode = { ...node }
		if (node.title.toLowerCase().includes(keyword.toLowerCase())) {
			if (node.children) {
				matchNode.children = searchTreeNode(node.children, keyword)
			}
			acc.push(matchNode)
		} else if (node.children) {
			const matchChildren = searchTreeNode(node.children, keyword)
			if (matchChildren.length) {
				matchNode.children = matchChildren
				acc.push(matchNode)
			}
		}
		return acc
	}, [])
}

// 过滤后的树数据
const filteredTreeData = computed(() => {
	if (!searchValue.value) {
		return treeData.value
	}
	return searchTreeNode(treeData.value, searchValue.value)
})

// 搜索方法
const onSearch = (value: string) => {
	searchValue.value = value
}

onMounted(() => {
	loadTreeData()
})
</script>

<style lang="scss" scoped>
.org-list {
	display: flex;
	width: 100%;
	height: 100%;

	.left-menu {
		display: flex;
		flex-direction: column;
		width: 280px;
		height: 100%;
		padding: 24px 4px;
		border-right: 1px solid #f0f0f0;
		overflow: hidden;

		.search-box {
			margin-bottom: 16px;
			padding: 0 16px;
		}

		.tree-container {
			flex: 1;
			overflow-y: auto;
			padding: 0 16px;

			.tree-node {
				.node-content {
					display: flex;
					align-items: center;
					padding: 8px 12px;
					margin: 4px 0;
					border-radius: 4px;
					cursor: pointer;
					transition: all 0.3s;

					&:hover {
						background-color: #f5f5f5;
					}

					&.node-selected {
						background-color: #e6f4ff;
						color: #008eff;
						font-weight: 500;
					}

					.org-icon {
						width: 16px;
						height: 16px;
						margin-right: 8px;
						opacity: 0.6;

						&.selected {
							opacity: 1;
						}
					}

					.node-title {
						flex: 1;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}
			}
		}
	}

	.right-content {
		flex: 1;
		padding: 24px;
		overflow: auto;

		.header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 24px;

			.title {
				font-size: 20px;
				font-weight: 500;
			}
		}

		.edit-btn {
			color: #1890ff;
		}

		.delete-btn {
			color: #ff4d4f;
		}
	}
}
</style>
