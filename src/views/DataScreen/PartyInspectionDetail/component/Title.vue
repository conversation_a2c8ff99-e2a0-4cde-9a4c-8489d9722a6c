<template>
	<div class="title">
		<div class="return-button" @click="onBack"><img :src="backHeader" alt="左箭头" class="back-header" />返回</div>
		<div class="title-text">班子巡察</div>
	</div>
</template>

<script setup lang="ts">
import backHeader from '@/assets/images/back-header.png'

defineProps({
	onBack: Function,
})
</script>

<style lang="scss" scoped>
.title {
	height: 70px;
	background-color: #008cff;
	text-align: left;
	position: relative;
	display: flex;
	align-items: center;
	padding: 0 20px;
	color: #ffff;
	font-style: normal;
	text-transform: none;
	font-family: Source <PERSON>, Source Han Sans CN;
	.return-button {
		border: none;
		float: left;
		padding: 0 10px;
		line-height: 30px;
		align-items: center;
		justify-content: center;
		display: flex;
		font-weight: 500;
		font-size: 28px;
		line-height: 33px;
		cursor: pointer;
		.back-header {
			width: 24px;
			height: 24px;
			margin-right: 7px;
		}
	}
	.title-text {
		flex-grow: 1;
		text-align: center;
		font-weight: bold;
		font-size: 32px;
		line-height: 38px;
	}
}
</style>
