<template>
	<a-modal :visible="visible" :title="title" @ok="handleOk" @cancel="handleCancel" :confirmLoading="loading" :width="480">
		<a-form ref="formRef" :model="formState" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
			<a-form-item label="姓名" name="user_name">
				<a-select
					v-model:value="formState.user_id"
					:loading="searching"
					show-search
					:filter-option="false"
					placeholder="请输入姓名搜索或手动输入"
					@search="handleSearch"
					@change="handleUserSelect"
				>
					<template v-if="userOptions.length">
						<a-select-option v-for="user in userOptions" :key="user.user_id" :value="user.user_id">
							<div class="user-option">
								<span>{{ user.user_name }}</span>
								<span class="job">{{ user.current_job }}</span>
							</div>
						</a-select-option>
					</template>
					<a-select-option v-else-if="searchValue" :value="searchValue"> {{ searchValue }}</a-select-option>
				</a-select>
			</a-form-item>

			<a-form-item label="职务" name="current_job">
				<a-input v-model:value="formState.current_job" placeholder="请输入职务" :maxLength="50" />
			</a-form-item>
		</a-form>
	</a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import { addOrgUser, updateOrgUser } from '@/apis/data-screen'
import { findByUserName } from '@/apis/data-screen'

const props = defineProps({
	visible: Boolean,
	title: String,
	orgId: [String, Number],
	editData: Object,
})

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref<FormInstance>()
const loading = ref(false)
const searching = ref(false)
const userOptions = ref<any[]>([])
const searchValue = ref<any>('')

const formState = reactive<{ user_id: string | null; current_job: string }>({
	user_id: null,
	current_job: '',
})

const rules = {
	user_id: [{ required: true, message: '请输入姓名' }],
	current_job: [{ required: true, message: '请输入职务' }],
}

const handleSearch = async (value: string) => {
	searchValue.value = value
	if (!value) {
		userOptions.value = []
		return
	}

	searching.value = true
	try {
		const { code, data } = await findByUserName(value)
		if (code === 0) {
			userOptions.value = data || []
		}
	} catch (err) {
		message.error('搜索失败')
	} finally {
		searching.value = false
	}
}

const handleUserSelect = (value: string | null) => {
	const selectedUser = userOptions.value.find((user) => user.user_id === value)
	if (selectedUser) {
		// 选择了匹配的用户
		formState.user_id = value
		formState.current_job = selectedUser.current_job
	} else {
		// 手动输入
		formState.user_id = value as string
		formState.current_job = '' // 清空职务,让用户手动输入
	}
}

watch(
	() => props.editData,
	(val) => {
		if (val) {
			Object.assign(formState, val)
		} else {
			formRef.value?.resetFields()
			searchValue.value = null
		}
	}
)

watch(
	() => props.visible,
	(val) => {
		if (!val) {
			// 弹窗关闭时清空数据
			searchValue.value = null
			userOptions.value = []
			formRef.value?.resetFields()
			formState.user_id = null
			formState.current_job = ''
		}
	}
)

const handleOk = () => {
	formRef.value?.validate().then(async () => {
		loading.value = true
		try {
			const { user_name = formState.user_id, user_id } = userOptions.value.find((user) => user.user_id === formState.user_id) || {}
			const params = {
				...formState,
				org_id: props.orgId,
				user_name,
			}
			user_id && (params.user_id = user_id)

			if (props.editData) {
				await updateOrgUser(params)
			} else {
				await addOrgUser(params)
			}

			message.success('操作成功')
			emit('success')
		} catch (err) {
			message.error('操作失败')
		} finally {
			loading.value = false
		}
	})
}

const handleCancel = () => {
	/* formRef.value?.resetFields()
	searchValue.value = null
	userOptions.value = []
	formState.user_id = null
	formState.current_job = '' */
	emit('update:visible', false)
}
</script>

<style lang="scss" scoped>
:deep(.ant-modal-content) {
	.ant-modal-header {
		border-bottom: 1px solid #f0f0f0;
		padding: 16px 24px;

		.ant-modal-title {
			font-size: 16px;
			font-weight: 500;
		}
	}

	.ant-modal-body {
		padding: 24px;
	}

	.ant-modal-footer {
		border-top: 1px solid #f0f0f0;
		padding: 10px 16px;
	}
}

:deep(.ant-form-item) {
	margin-bottom: 24px;

	&:last-child {
		margin-bottom: 0;
	}
}

.user-option {
	display: flex;
	justify-content: space-between;
	align-items: center;

	.job {
		color: #999;
		font-size: 12px;
	}
}
</style>
