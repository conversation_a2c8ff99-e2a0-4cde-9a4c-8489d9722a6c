<template>
	<div class="FourFocusHome">
		<div class="search">
			<!-- <a-space direction="vertical" :size="12"> -->
			<a-space style="margin-left: 20px; gap: 10px">
				届次：
				<a-select
				placeholder="请选择"
				v-model:value="formData.year"
				style="width: 120px"
				:options="yearOptions"
				></a-select>
				班子名称：
				<a-input placeholder="请输入" v-model:value="formData.org_name" style="width: 120px"></a-input>
				巡察状态：
				<a-select
					placeholder="全部"
					v-model:value="formData.status"
					style="width: 120px"
					:options="inspectedOptions"
					@change="handleChange"
				></a-select>
			</a-space>
			<div class="buttonbox">
				<a-button @click="onSearch"  type="primary">查询</a-button>
				<a-button @click="onReset">重置</a-button>
			</div>
		</div>
		<a-table
			:columns="columns"
			:data-source="data"
			bordered
			:pagination="{ current: pagination.page, total: pagination.total, onChange: onPaginationChange, page_size: pagination.page_size }"
		>
			<template #bodyCell="{ column, record }">
				<template v-if="column.key === 'partyName'">{{ record.name }}</template>
				<template v-else-if="column.key === 'status'">
					<span>
						<a-tag record.status :color="record.status === 0 ? 'green' : 'yellow'">
							{{ record.status === 0 ? '已巡察' : '未巡察' }}
						</a-tag>
					</span>
				</template>
				<template v-else-if="column.key === 'operation'">
					<span>
						<!-- <a v-if="record.status">详情</a>
						<a-divider type="vertical" v-if="record.status" /> -->
						<a
							v-if="!record.status"
							@click="
								() => {
									onInspectPage(`/data-screen/party-inspection-detail/CommunicateLevel.vue`, record)
								}
							"
							>修改</a
						>
						<a
							v-if="record.status"
							@click="
								() => {
									console.log(record)
									onInspectPage(`/data-screen/party-inspection-detail/CommunicateLevel.vue`, record)
								}
							"
							>录入</a
						>
					</span>
				</template>
			</template>
		</a-table>
	</div>
</template>

<script setup lang="ts">
import { reactive, ref, inject, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import type { Dayjs } from 'dayjs'
import { SelectProps } from 'ant-design-vue'
import { DownOutlined } from '@ant-design/icons-vue'
import { getSpeechList } from '@/apis/data-screen'
import { watch } from 'vue'
const page = inject('page')

page.clearPage()

const yearData = ref('')
const formData = reactive({
	year: 15,
	org_name: undefined,
	status: undefined,
})
const pagination = reactive({
	total: 0,
	page: 1,
	page_size: 10,
})
const router = useRouter()

const onInspectPage = (path: string, record: any) => {
	page.updatePage('PartyInspectionDetail')
	router.push({
		path,
		query: {
			org_id: record.organization_id,
			org_name: record.org_name,
			year: record.year,
		},
	})
}

const customFormat = (value: any) => {
	if (value) {
		const date = new Date(value)
		const yearStr = date.getFullYear().toString()
		return `${yearStr}年度`
	}
	return ''
}

const handleChange = (value: string) => {
	console.log(`selected ${value}`)
}
const yearOptions = ref<SelectProps['options']>([
  {
    value: 15,
    label: '15届',
  },
  {
    value: 16,
    label: '16届',
  },
])
const inspectedOptions = ref<SelectProps['options']>([
	{
		value: 0,
		label: '已巡察',
	},
	{
		value: 1,
		label: '未巡察',
	},
])
const columns = ref([
	{
		title: '班子名称',
		dataIndex: 'org_name',
		key: 'org_name',
	},
	{
		title: '巡察状态',
		key: 'status',
		dataIndex: 'status',
	},
	{
		title: '操作',
		key: 'operation',
	},
])

const data = ref([])
const onSearch = () => {
	loadData()
}
const onReset = () => {
	formData.year = 15
	formData.status = undefined
	formData.org_name = undefined
	pagination.page = 1
}
const onPaginationChange = (page: any, page_size: any) => {
	pagination.page = page
	pagination.page_size = page_size
}
/**
 * year	number	20	Y	考核年份
org_name	String	100	N	组织名称
status	number	20	N	状态0已巡察1未巡察
page	number	20	N	页码
page_size	number	20	N	每页条数	默认10
 *
 */
// 数据加载
const loadData = async () => {
  const {page,page_size} = pagination
	const res = await getSpeechList({ ...formData, year: formData.year,page,page_size})
	if (res.code === 0) {
		const { data: _data, total }: any = res
		pagination.total = total

		data.value = _data
	}
}
onActivated(() => {
	loadData()
})
watch(
	() => pagination,
	() => {
		loadData()
	},
	{
		immediate: true,
		deep: true,
	}
)
</script>

<style lang="scss" scoped>
.FourFocusHome {
	padding: 24px;
	.search {
		width: 100%;
		height: 60px;
		background: #f3f3f3;
		border-radius: 4px 4px 4px 4px;
		margin-bottom: 16px;
		display: flex;
		flex-direction: row;
		.buttonbox {
			margin-left: 20px;
			height: 100%;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			gap: 10px;
			/*:first-child {*/
				/*background: #008eff;*/
				/*color: #ffffff;*/
			/*}*/
			/*:nth-child(2) {*/
				/*background: #ffffff;*/
			/*}*/
		}
		button {
			width: 62px;
			height: 36px;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 4px 4px 4px 4px;
			font-family: Microsoft YaHei, Microsoft YaHei;
			font-weight: 400;
			font-size: 18px;
			/*color: #000000;*/
			line-height: 21px;
			font-style: normal;
			text-transform: none;
		}
	}
	:deep(.ant-table-thead tr th) {
		background-color: #f3f3f3;
		font-weight: 500;
	}
	:deep(.ant-table-tbody tr td:nth-child(1)) {
		text-align: left;
	}
	:deep(.ant-table-cell) {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		background-color: #ffff;
		text-align: center;
		font-style: normal;
		text-transform: none;

		font-size: 16px;
		color: #333333;
		line-height: 19px;
	}
}
</style>
