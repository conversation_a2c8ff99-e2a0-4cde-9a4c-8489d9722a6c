<template>
	<div class="middle">
		<div class="middle_header">
			<div
				class="middle_header_item"
				:class="{ active: curTab.join(',') === item.value.join(','), disabled: item.disabled }"
				v-for="item in tabList"
				:key="item.value"
				@click="handleToggleTab(item)"
			>
				{{ item.name }}
			</div>
		</div>
		<div class="middle_body">
			<TreeAndFruit @select="onSelect" :treeData="treeData" :key="curTab.join()" :menu-key="curTab.join()"></TreeAndFruit>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { onActivated, onMounted, onUnmounted, ref } from 'vue'
import TreeAndFruit from './TreeAndFruit/index.vue'
import { useRouter } from 'vue-router'

import { apiGetTreeTab, apiGetTreeData } from '@/apis/data-screen'
import { debounce } from '@/utils/utils'
import { message } from 'ant-design-vue'

const tabList = ref<any[]>([
	{
		name: '全县',
		value: ['1'],
	},
	{
		name: '乡镇街道',
		value: ['10300203', '10300202', '10300201'],
	},
	{
		name: '经济部门',
		value: ['10300101'],
	},
	{
		name: '保障部门',
		value: ['10300102'],
	},
	{
		name: '执纪执法部门',
		value: ['10300103'],
	},
	{
		name: '国企',
		value: ['10300105'],
	},
	{
		name: '医院',
		value: ['10300107'],
	},
	{
		name: '学校',
		value: ['10300106'],
	},
])
const curTab = ref<any[]>(['1'])
const treeData = ref<any[]>()

const router = useRouter()

const testRender = ref<any>('')

// 获取树的切换列表
const getTreeTab = async () => {
	const { data: resData } = await apiGetTreeTab()
	const { code, data } = resData
	if (code === 0) {
		// tabList.value = data
	}
}
// 根据类型获取树的数据
const getTreeDataByType = async () => {
	let ids = curTab.value
	if (curTab.value.includes('1')) {
		ids = []
	}
	const { data, code, message: msg } = await apiGetTreeData(ids)
	if (code === 0) {
		// data[0]['patrol_index'] = -22 // 写死一个数据展示棕色
		treeData.value = data
	} else {
		message.error(msg)
	}
}

const handleToggleTab = (item: any) => {
	console.log('sdadsadsa')
	curTab.value = item.value
	treeData.value = []

	getTreeDataByType()
}
handleToggleTab({
	name: '全县',
	value: ['1'],
})
// 选中
const onSelect = (item: any) => {
	router.push(`/data-screen/org-detail?org_id=${item.org_id}`)
}
</script>

<style lang="scss" scoped>
.middle {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	&_header {
		margin: 0 16px;
		margin-top: 20px;
		display: flex;
		justify-content: space-between;
		height: 50px;
		flex-shrink: 0;
		&_item {
			padding: 12px 17px 7px 17px;
			box-sizing: border-box;
			font-weight: 500;
			min-width: 105px;
			height: 46px;
			text-align: center;
			font-size: 20px;
			line-height: 23px;
			color: #bdfeff;
			background-image: url('@/assets/images/data-screen/middle_header_bgk.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			background-position: center;
			cursor: pointer;
		}
		.active {
			color: #ffd900;
			background-image: url('@/assets/images/data-screen/middle_header_bgk_active.png');
		}
		.disabled {
			cursor: no-drop;
		}
	}
	&_body {
		flex: 1;
		padding: 12px 24px 44px 24px;
	}
}
</style>
