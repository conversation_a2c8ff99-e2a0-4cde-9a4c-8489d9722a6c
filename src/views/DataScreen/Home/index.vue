<template>
	<div class="data-screen-home">
		<div class="header">
			<div class="title"></div>
			<div class="tips-modal-btn" @click="onModal"><i class="tree-icon"></i>巡察指数树形图</div>
		</div>
		<div class="background-img"></div>
		<div class="ds-home-content">
			<div class="ds-h-content__left" @touchend.prevent="() => {}">
				<Left />
			</div>
			<div class="ds-h-content__middle">
				<Middle />
			</div>
			<div class="ds-h-content__right">
				<Right />
			</div>
		</div>
	</div>
	<TreeModal :visible="visible" title="丰都县巡察结果体系（巡察指数）树形图" :on-close="onClose" />
</template>
<script lang="ts">
export default {
	name: 'DataScreenHome',
}
</script>
<script lang="ts" setup>
import { inject, ref } from 'vue'
import Left from './components/Left.vue'
import Right from './components/Right.vue'
import Middle from './components/Middle.vue'
import TreeModal from './components/TreeModal.vue'
const page: any = inject('page')

page.updatePage?.('DataScreenHome')

const visible = ref(false)

const onModal = () => {
	visible.value = true
}

const onClose = () => {
	visible.value = false
}
</script>

<style lang="scss" scoped>
.data-screen-home {
	height: 100%;
	width: 100%;
	// background: url('./images/backgroud.png') no-repeat center / 100% 100%;
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
	.header {
		position: absolute;
		width: 100%;
		display: flex;
		justify-content: center;
		.title {
			width: 1098px;
			height: 78px;
			background: url('./images/header.png') no-repeat top / 100% 142px;
		}
		.tips-modal-btn {
			position: absolute;
			right: 0px;
			top: 0px;
			width: 180px;
			height: 40px;
			background: url('./images/tips-1.png') no-repeat center / cover;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: bold;
			font-size: 16px;
			color: #d8f0ff;
			line-height: 16px;
			letter-spacing: 1px;
			text-shadow: 0px 0px 10px #0091ff;
			.tree-icon {
				display: inline-block;
				width: 26px;
				height: 30px;
				background: url('./images/tips-2.png') no-repeat center / cover;
				cursor: pointer;
			}
		}
	}
	.background-img {
		background: url('./images/background-1.png') no-repeat center / 100% 100%;
		height: 88px;
	}
	.ds-home-content {
		display: flex;
		flex: 1;
		width: 100%;
		overflow: hidden;
		background: url('./images/background-2.png') no-repeat center / 100% 100%;
		.ds-h-content__left {
			margin: 10px 0px 40px 21px;
			display: flex;
			flex-direction: column;
			width: 460px;
			overflow: hidden;
		}
		.ds-h-content__middle {
			flex: 1;
		}
		.ds-h-content__right {
			width: 460px;
			margin-bottom: 40px;
			margin: 10px 21px 40px 0px;
		}
	}
}
</style>
