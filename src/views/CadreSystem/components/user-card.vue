<template>
	<div :class="`user-card ${size} ${!showPosition ? 'middle' : ''}`" @click="onClick" v-bind="$attrs">
		<!-- <div class="collection-icon" v-if="type === 'collect' && user.hasFavorite" @click.stop="onIconClick"></div> -->
		<div class="avatar">
			<CodeAvatar :user_id="user.user_id" :head_url="user.head_url">
				<template #avatar="{ avatar }">
					<img :src="avatar" @load="onLoad" />
				</template>
			</CodeAvatar>
		</div>
		<div :class="`user-info`">
			<div class="name">
				{{ user.user_name || user.name }}
			</div>
			<div class="position" v-if="showPosition">{{ user.position || user.currentJob || user.current_job }}</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { PropType, watch } from 'vue'
const CDN_URL = import.meta.env.VITE_CDN_URL
import { historyPush } from '@/utils/history'
import defaultAvatar from '@/assets/images/avatar.png'
import CodeAvatar from '@/components/CodeAvatar.vue'

const props = defineProps({
	//空缺展示
	empty: {
		type: Boolean,
		default: false,
	},
	user: {
		type: Object,
		default: () => ({}),
	},
	type: {
		type: String as PropType<'collect'>,
		required: false,
	},
	size: {
		type: String as PropType<'small' | 'large'>,
		default: 'small',
	},
	middle: {
		type: Boolean,
		default: false,
	},
	showPosition: {
		type: Boolean,
		default: true,
	},
})
const onLocation = (data: any) => {
	if (!data.user_id) return

	historyPush(`/cadre-portrait/home?user_id=${data.user_id}`)
}
const emit = defineEmits(['card-click', 'icon-click', 'load'])

const onLoad = () => {
	emit('load')
}
const onClick = () => {
	// onLocation(props.user)
	emit('card-click', props.user)
}

const onIconClick = () => {
	emit('icon-click', props.user)
}
</script>

<style lang="less" scoped>
.middle {
	height: 270px !important;
}
.user-card {
	position: relative;
	display: flex;
	margin: 24px 36px 0 0;
	padding: 15px 30px;
	width: 284px;
	height: 300px;
	background: #f9f9f9;
	border-radius: 10px 10px 10px 10px;
	opacity: 1;
	cursor: pointer;
	display: flex;
	flex-direction: column;
	align-items: center;
	.collection-icon {
		position: absolute;
		top: 0px;
		right: -10px;
		width: 106px;
		height: 40.5px;
		background: url('../image/collection.png') no-repeat center / cover;
	}
	.avatar {
		width: 136px;
		height: 180px;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		img {
			display: inline-block;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
	.user-info {
		width: 100%;
		.name {
			width: 100%;
			text-align: center;
			margin-top: 12px;
			font-size: 18px;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: rgba(0, 0, 0, 0.9);
			line-height: 21px;
		}
		.position {
			margin-top: 4px;
			font-size: 14px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: rgba(0, 0, 0, 0.9);
			line-height: 24px;
			text-align: center;

			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 3;
			overflow: hidden;
		}
	}
}

.small {
	padding: 12px;
	width: 214px;
	min-width: 200px;
	height: 338px;
	background: #f9f9f9;
	border-radius: 10px 10px 10px 10px;
	opacity: 1;
	margin: 16px 16px 0 0;
	.avatar {
		width: 144px;
		height: 190px;
	}
	.user-info {
		.name {
			margin-top: 12px;
			line-height: 26px;
			font-size: 22px;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 600;
			color: rgba(0, 0, 0, 0.9);
		}
		.source {
			font-size: 18px;
			font-weight: 600;
		}
		.position {
			margin-top: 8px;
			font-size: 18px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: rgba(0, 0, 0, 0.9);
			line-height: 24px;
			text-align: center;
		}
	}
}
</style>
