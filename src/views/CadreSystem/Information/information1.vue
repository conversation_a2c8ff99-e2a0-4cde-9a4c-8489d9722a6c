<template>
	<div class="cadre-system-information">
		<div :class="`left-ignore ${!sliderStatus && 'translate-animation'}`" @transitionend="onTransitionEnd">
			<div class="scroll-box">
				<OrgTree @select="onTreeSelect" @bread-crumb="onBreadCrumb" />
			</div>
			<div :class="['slider-control', !sliderStatus && 'slider-hidden']" @click="onExpand"></div>
		</div>
		<div class="right">
			<a-breadcrumb separator=">" class="breadcrumb">
				<a-breadcrumb-item v-for="item in breadCrumb" :key="item.org_id" @click="onHandleCrumb(item)">{{ item.name }}</a-breadcrumb-item>
			</a-breadcrumb>
			<div class="top-box">
				<div class="btn-box">
					<div class="select-btn" :class="{ active: activeKey === '1' }" @click="onSelect('1')">领导班子</div>
					<div class="select-btn" :class="{ active: activeKey === '2' }" @click="onSelect('2')">中层干部</div>
				</div>
				<div class="msg-block" v-if="activeKey === '1'">
					<!-- <div class="text-box">
						总职数<span class="number">{{ orgCount.total }}</span
						>人、正职<span class="number">{{ orgCount.real_leader }}</span
						>人、正职空缺<span class="number">{{ orgCount.real_leader_vacancy }}</span
						>人、副职<span class="number">{{ orgCount.vice_leader }}</span
						>人、副职空缺<span class="number">{{ orgCount.vice_leader_vacancy }}</span
						>人
					</div> -->
					<div class="input-box">
						<a-input v-model:value="userName" placeholder="输入姓名">
							<template #prefix>
								<span class="search-icon"></span>
							</template>
						</a-input>
						<a-button type="link" @click="onSearch">查询</a-button>
					</div>
				</div>
			</div>
			<div class="scroll-container" ref="scrollContainer" v-show="activeKey === '1'">
				<div :class="`${isSearch ? 'search-scroll' : ''} inner`">
					<template v-for="(userlist, index) in dataList" :key="index">
						<template v-if="isSearch">
							<template v-for="user in userlist.leader_info" :key="user.user_id">
								<UserCard :user="user" @card-click="userCardClick" :style="{ width: sliderStatus ? '12.9%' : '11.38%' }" />
							</template>
						</template>
						<div style="width: 100%" class="border-bottom" v-else>
							<div :class="`org-name-box ${index === 0 && 'margin-top-0'}`">
								<div class="org-name">{{ userlist.org_name }}</div>
								<div class="text-box">
									( 总职数<span class="number"> {{ userlist.total }} </span>人、正职<span class="number"> {{ userlist.real_leader }} </span
									>人、正职空缺<span class="number"> {{ userlist.real_leader_vacancy }} </span>人、副职<span class="number">
										{{ userlist.vice_leader }} </span
									>人、副职空缺<span class="number"> {{ userlist.vice_leader_vacancy }} </span>人 )
								</div>
							</div>

							<div class="user-box">
								<template v-for="user in userlist.leader_info" :key="user.user_id">
									<UserCard :user="user" @card-click="userCardClick" :style="{ width: sliderStatus ? '12.9%' : '11.38%' }" />
								</template>
								<template v-if="userlist.vacancy">
									<UserCard
										:style="{ width: sliderStatus ? '12.9%' : '11.38%' }"
										:user="{ position: item }"
										empty
										v-for="item in userlist.vacancy"
										:key="item"
									/>
								</template>
							</div>
							<!-- <a-row :gutter="24" v-if="userlist.middle_level.length">
						<div class="tip-link">
							<div class="link" @click="onMidLevelExpand">
								<span :class="`${midLevelVisible ? '' : 'no-expand'} icon`"></span>
								<span class="text"> 中层干部 </span>
							</div>
						</div>
					</a-row> -->
							<!-- <div class="user-box" v-if="midLevelVisible">
						<template v-for="user in userlist.middle_level" :key="user.user_id">
							<UserCard :user="user" @card-click="userCardClick" :style="{ width: sliderStatus ? '17%' : '11.38%' }" />
						</template>
					</div> -->
						</div>
					</template>
					<div class="empty-box">
						<a-empty :description="null" v-if="!dataList1.length" />
					</div>
				</div>
			</div>
			<div class="scroll-container middle" ref="scrollContainer1" v-show="activeKey === '2'">
				<div class="inner">
					<template v-for="(userlist, index) in dataList1" :key="index">
						<div style="width: 100%">
							<div :class="`org-name ${index === 0 && 'margin-top-0'}`">{{ userlist.org_name }}</div>
							<div class="user-box">
								<template v-for="user in userlist.middle_level" :key="user.user_id">
									<UserCard :user="user" @card-click="userCardClick1" :style="{ width: sliderStatus ? '12.9%' : '11.38%' }" />
								</template>
							</div>
							<!-- <a-row :gutter="24" v-if="userlist.middle_level.length">
						<div class="tip-link">
							<div class="link" @click="onMidLevelExpand">
								<span :class="`${midLevelVisible ? '' : 'no-expand'} icon`"></span>
								<span class="text"> 中层干部 </span>
							</div>
						</div>
					</a-row> -->
							<!-- <div class="user-box" v-if="midLevelVisible">
						<template v-for="user in userlist.middle_level" :key="user.user_id">
							<UserCard :user="user" @card-click="userCardClick" :style="{ width: sliderStatus ? '17%' : '11.38%' }" />
						</template>
					</div> -->
						</div>
					</template>
					<div class="empty-box">
						<a-empty :description="null" v-if="!dataList1.length" />
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onActivated, shallowRef } from 'vue'
import { getUserListByOrgId } from '@/apis/cadreSystem'
import UserCard from '../components/user-card.vue'
import OrgTree from '@/components/OrgTree.vue'
import { useRouter } from 'vue-router'
import { getUserInfoItem, scrollToTop } from '@/utils/utils'
import BScroll from '@better-scroll/core'
import Pullup from '@better-scroll/pull-up'
import MouseWheel from '@better-scroll/mouse-wheel'
import MeScroll from 'mescroll.js'
BScroll.use(Pullup)
BScroll.use(MouseWheel)

const router = useRouter()
// 侧边栏是否展开
const sliderStatus = ref(true)
// 过度效果结束
const animationEnd = ref(true)
// tab 栏 key
const activeKey = ref('1')
// 当前顶级组织
const currentOrgId = ref(1)
// 搜索用户名
const userName = ref('')
// 班子基础信息
const orgCount = ref<any>({})
//
const breadCrumb = ref<any>([])
// 两个列表逻辑分开
const dataList = shallowRef([])
const currentPage = ref(1)
const mescrollInstance = ref()
// 是否为搜索状态
const isSearch = ref(false)
// 滚动容器
const scrollContainer = ref<any>(null)
// 滚动距离
const scrollInstance = ref<any>(null)
const scrollInstance1 = ref<any>(null)

const dataList1 = shallowRef([])
const currentPage1 = ref(1)
const mescrollInstance1 = ref()
// 滚动容器
const scrollContainer1 = ref<any>(null)
// 当前组织
const currentOrg = ref<any>({
	name: getUserInfoItem('org_nme'),
})

const onExpand = () => {
	sliderStatus.value = !sliderStatus.value
}

const onTransitionEnd = () => {
	animationEnd.value = !animationEnd.value
}

const userCardClick = (item: any) => {
	router.push(`/cadre-portrait/home?user_id=${item.user_id}`)
}

const userCardClick1 = (item: any) => {
	router.push(`/cadre-portrait/cadre-table?user_id=${item.user_id}`)
}

const onSelect = (index: string) => {
	activeKey.value = index
}

const onTreeSelect = (selectedKey: any, node: any) => {
	if (!selectedKey) return

	currentOrgId.value = selectedKey

	currentPage.value = 1
	currentPage1.value = 1

	initData(selectedKey)
	initData1(selectedKey)

	scrollToTop(scrollContainer.value)
	scrollToTop(scrollContainer1.value)

	currentOrg.value = node
}

const onBreadCrumb = (bread: any) => {
	breadCrumb.value = bread
}

const onHandleCrumb = (bread: any) => {
	console.log(bread)
}

const onSearch = () => {
	if (userName.value) {
		isSearch.value = true
	} else {
		isSearch.value = false
	}

	currentPage.value = 1

	initData(currentOrgId.value)
}

onMounted(() => {
	var mescroll = new MeScroll(scrollContainer.value, {
		down: {
			use: false,
		},
		up: {
			htmlNodata: ' ',
			htmlLoading: ' ',
			onScroll: (_mescroll: any, y: number) => {
				scrollInstance.value = y
			},
			callback: async () => {
				try {
					const response = await getUserListByOrgId(currentOrgId.value, { page: currentPage.value, user_name: userName.value })
					if (response.code === 0) {
						const newData = response.data.data
						dataList.value = [...dataList.value, ...newData] as any // 使用 spread 运算符合并数据
						currentPage.value++
						mescroll.endByPage(newData.length, response.data.pages)
					} else {
						console.error('获取数据失败')
					}
				} catch (error) {
					console.error('发生错误', error)
				}
			},
		},
	})
	mescrollInstance.value = mescroll

	var mescroll1 = new MeScroll(scrollContainer1.value, {
		down: {
			use: false,
		},
		up: {
			htmlNodata: ' ',
			htmlLoading: ' ',
			onScroll: (_mescroll: any, y: number) => {
				scrollInstance1.value = y
			},
			callback: async () => {
				try {
					const response = await getUserListByOrgId(currentOrgId.value, { page: currentPage1.value, user_name: userName.value })
					if (response.code === 0) {
						const newData = response.data.data
						dataList1.value = [...dataList1.value, ...newData] as any // 使用 spread 运算符合并数据
						currentPage1.value++

						mescroll1.endByPage(newData.length, response.data.pages)
					} else {
						console.error('获取数据失败')
					}
				} catch (error) {
					console.error('发生错误', error)
				}
			},
		},
	})

	mescrollInstance1.value = mescroll1
})

onActivated(() => {
	scrollInstance.value && scrollContainer.value?.scrollTo(0, scrollInstance.value)
	scrollInstance1.value && scrollContainer1.value?.scrollTo(0, scrollInstance1.value)
})
// 初始化数据
const initData = async (currentOrgId: number) => {
	try {
		const response = await getUserListByOrgId(currentOrgId, { page: currentPage.value, user_name: userName.value })
		if (response.code === 0) {
			dataList.value = response.data.data
			currentPage.value++
			mescrollInstance.value?.resetUpScroll()
		} else {
			console.error('获取初始数据失败')
		}
	} catch (error) {
		console.error('发生错误', error)
	}
}
// 初始化数据
const initData1 = async (currentOrgId: number) => {
	try {
		const response = await getUserListByOrgId(currentOrgId, { page: currentPage1.value })
		if (response.code === 0) {
			dataList1.value = response.data.data
			currentPage1.value++
		} else {
			console.error('获取初始数据失败')
		}
	} catch (error) {
		console.error('发生错误', error)
	}
}
</script>

<style lang="less" scoped>
.cadre-system-information {
	width: 100%;
	height: 100%;
	overflow: hidden;
	display: flex;
	position: relative;
	.translate-animation {
		position: absolute !important;
		transform: translate(-100%);
		z-index: 10;
	}
	.left-ignore {
		position: relative;
		padding: 16px 8px;
		height: 100%;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		transition: transform 0.3s ease-in-out;

		.scroll-box {
			height: 100%;
			overflow: auto;
			& > div {
				width: auto;
			}
			::v-deep(.ant-tree-treenode) {
				margin-bottom: 5px;
			}
		}

		.slider-control {
			position: absolute;
			right: 0;
			top: 40%;
			width: 30px;
			height: 56px;
			background: url(../image/slider-icon.png) no-repeat center / cover;
			cursor: pointer;
		}
		.slider-hidden {
			transform: rotateZ(180deg) translate(-100%);
		}

		// ::v-deep(.ant-tree-title) {
		// 	font-size: 16px;
		// }
		// ::v-deep(.anticon) {
		// 	font-size: 15px;
		// }
	}
	.right {
		display: flex;
		flex-direction: column;
		margin-left: 20px;
		flex: 1;
		height: 100%;
		background-color: #ffffff;
		transition: all 0.3s linear;
		.breadcrumb {
			padding: 13px 32px;

			border-bottom: 1px solid rgba(0, 0, 0, 0.2);
			:deep(.ant-breadcrumb-link) {
				font-size: 24px;
				color: rgba(0, 0, 0, 0.45);
			}
			:deep(.ant-breadcrumb-separator) {
				font-size: 24px;
				color: rgba(0, 0, 0, 0.45);
			}
		}
		&::-webkit-scrollbar {
			display: none;
		}
		:deep(.ant-tabs-tab) {
			font-size: 20px;
		}
		.top-box {
			margin-top: 17px;
			margin-bottom: 27px;
			padding: 0px 32px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.btn-box {
				display: flex;
				gap: 0px 20px;
				.select-btn {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 231px;
					height: 60px;
					border-radius: 6px;
					font-weight: bold;
					font-size: 24px;
					color: rgba(0, 0, 0, 0.65);
					line-height: 28px;
					background: #f8f8f8;
				}
				.active {
					color: #008eff;
					background: rgba(0, 142, 255, 0.1);
				}
			}
		}
		.msg-block {
			:deep(.ant-input-affix-wrapper) {
				width: 421px;
				height: 60px;
				background: rgba(240, 240, 240, 0.4);
				border: none;
				.ant-input {
					background: transparent;
					font-size: 21px;
				}
			}

			.text-box {
				font-size: 18px;
				font-weight: 600;
			}
			.input-box {
				display: flex;
				align-items: center;
				width: 400px;
			}

			.search-icon {
				display: inline-block;
				width: 28px;
				height: 28px;
				background: url(../image/search.png) no-repeat center / 100%;
			}
		}
		.scroll-container {
			flex: 1;
			overflow: auto;
			padding: 0px 32px;
			.search-scroll {
				display: flex;
				flex-wrap: wrap;
			}
			.border-bottom {
			}
			.inner {
				min-height: 100%;

				.empty-box {
					overflow: hidden;
					width: 100%;
					.ant-empty {
						margin: 10% auto 0px;
					}
				}
				.text-box {
					font-size: 16px;
				}

				.org-name {
					display: flex;
					align-items: center;
					font-size: 25px;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: 600;
					color: #000000;
					line-height: 28px;
					&::before {
						margin-right: 6px;
						content: '';
						display: inline-block;
						width: 27px;
						height: 27px;
						background: url('../image/person-icon-1.png') center / cover no-repeat;
					}
				}
				.user-box {
					display: flex;
					flex-wrap: wrap;
					width: 100%;
					gap: 16px 20px;
					border-bottom: 1px dashed rgba(0, 0, 0, 0.2);

					:deep(.small) {
						margin: 0px;
						background: transparent;
					}
				}
			}
		}
		.middle {
			.org-name {
				margin-top: 18px;
			}
			.user-box {
				padding: 27px;
				display: flex;
				flex-wrap: wrap;
				width: 100%;
				gap: 16px 20px;
				border-bottom: 1px dashed rgba(0, 0, 0, 0.2);

				:deep(.small) {
					margin: 0px;
				}
			}
		}
		.org-name-box {
			margin-top: 18px;
			display: flex;
			align-items: center;
			padding: 0px 0px 27px;
			.org-name {
				display: flex;
				align-items: center;
				font-size: 25px;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 600;
				color: #000000;
				line-height: 28px;
				&::before {
					margin-right: 6px;
					content: '';
					display: inline-block;
					width: 27px;
					height: 27px;
					background: url('../image/person-icon-1.png') center / cover no-repeat;
				}
			}
			.text-box {
				margin-left: 12px;
				font-size: 21px;
				line-height: 21px;
				color: #000000;
				.number {
					margin: 0px 5px;
					color: #008eff;
				}
			}
			.user-box {
				padding: 27px 0px;
				display: flex;
				flex-wrap: wrap;
				width: 100%;
				gap: 16px 20px;
				border-bottom: 1px dashed rgba(0, 0, 0, 0.2);

				:deep(.small) {
					margin: 0px;
				}
			}
		}

		.tip-link {
			width: 100%;
			display: flex;
			justify-content: center;
			.link {
				display: flex;
				align-items: center;
				cursor: pointer;
				.icon {
					display: inline-block;
					width: 24px;
					height: 24px;
					background: url('../image/expend.png') no-repeat center / cover;
					transition: all ease-out 0.2s;
				}
				.no-expand {
					transform: rotateZ(180deg);
				}
				.text {
					font-size: 24px;
					margin-left: 10px;
					color: #1296db;
				}
			}
		}
	}
}
.margin-top-0 {
	margin-top: 0 !important;
}
</style>
