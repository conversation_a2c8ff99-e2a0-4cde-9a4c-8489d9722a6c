<template>
	<div class="cadre-system-information">
		<div :class="`left-ignore ${!sliderStatus && 'translate-animation'}`" @transitionend="onTransitionEnd">
			<div class="scroll-box">
				<OrgTree @select="onTreeSelect" @bread-crumb="onBreadCrumb" ref="treeRef" />
			</div>
			<div :class="['slider-control', !sliderStatus && 'slider-hidden']" @click="onExpand"></div>
		</div>
		<div class="right">
			<div class="control-box">
				<a-breadcrumb separator=">" class="breadcrumb">
					<a-breadcrumb-item v-for="item in breadCrumb" :key="item.org_id" @click="onHandleCrumb(item)">{{ item.name }}</a-breadcrumb-item>
				</a-breadcrumb>
				<PannueList @active="onChangePageType" />
			</div>
			<div class="top-box">
				<div class="btn-box">
					<div class="select-btn" :class="{ active: activeKey === '3' }" @click="onSelect('3')">市管干部</div>
					<div class="select-btn" :class="{ active: activeKey === '1' }" @click="onSelect('1')">县管干部</div>
					<div class="select-btn" :class="{ active: activeKey === '2' }" @click="onSelect('2')">中层干部</div>
				</div>
				<div class="msg-block">
					<!-- <div class="text-box">
						总职数<span class="number">{{ orgCount.total }}</span
						>人、正职<span class="number">{{ orgCount.real_leader }}</span
						>人、正职空缺<span class="number">{{ orgCount.real_leader_vacancy }}</span
						>人、副职<span class="number">{{ orgCount.vice_leader }}</span
						>人、副职空缺<span class="number">{{ orgCount.vice_leader_vacancy }}</span
						>人
					</div> -->
					<div class="input-box">
						<a-input v-model:value="userName" placeholder="输入姓名" @focus="onFocus" @blur="onBlur">
							<template #prefix>
								<span class="search-icon"></span>
							</template>
						</a-input>
						<a-button type="link" @click="onSearch">搜索</a-button>
					</div>
					<div class="pre-warn-cadre" @click="onPreWarn">预警干部</div>
				</div>
			</div>
			<div class="filter-box">
				<SortFilter :filters="filters" :on-select="onSortFilter" />
			</div>
			<div class="scroll-container" ref="scrollContainer">
				<div class="search-scroll">
					<template v-for="(userlist, index) in dataList" :key="index">
						<div
							style="width: 100%"
							class="border-bottom"
							v-if="filterData(userlist, activeKey)?.length || (!isSearch && activeKey === '1' && userlist.vacancy?.length)"
						>
							<div :class="`org-name-box ${index === 0 && 'margin-top-0'}`">
								<div class="org-name">{{ userlist.org_name }}</div>
								<div class="text-box">
									( 总职数<span class="number"> {{ userlist.total }} </span>人、正职<span class="number"> {{ userlist.real_leader }} </span
									>人、正职空缺<span class="number"> {{ userlist.real_leader_vacancy }} </span>人、副职<span class="number">
										{{ userlist.vice_leader }} </span
									>人、副职空缺<span class="number"> {{ userlist.vice_leader_vacancy }} </span>人 )
								</div>
							</div>
							<div class="user-box">
								<TableListBox
									:type="listType"
									:data="filterData(userlist, activeKey)"
									:vacancy="!isSearch && activeKey === '1' ? userlist.vacancy : []"
									:slider-status="sliderStatus"
									:middle="activeKey === '3'"
									:show-position="activeKey !== '3'"
									:sort="sortFilter"
								/>
							</div>
						</div>
					</template>
				</div>
				<div class="empty-box">
					<a-empty :description="null" v-if="!dataList.length" />
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { getUserListByOrgId } from '@/apis/cadreSystem'
import OrgTree from '@/components/OrgTree.vue'
import PannueList from '@/components/PannueList.vue'
import SortFilter from '@/components/SortFilter.vue'
import { getUserInfoItem, scrollToTop } from '@/utils/utils'
import BScroll from '@better-scroll/core'
import MouseWheel from '@better-scroll/mouse-wheel'
import Pullup from '@better-scroll/pull-up'
import MeScroll from 'mescroll.js'
import { onActivated, onMounted, ref, shallowRef } from 'vue'
import { useRouter } from 'vue-router'
import TableListBox from './components/TableListBox.vue'
BScroll.use(Pullup)
BScroll.use(MouseWheel)

const router = useRouter()
// 侧边栏是否展开
const sliderStatus = ref(true)
// 过度效果结束
const animationEnd = ref(true)
// tab 栏 key
const activeKey = ref('1')
// 当前顶级组织
const currentOrgId = ref(1)
// 搜索用户名
const userName = ref('')
// 班子基础信息
const orgCount = ref<any>({})
//
const breadCrumb = ref<any>([])
// 两个列表逻辑分开
const dataList = shallowRef([])
const currentPage = ref(1)
const mescrollInstance = ref()
// 是否为搜索状态
const isSearch = ref(false)
// 滚动容器
const scrollContainer = ref<any>(null)
// 滚动距离
const scrollInstance = ref<any>(null)
const scrollInstance1 = ref<any>(null)

const dataList1 = shallowRef([])
const currentPage1 = ref(1)
const mescrollInstance1 = ref()
const listType = ref('2')
const filters = [
	{ label: '年龄', key: 'age' },
	{
		label: '任现职务时间',
		key: 'current_job_time',
	},
	{
		label: '干部指数',
		key: 'cadre_index',
	},
]
const sortFilter = ref({})

const treeRef = ref<any>()

// 滚动容器
const scrollContainer1 = ref<any>(null)
// 当前组织
const currentOrg = ref<any>({
	name: getUserInfoItem('org_nme'),
})

const onExpand = () => {
	sliderStatus.value = !sliderStatus.value
}

const onTransitionEnd = () => {
	animationEnd.value = !animationEnd.value
}

const filterData = (data: any, type: any) => {
	switch (type) {
		case '1':
			return data.leader_info
		case '2':
			return data.middle_level
		case '3':
			return data.admin_leader_info
	}
}

const onSelect = (index: string) => {
	activeKey.value = index

	scrollContainer.value.scrollTo(0, 0)
}
const onSortFilter = (filter: any) => {
	sortFilter.value = filter
}
const getSearchData = (data: any, key = 'leader_info') => {
	return data.flatMap((item: any) => {
		return item[key]
	})
}
const onTreeSelect = (selectedKey: any, node: any) => {
	if (!selectedKey) return

	if (!userName.value) {
		isSearch.value = false
	}

	currentOrgId.value = selectedKey

	currentPage.value = 1

	initData(selectedKey)

	scrollToTop(scrollContainer.value)

	currentOrg.value = node
}

const onBreadCrumb = (bread: any) => {
	breadCrumb.value = bread
}
const onHandleCrumb = (bread: any) => {
	console.log('🚀 ~ onHandleCrumb ~ bread:', bread)
	treeRef.value.onTreeSelect(
		[bread.org_id],
		{
			node: {
				dataRef: bread,
			},
		},
		'api'
	)
}

const onSearch = async () => {
	currentPage.value = 1

	await initData(currentOrgId.value)

	if (!userName.value) {
		isSearch.value = false
	} else {
		isSearch.value = true
	}

	scrollContainer.value.scrollTo(0, 0)
}

const keyBoard = (event: any) => {
	if (event.keyCode === 13) {
		// 按下了Enter键，执行相应操作
		onSearch()
	}
}

const onBlur = () => {
	document.removeEventListener('keydown', keyBoard)
}

const onFocus = () => {
	document.addEventListener('keydown', keyBoard)
}

const onChangePageType = (type: string) => {
	listType.value = type

	scrollContainer.value.scrollTo(0, 0)
}

const onPreWarn = () => {
	router.push({
		path: '/cadre-warn',
		query: {
			org_id: currentOrgId.value,
		},
	})
}

onMounted(() => {
	var mescroll = new MeScroll(scrollContainer.value, {
		down: {
			use: false,
		},
		up: {
			htmlNodata: ' ',
			htmlLoading: ' ',
			onScroll: (_mescroll: any, y: number) => {
				scrollInstance.value = y
			},
			callback: async () => {
				try {
					const response = await getUserListByOrgId(currentOrgId.value, { page: currentPage.value, user_name: userName.value, page_size: 10 })
					if (response.code === 0) {
						const newData = response.data.data

						dataList.value = [...dataList.value, ...newData] as any // 使用 spread 运算符合并数据
						currentPage.value++
						mescroll.endByPage(newData.length, response.data.pages)
					} else {
						console.error('获取数据失败')
					}
				} catch (error) {
					console.error('发生错误', error)
				}
			},
		},
	})
	mescrollInstance.value = mescroll
})

onActivated(() => {
	scrollInstance.value && scrollContainer.value?.scrollTo(0, scrollInstance.value)
})
// 初始化数据
const initData = async (currentOrgId: number) => {
	try {
		const response = await getUserListByOrgId(currentOrgId, { page: currentPage.value, user_name: userName.value })
		if (response.code === 0) {
			dataList.value = response.data.data

			currentPage.value++

			mescrollInstance.value?.resetUpScroll()
		} else {
			console.error('获取初始数据失败')
		}
	} catch (error) {
		console.error('发生错误', error)
	}
}
</script>

<style lang="less" scoped>
.cadre-system-information {
	width: 100%;
	height: 100%;
	overflow: hidden;
	display: flex;
	position: relative;
	.translate-animation {
		position: absolute !important;
		transform: translate(-100%);
		z-index: 10;
	}
	.left-ignore {
		position: relative;
		padding: 16px 8px;
		height: 100%;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		transition: transform 0.3s ease-in-out;

		.scroll-box {
			height: 100%;
			overflow: auto;
			& > div {
				width: auto;
			}
			::v-deep(.ant-tree-treenode) {
				margin-bottom: 5px;
			}
		}

		.slider-control {
			position: absolute;
			right: 0;
			top: 40%;
			width: 30px;
			height: 56px;
			background: url(@/assets/images/slider-icon.png) no-repeat center / cover;
			cursor: pointer;
		}
		.slider-hidden {
			transform: rotateZ(180deg) translate(-100%);
		}

		// ::v-deep(.ant-tree-title) {
		// 	font-size: 16px;
		// }
		// ::v-deep(.anticon) {
		// 	font-size: 15px;
		// }
	}
	.right {
		display: flex;
		flex-direction: column;
		margin-left: 20px;
		flex: 1;
		height: 100%;
		background-color: #ffffff;
		transition: all 0.3s linear;
		.control-box {
			display: flex;
			justify-content: space-between;
			width: 100%;
			border-bottom: 1px solid rgba(0, 0, 0, 0.2);
			padding: 13px 32px;

			.breadcrumb {
				:deep(.ant-breadcrumb-link) {
					font-size: 24px;
					color: rgba(0, 0, 0, 0.45);
				}
				:deep(.ant-breadcrumb-separator) {
					font-size: 24px;
					color: rgba(0, 0, 0, 0.45);
				}
			}
		}

		&::-webkit-scrollbar {
			display: none;
		}
		:deep(.ant-tabs-tab) {
			font-size: 20px;
		}
		.top-box {
			padding: 17px 32px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.btn-box {
				display: flex;
				gap: 0px 20px;
				.select-btn {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 180px;
					height: 60px;
					border-radius: 6px;
					font-weight: bold;
					font-size: 24px;
					color: rgba(0, 0, 0, 0.65);
					line-height: 28px;
					background: #f8f8f8;
				}
				.active {
					color: #008eff;
					background: rgba(0, 142, 255, 0.1);
				}
			}
		}
		.msg-block {
			display: flex;
			align-items: center;
			:deep(.ant-input-affix-wrapper) {
				width: 421px;
				height: 60px;
				background: rgba(240, 240, 240, 0.4);
				border: none;
				.ant-input {
					background: transparent;
					font-size: 21px;
				}
			}

			.text-box {
				font-size: 18px;
				font-weight: 600;
			}
			.input-box {
				display: flex;
				align-items: center;
				width: 400px;
				background: rgba(240, 240, 240, 0.4);
				:deep(.ant-input-affix-wrapper) {
					background: transparent;
				}
				:deep(.ant-btn-link) {
					padding: 0 18px;
					font-size: 21px;
					color: #008eff;
					line-height: 25px;
					position: relative;
					&::before {
						content: '';
						display: inline-block;
						width: 1px;
						height: 38px;
						background: rgba(0, 142, 255, 0.25);
						position: absolute;
						top: 50%;
						left: 0px;
						transform: translateY(-50%);
					}
				}
			}
			.pre-warn-cadre {
				margin-left: 31px;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 143px;
				height: 60px;
				background: #ffffff;
				border-radius: 6px 6px 6px 6px;
				border: 1px solid #008eff;

				font-weight: bold;
				font-size: 24px;
				color: #008eff;
				line-height: 28px;
				text-align: left;
				font-style: normal;
			}

			.search-icon {
				display: inline-block;
				width: 28px;
				height: 28px;
				background: url(../image/search.png) no-repeat center / 100%;
			}
		}
		.filter-box {
			padding: 0px 33px 23px;
		}
		.scroll-container {
			flex: 1;
			overflow: auto;
			padding: 0px 32px;
			.search-scroll {
				display: flex;
				flex-wrap: wrap;
			}
			.border-bottom {
			}

			.empty-box {
				overflow: hidden;
				width: 100%;
				.ant-empty {
					margin: 10% auto 0px;
				}
			}
			.inner {
				min-height: 100%;

				.text-box {
					font-size: 16px;
				}

				.org-name {
					display: flex;
					align-items: center;
					font-size: 25px;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: 600;
					color: #000000;
					line-height: 28px;
					&::before {
						margin-right: 6px;
						content: '';
						display: inline-block;
						width: 27px;
						height: 27px;
						background: url('../image/person-icon-1.png') center / cover no-repeat;
					}
				}
				.user-box {
					display: flex;
					flex-wrap: wrap;
					width: 100%;
					gap: 16px 20px;
					border-bottom: 1px dashed rgba(0, 0, 0, 0.2);

					:deep(.small) {
						margin: 0px;
						background: transparent;
					}
				}
			}
		}
		.middle {
			.org-name {
				margin-top: 18px;
			}
			.user-box {
				padding: 27px;
				display: flex;
				flex-wrap: wrap;
				width: 100%;
				gap: 16px 20px;
				border-bottom: 1px dashed rgba(0, 0, 0, 0.2);

				:deep(.small) {
					margin: 0px;
				}
			}
		}
		.org-name-box {
			margin-top: 18px;
			display: flex;
			align-items: center;
			padding: 0px 0px 27px;
			.org-name {
				display: flex;
				align-items: center;
				font-size: 25px;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 600;
				color: #000000;
				line-height: 28px;
				&::before {
					margin-right: 6px;
					content: '';
					display: inline-block;
					width: 27px;
					height: 27px;
					background: url('../image/person-icon-1.png') center / cover no-repeat;
				}
			}
			.text-box {
				margin-left: 12px;
				font-size: 21px;
				line-height: 21px;
				color: #000000;
				.number {
					margin: 0px 5px;
					color: #008eff;
				}
			}
			.user-box {
				padding: 27px 0px;
				display: flex;
				flex-wrap: wrap;
				width: 100%;
				gap: 16px 20px;
				border-bottom: 1px dashed rgba(0, 0, 0, 0.2);

				:deep(.small) {
					margin: 0px;
				}
			}
		}

		.tip-link {
			width: 100%;
			display: flex;
			justify-content: center;
			.link {
				display: flex;
				align-items: center;
				cursor: pointer;
				.icon {
					display: inline-block;
					width: 24px;
					height: 24px;
					background: url('../image/expend.png') no-repeat center / cover;
					transition: all ease-out 0.2s;
				}
				.no-expand {
					transform: rotateZ(180deg);
				}
				.text {
					font-size: 24px;
					margin-left: 10px;
					color: #1296db;
				}
			}
		}
	}
}
.margin-top-0 {
	margin-top: 0 !important;
}
</style>
