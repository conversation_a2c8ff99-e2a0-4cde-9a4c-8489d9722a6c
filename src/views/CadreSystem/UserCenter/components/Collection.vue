<template>
	<div class="collection">
		<div class="detail" v-if="detailVisible">
			<div class="collect-header">
				<div class="back" @click="onBack">返回</div>
				<div class="line"></div>
				<div class="collect-name">
					{{ current_collection.name }}
				</div>
			</div>
			<div class="filter-box m-top-10">
				<CheckBox :options="options" @change="onChangeFilter" :value="userType" />
				<PannueList :value="listType" @active="onChangePageType" />
			</div>
			<div class="collect-list m-top-10">
				<!-- <template v-for="(item, index) in favoriteList" :key="index">
					<user-card type="collect" @icon-click="onIconClick" @card-click="onCardClick" :user="item" />
				</template> -->
				<TableListBox :type="listType" :data="searchData" @icon-click="onIconClick" @card-click="onCardClick" :slider-status="sliderStatus" />
			</div>
		</div>
		<template v-else>
			<div class="collect-header-search">
				<div class="left"><a-button type="primary" :icon="h(PlusOutlined)" @click="onCreate">新建收藏夹</a-button></div>
				<div class="right-box">
					<Search @search="onSearch" @clear="onClear" />
				</div>
			</div>
			<div class="margin-top-20"></div>
			<a-table :data-source="favoritesList" :columns="columns" :pagination="{ ...pagination, onChange }">
				<template #bodyCell="{ column, record }">
					<template v-if="column.key === 'operation'"
						><a @click="onDetails(record)">详情</a> <a style="margin: 0px 20px" @click="onEdit(record)">编辑</a>

						<a-popconfirm title="确认删除?" ok-text="确认" cancel-text="取消" @confirm="onDelete(record)">
							<a>删除</a>
						</a-popconfirm>
					</template>
				</template>
			</a-table>
		</template>
		<a-modal class="folder-modal" :closable="false" :visible="visible" width="" destroyOnClose :footer="null" @cancel="onClose">
			<Folder @close="onClose" @success="onSuccess" :modal-type="modalType" :editor="editor" />
		</a-modal>
	</div>
</template>

<script lang="ts" setup>
import { ref, h, reactive, onActivated, computed } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'

import { getUserInfoItem } from '@/utils/utils'
import { historyPush } from '@/utils/history'
import { getCollectionList, deleteCollection, getFavoriteList, getUsersByIds, deleteFavorite, addFavorite } from '@/apis/user-center'

import Search from './Search.vue'
import Folder from '@/components/Folder.vue'
import UserCard from '../../components/user-card.vue'
import CheckBox from '@/components/CheckBox.vue'
import TableListBox from '../../components/TableListBox.vue'
interface Favorites {
	favoritesId: number
	name?: string | null
	description?: string | null
	createTime?: string | null
	count: number
}

// //
const options = [
	{
		label: '县管正职',
		value: '1',
	},
	{
		label: '县管副职',
		value: '2',
	},
	{
		label: '中层干部',
		value: '3',
	},
]

const user_id = getUserInfoItem('_uid')

const visible = ref(false)
// 查看详情
const detailVisible = ref(false)

const editor = reactive({
	editor: false,
	editorInfo: {},
})

const pagination = reactive({
	total: 0,
	pageSize: 10,
	page: 1,
})

const current_collection = ref<Favorites>({
	favoritesId: 0,
	name: '',
	description: '',
	createTime: '',
	count: 0,
})

const favoriteList = ref<any>([])

const userType = ref([])
// 侧边栏是否展开
const sliderStatus = ref(true)
const favoritesList = ref<Array<Favorites>>([])

const modalType = ref()
const listType = ref('2')

const columns = [
	{
		dataIndex: 'name',
		key: 'name',
		align: 'center',
		title: '收藏夹名称',
	},
	{
		dataIndex: 'description',
		key: 'description',
		align: 'center',
		title: '收藏夹描述',
	},
	{
		dataIndex: 'createTime',
		key: 'createTime',
		align: 'center',
		title: '创建时间',
	},
	{
		dataIndex: 'count',
		key: 'count',
		align: 'center',
		title: '收藏人数',
	},
	{
		dataIndex: 'operation',
		key: 'operation',
		align: 'center',
		title: '操作',
	},
]

const onChangeFilter = (value) => {
	userType.value = value
}

const onDetails = (record: Favorites) => {
	current_collection.value = record

	detailVisible.value = true

	initFavoriteList(record.favoritesId)
}

const searchData = computed(() => {
	const _userType = userType.value

	if (!_userType.length) return favoriteList.value

	const data = favoriteList.value

	return data.filter((item: any) => userType.value.includes(String(item.has_divided)))
})

const onChangePageType = (type: string) => {
	listType.value = type
}

const onCreate = () => {
	modalType.value = 'insert'
	visible.value = true
}
const onClose = () => {
	visible.value = false
}
const onSuccess = () => {
	visible.value = false

	initData()
}
const searchValue = ref('')

const onSearch = (value: string) => {
	searchValue.value = value

	initData()
}

const onClear = () => {
	searchValue.value = ''
}

const onBack = () => {
	detailVisible.value = false

	favoriteList.value = []

	current_collection.value = {} as any

	userType.value = []

	listType.value = '2'

	initData()
}

const onEdit = (record: any) => {
	modalType.value = 'editor'
	editor.editor = true
	editor.editorInfo = record
	visible.value = true
}

const onDelete = async (record: Favorites) => {
	await deleteCollection(record.favoritesId)
	if (favoriteList.value.length === 1 && pagination.page > 1) {
		pagination.page = pagination.page - 1
	}
	initData()
}

const onChange = (page: number) => {
	pagination.page = page

	initData()
}

const onIconClick = async (data: any) => {
	await deleteFavorite(current_collection.value?.favoritesId, data.user_id)
	favoriteList.value = favoriteList.value.filter((item: any) => item.user_id !== data?.user_id)
}

const onCardClick = (data: any) => {
	historyPush(`/cadre-portrait/home?user_id=${data.user_id}`)
}

const initFavoriteList = async (favoritesId: number) => {
	const res = await getFavoriteList(favoritesId)

	const data = res.data

	if (data?.length) {
		const _res = await getUsersByIds({ user_ids: data?.join(',') })

		const list: any = []
		data.map((item: any) => {
			const current = _res.data.find((item1: any) => {
				return item === item1.user_id
			})
			if (current) {
				current.hasFavorite = 1

				list.push(current)
			}
		})

		favoriteList.value = list
	}
}

const initData = async () => {
	const res: any = await getCollectionList(user_id, pagination.page, searchValue.value)

	favoritesList.value = res.data

	pagination.total = res.total
}
initData()

onActivated(() => {
	initData()
	// 新增收藏後回來刷新
	current_collection.value?.favoritesId && onDetails(current_collection.value)
})
</script>

<style lang="scss" scoped>
.collection {
	overflow-y: auto;
	height: 100%;
	.collect-header-search {
		display: flex;
		justify-content: space-between;
	}
	.collect-header {
		display: flex;
		align-items: center;
		font-size: 20px;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
		color: rgba(0, 0, 0, 0.9);
		.back {
			display: flex;
			align-items: center;
			margin-right: 20px;
			&::before {
				margin-right: 20px;
				content: '';
				display: inline-block;
				width: 18px;
				height: 19px;
				background: url('@/assets/images/back-2.png') no-repeat center / cover;
			}
		}
		.line {
			margin: 0 20px;
			width: 1px;
			height: 17px;
			border: 1px dashed #ccc;
		}
		.collect-name {
			font-weight: bold;
		}
		.left {
			font-size: 22px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: rgba(0, 142, 255, 0.9);
			line-height: 26px;
		}
		.right-box {
		}
	}
	.filter-box {
		display: flex;
		justify-content: space-between;
	}
	.detail {
		.collect-list {
			display: flex;
			flex-wrap: wrap;
		}
	}
}
.margin-top-20 {
	margin-top: 20px;
}
:deep(.ant-btn) {
	height: 50px;
	font-size: 22px;
}

:deep(.ant-table-thead) > tr > th {
	font-size: 24px;
}

:deep(.ant-table-tbody) > tr > td {
	font-size: 22px;
}

:deep(.ant-empty) {
	.ant-empty-description {
		font-size: 20px;
	}
}
</style>

<style lang="scss">
.folder-modal {
	width: 752px;
	.ant-modal-body {
		padding: 0px !important;
	}
}
</style>
