<template>
	<div class="search">
		<a-input v-model:value="value" v-bind="$attrs">
			<template #prefix>
				<span class="search-icon"></span>
			</template>
			<template #suffix>
				<span class="line" />
				<span class="cancel" @click="onClear"></span>
			</template>
		</a-input>
		<a-button type="primary" @click="onSearch">搜索</a-button>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const value = ref('')

const emits = defineEmits(['search', 'clear'])

const onClear = () => {
	value.value = ''
	emits('clear', value)
}

const onSearch = () => {
	emits('search', value.value)
}
</script>

<style lang="scss" scoped>
.search {
	display: flex;
	width: 560px;
	.search-icon {
		display: inline-block;
		width: 28px;
		height: 28px;
		background: url(../../image/search.png) no-repeat center / 100%;
	}
	.cancel {
		display: inline-block;
		width: 28px;
		height: 28px;
		background: url(../../image/cancel.png) no-repeat center / 100%;
		cursor: pointer;
	}
	.line {
		margin-right: 10px;
		display: inline-block;
		width: 1px;
		height: 28px;
		background-color: #d9d9d9;
	}
	button {
		margin-left: 5px;
		width: 100px;
		height: 50px;
		background: #008eff;
		border-radius: 4px 4px 4px 4px;
		opacity: 1;
	}
}
:deep(.ant-input) {
	font-size: 22px;
}
</style>
