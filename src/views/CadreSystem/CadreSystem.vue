<template>
	<div class="cadre-system">
		<p-header :title="$route.meta.title || ($route.meta.hiddenMenu ? '动议名单' : '县管干部')" />
		<div class="menu" v-if="!$route.meta.hiddenMenu">
			<div
				:class="`menu-item ${activeRef === item.key ? 'active-menu-item' : ''}`"
				v-for="(item, index) in menu"
				:key="index"
				@click="handeClickMenu(item)"
			>
				<div class="menu-item__inner">
					<img class="icon" :src="activeRef === item.key ? item.activeIcon : item.icon" />
					<span class="text">{{ item.label }}</span>
				</div>
			</div>
		</div>
		<div class="content">
			<router-view v-slot="{ Component }">
				<!-- <transition :name="transitionName" mode="in-out"> -->
				<keep-alive :exclude="['UserCenter']">
					<component :is="Component" />
				</keep-alive>
				<!-- </transition> -->
			</router-view>
			<!-- <router-view /> -->
		</div>

		<div class="ai-trigger" @click="showChatClick">
			<div class="character"></div>
			<AiChat @close="closeChatClick" v-if="showChat" :is-awake="isAwake" @wake-up="handleWakeUp" ref="aiChatRef" />
		</div>
	</div>
</template>
<script lang="ts" setup>
import { useComparative } from '@/store/comparative'
import useKeepAlive from '@/store/keepalive'
import { message } from 'ant-design-vue'
import { onActivated, onDeactivated, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import AiChat from '../AiChat/AiChat.vue'
import chaxunActivePng from './image/chaxun-active.png'
import chaxunPng from './image/chaxun.png'
import usericonActive from './image/person-icon-active.png'
import usericon from './image/person-icon.png'
import tongjiActivePng from './image/tongji-active.png'
import tongjiPng from './image/tongji.png'
import userActivePng from './image/user-active.png'
import userPng from './image/user.png'
const activeRef = ref(0)
const route = useRoute()

const router = useRouter()
//
const comparative = useComparative()
comparative.initConfigIds()

const keepalive = useKeepAlive()
// 过渡名称
const transitionName = ref()
// 清空页面路由缓存
const showChat = ref(false)
const isAwake = ref(false) // 是否被唤醒
keepalive.push('CadreSystem')

const menu = [
	{
		label: '干部信息',
		path: '/cadre-system/information',
		icon: userPng,
		activeIcon: userActivePng,
		key: 0,
	},
	{
		label: '干部查询',
		path: '/cadre-system/search',
		icon: chaxunPng,
		activeIcon: chaxunActivePng,
		key: 1,
	},
	{
		label: '统计分析',
		path: '/cadre-system/statistics',
		icon: tongjiPng,
		activeIcon: tongjiActivePng,
		key: 2,
	},
	{
		label: '个人中心',
		path: '/cadre-system/user-center',
		icon: usericon,
		activeIcon: usericonActive,
		key: 3,
	},
]

const handeClickMenu = (item: any) => {
	activeRef.value = item.key

	router.replace({
		path: item.path,
		query: {
			_h: route.query._h,
		},
	})
}
//#region ====================== AI会话显示 ======================
const aiChatRef = ref<any>()
//是否显示聊天窗口
const showChatClick = () => {
	showChat.value = true
}
const closeChatClick = () => {
	showChat.value = false
	isAwake.value = false

}
const handleWakeUp = (state) => {
	showChat.value = true
	isAwake.value = state
	aiChatRef.value?.registerCallbacks()
}
//#endregion
const confirm = () => {
	sessionStorage.setItem('userInfo', '')

	message.info('退出成功')

	router.replace('/login')
}

const cancel = (e: MouseEvent) => {
	console.log(e)
}
const _menu = menu.concat([
	{
		label: '搜索结果',
		path: '/cadre-system/search-result',
		key: 1,
	},
] as any)
// 组件加载时添加初始消息
watch(
	router.currentRoute,
	(newV, oldV) => {
		const { path } = newV
		const menuItem = _menu.find((item: any) => {
			return item.path === path
		}) as { key: number }
		// 老路由
		const oldMenu = _menu.find((item: any) => {
			return item.path === oldV?.path
		}) as { key: number }

		activeRef.value = menuItem?.key

		if (oldMenu?.key !== undefined) {
			transitionName.value = menuItem?.key < oldMenu?.key ? 'a_right' : 'a_left'
		} else {
			transitionName.value = undefined
		}
	},
	{
		immediate: true,
	}
)
//#region ====================== 初始化 ======================
//全局注册唤醒回调
onMounted(() => {
	window.wak_up = (error) => {
		if (error) {
			message.error(`语音服务异常: ${error.message}`)
			isAwake.value = false
			showChat.value = false
		} else {
			isAwake.value = true
			showChat.value = true
			// message.success('唤醒成功！')
		}
	}
})

// 组件卸载时清理
onUnmounted(() => {
	// 清除全局唤醒回调
	window.wak_up = undefined
})
onDeactivated(() => {
	transitionName.value = undefined
})

onActivated(() => {
	keepalive.remove('CadrePortrait')

	comparative.updateComparative([])
})
//#endregion
</script>

<style scoped lang="less">
.cadre-system {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	background: #f5f5f5;
	.header {
		display: flex;
		align-items: center;
		padding: 0 32px;
		width: 100%;
		height: 68px;
		background: url(./image/header.png) center / cover no-repeat;

		.title {
			flex: 1;
			text-align: center;
			font-size: 27px;
			line-height: 32px;
			font-weight: 600;
			color: #ffffff;
		}
		.common {
			display: flex;
			align-items: center;
			cursor: pointer;
			.icon {
				margin-right: 10px;
				display: inline-block;
				width: 24px;
				height: 24px;
			}
			.text {
				font-size: 22px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #ffffff;
			}
		}
		.back {
			.icon {
				background: url(./image/back.png) center / cover no-repeat;
			}
		}
		.login-out {
			justify-content: flex-end;
			.icon {
				width: 20px;
				height: 20px;
				background: url(./image/login-out.png) center / contain no-repeat;
			}
		}
	}
	.menu {
		width: 100%;
		display: flex;
		height: 72px;
		background: #ffffff;
		.menu-item {
			display: flex;
			align-items: center;
			justify-content: center;
			border-bottom: 4px solid transparent;
			flex: 1;
			height: 72px;
			cursor: pointer;
			&:nth-child(2) > div {
				border-left: 1px solid #e8e8e8;
				border-right: 1px solid #e8e8e8;
			}
			&__inner {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;

				.icon {
					margin-right: 7px;
					width: 36px;
					height: 36px;
				}
				.text {
					// font-size: 26px;
					font-size: 23px;
					font-family: PingFang SC-Medium, PingFang SC;
					color: #222222;
					line-height: 23px;
				}
			}
		}
		.active-menu-item {
			border-bottom-color: #008eff;

			background: linear-gradient(180deg, rgba(0, 142, 255, 0) 0%, rgba(0, 142, 255, 0.13) 100%);
			.text {
				font-weight: bold;
				color: #008eff;
			}
		}
	}
	.content {
		position: relative;
		margin: 20px 0 0px 0;
		flex: 1;
		width: 100%;
		overflow: hidden;
	}
	.a_right-enter-active,
	.a_right-leave-active,
	.a_left-enter-active,
	.a_left-leave-active {
		transition: all 0.25s cubic-bezier(0.93, -0.01, 0.74, 0.79);
	}
	.a_right-enter,
	.a_right-leave-to {
		transform: translateX(100%);
	}
	.a_left-enter,
	.a_left-leave-to {
		transform: translateX(-100%);
	}
}
// 浮动小人样式
.ai-trigger {
	position: fixed;
	right: 40px;
	bottom: 40px;
	width: 80px;
	height: 80px;
	cursor: pointer;
	z-index: 1000;
	transition: all 0.3s ease;

	// &:hover {
	// 	transform: scale(1.1) translateY(-10px);
	// }

	.character {
		width: 100%;
		height: 100%;
		background: url('@/assets/images/ai-list/ai_people.webp') no-repeat center center / 100% 100%;
		animation: float 3s ease-in-out infinite;
	}

	&.hover-effect .character {
		animation: bounce 0.5s ease infinite;
	}
}
// 动画效果
@keyframes float {
	0%,
	100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-20px);
	}
}

@keyframes bounce {
	0%,
	100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-10px);
	}
}

.slide-fade-enter-active {
	transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
	transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
	transform: translateY(20px);
	opacity: 0;
}
</style>
