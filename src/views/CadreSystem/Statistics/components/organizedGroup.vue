<template>
	<!-- // 2025-3-10   原页面没有这些，已调整"班子结构说明"页面移入到统计分析 -->
	<div class="organizedGroup">
		<div class="organizedGroup-item">
			<div class="item-title">班子结构预警</div>
			<div class="flex gap align-normal">
				<div class="item-wrap flex-item" style="overflow: hidden">
					<div class="content">
						<div class="team-rule">
							<a-tabs v-model="activeTab" class="tabs-container" animated="{false}" @tabClick="handleTabChange">
								<a-tab-pane key="1" tab="乡镇" class="tab-content">
									<a-descriptions bordered :column="1">
										<a-descriptions-item :label="item.label" v-for="(item, index) in baseData.townDescriptionsList" :key="index">
											<div class="value-box">
												<template v-if="item.children">
													<div class="value-item" v-for="(value, index) in item.children" :key="index">
														<span class="text">{{ value }}</span>
													</div>
												</template>
												<template v-else>
													<div class="value-item">
														<span class="text">{{ item.value }}</span>
													</div>
												</template>
											</div>
										</a-descriptions-item>
										<a-descriptions-item v-if="type === 0">
											<div class="structure-box">
												<div class="structure-item" v-for="(item, index) in baseData.townStrcut" :key="index">
													<img :src="item.icon" alt="" class="img" />
													<div class="structure-label">{{ item.label }}</div>
												</div>
											</div>
										</a-descriptions-item>
									</a-descriptions>
								</a-tab-pane>
								<a-tab-pane key="2" tab="部门" class="tab-content">
									<a-descriptions bordered :column="1">
										<a-descriptions-item :label="item.label" v-for="(item, index) in baseData.DescriptionsList" :key="index">
											<div class="value-box">
												<template v-if="item.children">
													<div class="value-item" v-for="(value, index) in item.children" :key="index">
														<span class="text">{{ value }}</span>
													</div>
												</template>
												<template v-else>
													<div class="value-item">
														<span class="text">{{ item.value }}</span>
													</div>
												</template>
											</div>
										</a-descriptions-item>
										<a-descriptions-item class="self-row" v-if="type === 0">
											<div class="structure-box">
												<div class="structure-item" v-for="(item, index) in baseData.strcut" :key="index">
													<img :src="item.icon" alt="" class="img" />
													<div class="structure-label">{{ item.label }}</div>
												</div>
											</div>
										</a-descriptions-item>
									</a-descriptions>
								</a-tab-pane>
							</a-tabs>
						</div>
					</div>
				</div>
				<div class="flex-item flex-direction" style="overflow: hidden">
					<div class="item-wrap" style="width: 100%">
						<div class="flex align-center set-just">
							<div style="flex: 0 0 65%">
								<v-chart :option="option1" style="height: 250px; width: 100%" autoresize />
							</div>
							<div class="warn-total">
								<div class="label">预警总数</div>
								<div class="number">{{ total || '-' }}</div>
							</div>
						</div>
					</div>
					<div class="item-wrap flex-item card-wrap" style="width: 100%">
						<div class="item-card" v-for="item in departmentalData" :key="item">
							<div class="card-label">
								<span class="label">{{ cardTitle(item.type) }}</span>
								<span class="value">
									<span class="number">{{ item.number }}</span>
									<span class="unit">个</span>
								</span>
							</div>
							<div class="card-warn">
								<svg
									t="1697450179918"
									class="icon"
									viewBox="0 0 1024 1024"
									version="1.1"
									xmlns="http://www.w3.org/2000/svg"
									p-id="4025"
									width="40"
									height="40"
								>
									<path
										d="M175.726 934.787v-316.26c0-189.18 153.404-342.612 342.774-342.612s342.775 153.433 342.775 342.612v316.26h117.012c24.3 0 44 19.7 44 44s-19.7 44-44 44H44.747c-24.301 0-44-19.7-44-44s19.699-44 44-44h130.98z m367.588-520.804L374.237 692.332h135.221l-33.855 208.762L644.68 622.745H509.457l33.856-208.762h0.001z m259.29-305.76c15.875 9.237 21.299 29.622 12.156 45.488l-60.778 105.636-57.464-33.238 60.78-105.636c9.04-15.865 29.333-21.287 45.106-12.25h0.2zM518.4 30c19.892 0 35.966 14.962 35.966 33.539v119.693h-71.931V63.439C482.434 44.963 498.508 30 518.4 30h-0.001z m-284.003 78.223c15.773-9.138 36.065-3.716 45.208 12.05 0 0 0 0.1 0.1 0.1l60.78 105.636-57.465 33.237-60.78-105.636c-9.14-15.866-3.716-36.15 12.156-45.387h0.001zM26.44 316.985c9.041-15.867 29.334-21.39 45.208-12.252 0 0 0.1 0 0.1 0.101l105.283 61.052-33.152 57.638L38.595 362.37c-15.872-9.137-21.298-29.522-12.155-45.387z m984.12 0c9.143 15.864 3.717 36.249-12.155 45.486L893.12 423.423l-33.152-57.637 105.283-61.053c15.773-9.137 36.065-3.715 45.208 12.05 0 0.1 0.1 0.1 0.1 0.2v0.002z"
										:fill="cardColor[3]"
										p-id="4026"
									></path>
								</svg>
								<div class="value">
									<span class="number" :style="{ color: cardColor[3] }" @click="onChangeModal(2, item.green)">{{ item.green?.length || 0 }}</span>
									<span class="unit">个</span>
								</div>
							</div>
							<div class="card-warn">
								<svg
									t="1697450179918"
									class="icon"
									viewBox="0 0 1024 1024"
									version="1.1"
									xmlns="http://www.w3.org/2000/svg"
									p-id="4025"
									width="40"
									height="40"
								>
									<path
										d="M175.726 934.787v-316.26c0-189.18 153.404-342.612 342.774-342.612s342.775 153.433 342.775 342.612v316.26h117.012c24.3 0 44 19.7 44 44s-19.7 44-44 44H44.747c-24.301 0-44-19.7-44-44s19.699-44 44-44h130.98z m367.588-520.804L374.237 692.332h135.221l-33.855 208.762L644.68 622.745H509.457l33.856-208.762h0.001z m259.29-305.76c15.875 9.237 21.299 29.622 12.156 45.488l-60.778 105.636-57.464-33.238 60.78-105.636c9.04-15.865 29.333-21.287 45.106-12.25h0.2zM518.4 30c19.892 0 35.966 14.962 35.966 33.539v119.693h-71.931V63.439C482.434 44.963 498.508 30 518.4 30h-0.001z m-284.003 78.223c15.773-9.138 36.065-3.716 45.208 12.05 0 0 0 0.1 0.1 0.1l60.78 105.636-57.465 33.237-60.78-105.636c-9.14-15.866-3.716-36.15 12.156-45.387h0.001zM26.44 316.985c9.041-15.867 29.334-21.39 45.208-12.252 0 0 0.1 0 0.1 0.101l105.283 61.052-33.152 57.638L38.595 362.37c-15.872-9.137-21.298-29.522-12.155-45.387z m984.12 0c9.143 15.864 3.717 36.249-12.155 45.486L893.12 423.423l-33.152-57.637 105.283-61.053c15.773-9.137 36.065-3.715 45.208 12.05 0 0.1 0.1 0.1 0.1 0.2v0.002z"
										:fill="cardColor[2]"
										p-id="4026"
									></path>
								</svg>
								<div class="value">
									<span class="number" :style="{ color: cardColor[2] }" @click="onChangeModal(2, item.yellow)">{{ item.yellow?.length || 0 }}</span>
									<span class="unit">个</span>
								</div>
							</div>
							<div class="card-warn">
								<svg
									t="1697450179918"
									class="icon"
									viewBox="0 0 1024 1024"
									version="1.1"
									xmlns="http://www.w3.org/2000/svg"
									p-id="4025"
									width="40"
									height="40"
								>
									<path
										d="M175.726 934.787v-316.26c0-189.18 153.404-342.612 342.774-342.612s342.775 153.433 342.775 342.612v316.26h117.012c24.3 0 44 19.7 44 44s-19.7 44-44 44H44.747c-24.301 0-44-19.7-44-44s19.699-44 44-44h130.98z m367.588-520.804L374.237 692.332h135.221l-33.855 208.762L644.68 622.745H509.457l33.856-208.762h0.001z m259.29-305.76c15.875 9.237 21.299 29.622 12.156 45.488l-60.778 105.636-57.464-33.238 60.78-105.636c9.04-15.865 29.333-21.287 45.106-12.25h0.2zM518.4 30c19.892 0 35.966 14.962 35.966 33.539v119.693h-71.931V63.439C482.434 44.963 498.508 30 518.4 30h-0.001z m-284.003 78.223c15.773-9.138 36.065-3.716 45.208 12.05 0 0 0 0.1 0.1 0.1l60.78 105.636-57.465 33.237-60.78-105.636c-9.14-15.866-3.716-36.15 12.156-45.387h0.001zM26.44 316.985c9.041-15.867 29.334-21.39 45.208-12.252 0 0 0.1 0 0.1 0.101l105.283 61.052-33.152 57.638L38.595 362.37c-15.872-9.137-21.298-29.522-12.155-45.387z m984.12 0c9.143 15.864 3.717 36.249-12.155 45.486L893.12 423.423l-33.152-57.637 105.283-61.053c15.773-9.137 36.065-3.715 45.208 12.05 0 0.1 0.1 0.1 0.1 0.2v0.002z"
										:fill="cardColor[1]"
										p-id="4026"
									></path>
								</svg>
								<div class="value">
									<span class="number" :style="{ color: cardColor[1] }" @click="onChangeModal(2, item.orange)">{{ item.orange?.length || 0 }}</span>
									<span class="unit">个</span>
								</div>
							</div>
							<div class="card-warn">
								<svg
									t="1697450179918"
									class="icon"
									viewBox="0 0 1024 1024"
									version="1.1"
									xmlns="http://www.w3.org/2000/svg"
									p-id="4025"
									width="40"
									height="40"
								>
									<path
										d="M175.726 934.787v-316.26c0-189.18 153.404-342.612 342.774-342.612s342.775 153.433 342.775 342.612v316.26h117.012c24.3 0 44 19.7 44 44s-19.7 44-44 44H44.747c-24.301 0-44-19.7-44-44s19.699-44 44-44h130.98z m367.588-520.804L374.237 692.332h135.221l-33.855 208.762L644.68 622.745H509.457l33.856-208.762h0.001z m259.29-305.76c15.875 9.237 21.299 29.622 12.156 45.488l-60.778 105.636-57.464-33.238 60.78-105.636c9.04-15.865 29.333-21.287 45.106-12.25h0.2zM518.4 30c19.892 0 35.966 14.962 35.966 33.539v119.693h-71.931V63.439C482.434 44.963 498.508 30 518.4 30h-0.001z m-284.003 78.223c15.773-9.138 36.065-3.716 45.208 12.05 0 0 0 0.1 0.1 0.1l60.78 105.636-57.465 33.237-60.78-105.636c-9.14-15.866-3.716-36.15 12.156-45.387h0.001zM26.44 316.985c9.041-15.867 29.334-21.39 45.208-12.252 0 0 0.1 0 0.1 0.101l105.283 61.052-33.152 57.638L38.595 362.37c-15.872-9.137-21.298-29.522-12.155-45.387z m984.12 0c9.143 15.864 3.717 36.249-12.155 45.486L893.12 423.423l-33.152-57.637 105.283-61.053c15.773-9.137 36.065-3.715 45.208 12.05 0 0.1 0.1 0.1 0.1 0.2v0.002z"
										:fill="cardColor[0]"
										p-id="4026"
									></path>
								</svg>
								<div class="value">
									<span class="number" :style="{ color: cardColor[0] }" @click="onChangeModal(2, item.red)">{{ item.red?.length || 0 }}</span>
									<span class="unit">个</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="flex gap">
				<div class="item-wrap" style="overflow: hidden">
					<div class="title">班子结构不达标的乡镇</div>
					<div class="content">
						<v-chart :option="option2" class="chart1" autoresize ref="townRef" />
					</div>
				</div>
				<div class="item-wrap" style="overflow: hidden">
					<div class="title">班子结构不达标的部门</div>
					<v-chart :option="option3" class="chart1" autoresize ref="PartyRef" />
				</div>
			</div>
			<div class="item-wrap">
				<div class="title">班子结构不达标项分析</div>
				<div class="content" @touchend.prevent.stop="() => {}">
					<bar-charts :data="teamWarinData" @onChangeModal="onChangeModal" />
				</div>
			</div>
		</div>
		<div class="organizedGroup-item">
			<div class="search-wrap flex">
				<span class="item-title">干部队伍分析</span>
				<span class="search">
					<a-select
						v-model:value="scope"
						style="width: 130px; margin-right: 12px"
						@change="getTotalCadresFn"
						:options="[
							{ label: '全部', value: 1 },
							{ label: '部门', value: 2 },
							{ label: '乡镇', value: 3 },
						]"
					/>
					<a-select
						v-model:value="job_scope"
						style="width: 130px"
						@change="getTotalCadresFn"
						:options="[
							{ label: '全部', value: 1 },
							{ label: '正职', value: 2 },
							{ label: '副职', value: 3 },
						]"
					></a-select>
				</span>
			</div>
			<div class="total-wrap">
				<div class="total" v-for="item in totalCadresData.total_cadres_item_volist" :key="item.name" @click="onNameClick(item)">
					<span>{{ item.name }}</span>
					<span class="number bakhum">{{ item.value }}</span>
					<span>人</span>
				</div>
			</div>
			<div class="flex gap">
				<div class="item-wrap no-margin">
					<div class="title">性别结构</div>
					<div class="content flex align-center">
						<b-progress :data="totalCadresData.gender" @name-click="onNameClick" />
					</div>
				</div>
				<div class="item-wrap no-margin">
					<div class="title">民族结构</div>
					<div class="content">
						<b-progress :data="totalCadresData.ethnic" :vertical="false" @name-click="onNameClick" />
					</div>
				</div>
			</div>
			<div class="flex gap align-normal">
				<div class="flex-item flex-direction" style="overflow: hidden">
					<div class="item-wrap" style="width: 100%">
						<div class="title">政治面貌</div>
						<div class="content">
							<b-progress :data="totalCadresData.political_status" :vertical="false" @name-click="onNameClick" />
						</div>
					</div>
					<div class="item-wrap flex-item" style="width: 100%">
						<div class="title">年龄结构</div>
						<div class="content">
							<v-chart :option="ageOption" class="chart2" autoresize ref="ageRef" />
						</div>
					</div>
				</div>
				<div class="item-wrap flex-item" style="overflow: hidden">
					<div class="title">学历结构（全日制）</div>
					<div class="content education">
						<b-progress :data="totalCadresData.education" @name-click="onNameClick" />
						<v-chart :option="educationOption" class="chart3" autoresize ref="educationRef" />
					</div>
				</div>
			</div>
			<div class="item-wrap">
				<div class="title">干部职数</div>
				<div class="content flex">
					<v-chart :option="option4" class="chart4" autoresize />
					<v-chart :option="option5" class="chart4" autoresize />
					<v-chart :option="option6" class="chart4" autoresize />
				</div>
			</div>
		</div>
		<a-modal
			v-model:visible="visible"
			@cancel="onClose(2)"
			destroyOnClose
			title="预警详情"
			class="orgGroupModal"
			width="80%"
			style="top: 5%"
			:footer="false"
		>
			<orgGroupModal :type="teamType" :data="modalParams" :org_id="orgId" />
		</a-modal>
		<a-modal v-model:visible="visible1" title="人员详情" class="orgGroupModal" width="80%" style="top: 5%" :footer="false" destroyOnClose>
			<a-table
				:columns="columns"
				row-key="user_id"
				:row-class-name="(_record: any, index : any) => (index % 2 === 1 ? 'table-striped' : null)"
				:dataSource="dataSource"
				:pagination="false"
				:scroll="{ y: '70vh' }"
				:loading="loading.user"
				@cancel="onClose(1)"
			>
				<template #bodyCell="{ column, record, index }">
					<template v-if="column.key === 'rank'">
						{{ index + 1 }}
					</template>
					<template v-if="column.key === 'head_url'">
						<CodeAvatar :user_id="record.user_id" :head_url="record.head_url">
							<template #avatar="{ avatar }">
								<img v-lazy="avatar" alt="" class="avatar" />
							</template>
						</CodeAvatar>
					</template>
					<template v-if="column.key === 'user_name'">
						<a @click="onLocation(record)">{{ record.user_name }}</a>
					</template>
					<template v-if="column.key === 'current_job_time'">
						<span>{{ convertDay(record.current_job_time) }}</span>
					</template>
				</template>
			</a-table>
		</a-modal>
		<!-- <div class="openAimodal" @click="openAiModal">
			<div>一键生成班子分析报告</div>
		</div>
		<a-drawer title="" placement="right" :closable="false" :visible="visibleAi" @close="onCloseAi" :destroyOnClose='true'>
			<drawer-ai :parameterID="orgId" type='org' />
		</a-drawer> -->
	</div>
</template>
<script lang="ts" setup>
import { getCadresjob, getListByScope, getTeamStatistics, getTeamTownshipSequence, getTeamWaring, getTotalCadres } from '@/apis/statistics'
import defaultAvatar from '@/assets/images/avatar.png'
import CodeAvatar from '@/components/CodeAvatar.vue'
import { CDN_URL } from '@/config/env'
import router from '@/router'
import { convertPxToRem } from '@/utils/utils'
import dayJs from 'dayjs'
import { computed, onMounted, reactive, ref, shallowRef, watchEffect } from 'vue'
import DrawerAi from '@/components/DrawerAi.vue'
import { strcut, strcutTown, structure, townStructure } from '@/types/structure'
import bProgress from './comp/bProgress.vue'
import orgGroupModal from './comp/orgGroupModal.vue'
const props = defineProps<{
	orgId: any
}>()
//#region ====================== 定义变量 ======================
const org_id = ref<any>(undefined)
const departmentalData = ref<any>([])
const teamWarinData = ref<any>([])
const totalCadresData = ref<any>({})
const scope = ref<any>(1)
const job_scope = ref<any>(1)
const option1 = ref<any>({})
const option2 = ref<any>({})
const option3 = ref<any>({})
const ageOption = ref<any>({})
const educationOption = ref<any>({})
const option4 = ref<any>({})
const option5 = ref<any>({})
const option6 = ref<any>({})
const visible = ref<any>(false)
const visible1 = ref<any>(false)
const modalParams = ref<any>({})
const teamType = ref<any>(null)

const townRef = ref<any>()
const PartyRef = ref<any>()
const ageRef = ref<any>()
const educationRef = ref<any>()
	const visibleAi = ref<boolean>(false);
const loading = reactive({
	user: false,
	team: false,
})
const dataSource = shallowRef<any>()

const columns = [
	{
		key: 'rank',
		dataIndex: 'rank',
		align: 'center',
		width: '6%',
		title: '序号',
	},
	{
		key: 'head_url',
		dataIndex: 'head_url',
		align: 'center',
		width: '7%',
		title: '头像',
	},
	{
		key: 'user_name',
		dataIndex: 'user_name',
		align: 'center',
		width: '7%',
		title: '姓名',
		// colClass: blur.value ? 'filter-style' : '',
		colClass: 'cursor-pointer',
		colClick: (_data: any, event: any) => {
			event.stopPropagation()
		},
	},
	{
		key: 'position',
		dataIndex: 'position',
		align: 'left',
		width: '20%',
		title: '现任职务',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'birthday',
		dataIndex: 'birthday',
		align: 'center',
		width: '10%',
		title: '出生年月（年龄）',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'current_job_time',
		dataIndex: 'current_job_time',
		align: 'center',
		width: '10%',
		title: '任现职务时间',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'diploma',
		dataIndex: 'diploma',
		align: 'center',
		width: '10%',
		title: '全日制学历',
		// colClass: blur.value ? 'filter-style' : '',
	},
	{
		key: 'major',
		dataIndex: 'major',
		align: 'left',
		width: '10%',
		title: '毕业院校及专业',
		// colClass: blur.value ? 'filter-style' : '',
	},
	// {
	// 	key: 'specialty',
	// 	align: 'center',
	// 	width: '12%',
	// 	title: '专业',
	// 	// colClass: blur.value ? 'filter-style' : '',
	// },
	{
		key: 'cadre_index',
		dataIndex: 'cadre_index',
		align: 'center',
		width: '6%',
		title: '干部指数',
	},
	{
		key: 'cadre_index_rank',
		dataIndex: 'cadre_index_rank',
		align: 'center',
		width: '10%',
		title: '指数同序列排名',
	},
]
// 定义变量
const activeTab = ref('1') // 当前激活的标签页
const baseData = computed(() => {
	const data = {
		title: '',
		townNum: 0,
		party: 0,
		townDescriptionsList: [] as any[],
		DescriptionsList: [] as any[],
		townStrcut: [] as any[],
		strcut: [] as any[],
	}
	// 班子生态
	// data.title = '班子结构模型'
	data.townDescriptionsList = strcutTown
	data.DescriptionsList = strcut
	data.strcut = structure
	data.townStrcut = townStructure
	return data
})
const type = ref(0)
const getAvatar = (head_url?: string) => {
	return head_url ? `${CDN_URL}/fr_img/${head_url}` : defaultAvatar
}
const cardColor = ['#ff473e', '#ffa300', '#f6dd00', '#60ca71']
const pieColor = ['#1992FF', '#2ECF61', '#FFCF15', '#FF4B67', '#14B3D9', '#FF8943', '#9656F1', '#3EF589']
const pieTitle: any = {
	x: '49%',
	y: '40%',
	textAlign: 'center',
	textStyle: {
		rich: {
			a: {
				fontSize: convertPxToRem(30),
				color: '#000',
				fontWeight: '600',
				textAlign: 'center',
				fontFamily: 'iconfont',
			},
			b: {
				fontSize: convertPxToRem(12),
				color: '#000',
				textAlign: 'center',
			},
		},
	},
	subtextStyle: {
		fontSize: convertPxToRem(18),
		fontWeight: '500',
		color: '#5c5a68',
		textAlign: 'center',
	},
}
const pieSeries: any = {
	type: 'pie',
	radius: ['0%', '80%'],
	center: ['30%', '50%'],
	itemStyle: {
		borderWidth: 2,
		borderColor: '#fff',
	},
	label: {
		show: false,
		formatter: '{c}个',
		fontSize: convertPxToRem(16),
		fontWeight: 500,
		color: '#000',
	},
	labelLine: {
		show: true,
	},
	data: [],
}
const pieLegend: any = {
	right: '5%',
	y: 'center',
	data: [],
	itemWidth: 9,
	itemHeight: 9,
	orient: 'vertical',
	itemGap: convertPxToRem(18),
	textStyle: {
		color: '#000',
		fontSize: convertPxToRem(18),
		lineHeight: convertPxToRem(18),
	},
}
const total = ref(0)
//#endregion
//#region ====================== 获取数据源 ======================

const initData = async () => {
	// 班子结构预警
	getTeamStatistics({ org_id: props.orgId }).then(({ code, data }) => {
		if (code == 0 && data) {
			departmentalData.value = data.departmental_warning
			const totalParty = data.departmental_warning.reduce((pre: number, current: any) => pre + current.number, 0)
			const title = { ...pieTitle, x: '50%' }
			total.value = data.total
			const series = { ...pieSeries, label: { ...pieSeries, show: true }, center: ['50%', '50%'] }
			title.text = `{a|${totalParty}} {b|个}`
			title.subtext = '班子总数'
			series.radius = ['40%', '70%']
			series.itemStyle = {
				borderWidth: 2,
				borderColor: '#fff',
			}
			series.label.formatter = '{b} {c}个'
			// series.label.fontSize = convertPxToRem(20)
			series.data = data.team_warning.map(({ name, number }: any) => ({ name, value: number }))
			option1.value = {
				title,
				color: ['#FF0000', '#FFA300', '#F6DD00', '#60CA71'],
				series,
			}
		}
	})
	getTeamTownshipSequenceFn(1)
	getTeamTownshipSequenceFn(2)
	// 班子结构不达标项分析
	getTeamWaring({ org_id: props.orgId }).then(({ code, data }) => {
		if (code == 0 && data) {
			teamWarinData.value = data
		}
	})
	getTotalCadresFn()
	getCadresjobData()
}

const getTeamTownshipSequenceFn = (type: number) => {
	// 1 班子结构不达标乡镇  2 部门
	getTeamTownshipSequence({ org_id: props.orgId, type }).then(({ code, data }: any) => {
		if (code == 0 && data) {
			const legendData: string[] = []
			const tmpData = data
				.filter((item: any) => item.sequence_name)
				.map(({ sequence_name, warning_volist, org_ids }: any) => {
					legendData.push(sequence_name)
					return { name: sequence_name, value: warning_volist, org_ids }
				})

			pieSeries.data = tmpData

			const series = { ...pieSeries }
			const legend = {
				...pieLegend,
				formatter: (name: string) => {
					const _ = tmpData.find((item: any) => item.name === name)

					return `{a|${_?.name}} {b|${_?.value}个}`
				},
				textStyle: {
					...pieLegend.textStyle,
					padding: [3, 0, 0, 0],
					rich: {
						a: {
							width: convertPxToRem(240),
						},
						b: {
							width: convertPxToRem(60),
							align: 'right',
						},
					},
				},
			}
			legend.data = legendData
			series.label.formatter = '{c}个'
			series.itemStyle = {
				borderWidth: 0,
				borderColor: '#fff',
			}
			if (type === 1) {
				const selected: any = {}

				legendData.map((item: any) => {
					selected[item] = true
				})
				option2.value = {
					tmpData,
					legend: {
						...legend,
						selectedMode: true,
						selected,
					},
					color: pieColor,
					series,
				}
			} else {
				option3.value = {
					tmpData,
					legend,
					color: pieColor,
					series,
				}
			}
		}
	})
}
const getTotalCadresFn = () => {
	// 干部队伍分析
	getTotalCadres({ org_id: props.orgId, scope: scope.value, job_scope: job_scope.value }).then(({ code, data }) => {
		if (code == 0 && data) {
			const { age, education_v2, ...other } = data
			totalCadresData.value = other
			if (age) {
				const legendData: string[] = []
				const series = { ...JSON.parse(JSON.stringify(pieSeries)), radius: ['60%', '90%'], center: ['25%', '50%'] }
				series.data = age.map((item: any) => {
					legendData.push(item.name)
					return item
				})
				series.label.show = false
				ageOption.value = {
					tmpData: series.data,
					legend: {
						data: legendData,
						orient: 'vertical',
						top: 'center',
						right: '20%',
						itemWidth: 9,
						itemHeight: 9,
						textStyle: {
							fontSize: convertPxToRem(18),
							fontWeight: 500,
							color: '#000',
							rich: {
								a: {
									width: convertPxToRem(100),
								},
								b: {
									width: convertPxToRem(100),
									align: 'right',
								},
								c: {
									width: convertPxToRem(100),
									align: 'right',
								},
							},
						},
						formatter: function (name: string) {
							const item = age.filter((item: any) => item.name === name)[0]
							const total = age.reduce((pre: number, current: number) => pre + current, 0)
							return `{a|${name}}{b|${item.value}人}{c|(${item.proportion}%)}`
						},
					},
					color: pieColor,
					series,
				}
			}
			if (education_v2) {
				const legendData: string[] = []
				const tmpData = education_v2.map((item: any) => {
					legendData.push(item.name)
					return item
				})

				pieSeries.data = tmpData

				const series = { ...JSON.parse(JSON.stringify(pieSeries)), radius: ['40%', '65%'] }
				const legend = {
					...pieLegend,
					formatter: (name: string) => {
						const _ = tmpData.find((item: any) => item.name === name)

						return `{a|${_?.name}} {b|${_?.value}个} {c|(${_?.proportion}%)}`
					},
					textStyle: {
						...pieLegend.textStyle,
						padding: [3, 0, 0, 0],
						rich: {
							a: {
								width: convertPxToRem(120),
							},
							b: {
								width: convertPxToRem(100),
								align: 'right',
							},
							c: {
								width: convertPxToRem(100),
								align: 'right',
							},
						},
					},
				}
				legend.data = legendData
				series.label.formatter = '{c}人 ({d}%)'
				educationOption.value = {
					tmpData,
					legend,
					color: pieColor,
					series,
				}
			}
		}
	})
	getCadresjobData()
}

const getCadresjobData = () => {
	// 干部职数
	getCadresjob({ org_id: props.orgId, job_scope: job_scope.value, scope: scope.value }).then(({ code, data }) => {
		if (code == 0 && data) {
			const { main_job, deputy_job, cadre_analysis } = data

			const title = {
				x: '49%',
				y: '40%',
				textAlign: 'center',
				textStyle: {
					rich: {
						a: {
							fontSize: convertPxToRem(30),
							color: '#000',
							fontWeight: '600',
							textAlign: 'center',
							fontFamily: 'ArTarumianBakhum-Regular, ArTarumianBakhum',
						},
						b: {
							fontSize: convertPxToRem(12),
							color: '#000',
							textAlign: 'center',
						},
					},
				},
				subtextStyle: {
					fontSize: convertPxToRem(18),
					fontWeight: '500',
					color: '#5c5a68',
					textAlign: 'center',
				},
			}

			const [main_total, main_in, main_vacancy] = main_job
			option4.value = {
				title: {
					...title,
					x: '38%',
					text: `{a|${main_total.value}}名`,
					subtext: '正职总数',
				},
				legend: {
					...pieLegend,
					data: ['在岗', '空缺'],
					formatter: (name: any) => {
						const _ = main_job.find((item: any) => item.name === name)
						return `{a|${_.name}} {b|${_.value}名}`
					},
					textStyle: {
						...pieLegend.textStyle,
						padding: [3, 0, 0, 0],
						rich: {
							a: {
								width: convertPxToRem(70),
							},
							b: {
								width: convertPxToRem(40),
								align: 'right',
							},
						},
					},
				},
				color: pieColor,
				series: {
					type: 'pie',
					radius: ['55%', '80%'],
					center: ['40%', '50%'],
					itemStyle: {
						borderWidth: 2,
						borderColor: '#fff',
					},
					label: {
						show: false,
						formatter: '{b} {c}个',
						fontSize: convertPxToRem(16),
						fontWeight: 500,
						color: '#000',
					},
					labelLine: {
						show: true,
					},
					data: [main_in, main_vacancy],
				},
			}
			const [deputy_total, deputy_in, deputy_vacancy] = deputy_job

			option5.value = {
				title: {
					...title,
					x: '40%',
					text: `{a|${deputy_total.value}}名`,
					subtext: '副职总数',
				},
				legend: {
					...pieLegend,
					data: ['在岗', '空缺'],
					formatter: (name: string) => {
						const _ = deputy_job.find((item: any) => item.name === name)
						return `{a|${_.name}} {b|${_.value}名}`
					},
					textStyle: {
						...pieLegend.textStyle,
						padding: [3, 0, 0, 0],
						rich: {
							a: {
								width: convertPxToRem(70),
							},
							b: {
								width: convertPxToRem(40),
								align: 'right',
							},
						},
					},
				},
				color: pieColor,
				series: {
					type: 'pie',
					radius: ['55%', '80%'],
					center: ['40%', '50%'],
					itemStyle: {
						borderWidth: 2,
						borderColor: '#fff',
					},
					label: {
						show: false,
						formatter: '{b} {c}个',
						fontSize: convertPxToRem(16),
						fontWeight: 500,
						color: '#000',
					},
					labelLine: {
						show: true,
					},
					data: [deputy_in, deputy_vacancy],
				},
			}
			const [cadre_total, cadre_in, cadre_vacancy] = cadre_analysis
			option6.value = {
				legend: {
					...pieLegend,
					data: undefined,
					formatter: (name: string) => {
						const _ = cadre_analysis.find((item: any) => item.name === name)
						return `{a|${_.name}} {b|${_.value}个班子}`
					},
					textStyle: {
						...pieLegend.textStyle,
						padding: [3, 0, 0, 0],
						rich: {
							a: {
								width: convertPxToRem(120),
							},
							b: {
								width: convertPxToRem(60),
								align: 'right',
							},
						},
					},
				},
				color: pieColor,
				series: {
					...pieSeries,
					label: {
						show: false,
						formatter: '{b}',
						fontSize: convertPxToRem(16),
						fontWeight: 500,
						color: '#000',
					},
					data: cadre_analysis,
				},
			}
		}
	})
}

const onChangeModal = (type: number, ids: number[] | number, name?: string) => {
	visible.value = true
	modalParams.value = { ids, name }
	teamType.value = type
}

const onGetListByScope = async (params: any) => {
	loading.user = true
	const res = await getListByScope(params)
	if (res.code === 0) {
		dataSource.value = res.data
	}

	loading.user = false
}

const cardTitle = (type: number) => ({ 1: '乡镇街道', 2: '一级部门', 3: '二级部门' }[type])

const registryPieClick = (ref: any) => {
	const pieRef = ref

	if (pieRef.value && !pieRef.value.once) {
		const chart = pieRef.value.chart
		pieRef.value.once = true

		chart.on('legendselectchanged', (params: any) => {
			const option = chart.getOption()

			const selected = option.legend[0].selected

			Reflect.ownKeys(selected).forEach((key) => {
				selected[key] = true
			})

			option['animation'] = false

			chart.setOption(option)

			const tmpData = option.tmpData

			const org = tmpData.find((item: any) => {
				return item.name == params.name
			})

			setTimeout(() => {
				onChangeModal(2, org.org_ids)
			}, 50)
		})
	}
}
const registryPieClick1 = (ref: any) => {
	const pieRef = ref

	if (pieRef.value && !pieRef.value.once) {
		const chart = pieRef.value.chart
		pieRef.value.once = true

		chart.on('legendselectchanged', (params: any) => {
			const option = chart.getOption()

			const selected = option.legend[0].selected

			Reflect.ownKeys(selected).forEach((key) => {
				selected[key] = true
			})

			option['animation'] = false

			chart.setOption(option)

			const tmpData = option.tmpData

			const org = tmpData.find((item: any) => {
				return item.name == params.name
			})
			setTimeout(() => {
				// scope	number	是	机构 1全部2部门3乡镇
				// job_scope	number	是	职级 1全部(正负职不包含中层干部)2正职3副职
				// gender	number	否	性别 1男2女
				// ethnic	number	否	民族 1汉2其他
				// age	number	否	性别 1.35以下 2.40以下 3.45以下 4.50以下 5.55以下 6.55以上
				// diploma	String	否	学历
				onNameClick(org)
			}, 50)
		})
	}
}

const onClose = (type) => {
	type === 1 && (dataSource.value = [])
}

const onNameClick = (record: any) => {
	const paramsMap: any = {
		男: {
			gender: 1,
		},
		女: {
			gender: 2,
		},
		汉族: {
			ethnic: 1,
		},
		少数民族: {
			ethnic: 2,
		},
		'35岁以下': {
			age: 1,
		},
		'35-40岁': {
			age: 2,
		},
		'40-45岁': {
			age: 3,
		},
		'45-50岁': {
			age: 4,
		},
		'50-55岁': { age: 5 },
		'55岁以上': {
			age: 6,
		},
		大学及以上: {
			diploma: 1,
		},
		大学以下: {
			diploma: 2,
		},
		研究生及以上: {
			diploma: 3,
		},
		大学: {
			diploma: 4,
		},
		大专: {
			diploma: 5,
		},
		中专及以下: {
			diploma: 6,
		},
		中共党员: {
			political: 1,
		},
		非中共党员: {
			political: 2,
		},
		市管领导: {
			position: 1,
		},
		县管正职: {
			position: 2,
		},
		县管副职: {
			position: 3,
		},
		干部总数: {
			position: 4,
		},
	}

	const params = paramsMap[record.name] || {}

	visible1.value = true

	dataSource.value = []

	onGetListByScope({
		...params,
		org_id: props.orgId,
		scope: scope.value,
		job_scope: job_scope.value,
	})
}
//#endregion
//#region ====================== 生命周期 ======================
const openAiModal=()=>{
	visibleAi.value = true;
}
const onCloseAi=()=>{
	visibleAi.value = false;
}
onMounted(() => {
	registryPieClick(townRef)
	registryPieClick(PartyRef)
	registryPieClick1(educationRef)
	registryPieClick1(ageRef)
})

watchEffect(() => {
	if (org_id.value !== props.orgId) {
		org_id.value = props.orgId
		initData()
	}
})
//#endregion
//#region ====================== 跳转页面和获取时间 ======================

const onLocation = (data: any) => {
	if (!data.user_id) return

	router.push({
		path: `/cadre-portrait/home`,
		query: {
			user_id: data.user_id,
		},
	})

	visible1.value = false
}

const convertDay = (time: string) => {
	return time ? dayJs(time).format('YYYY.MM') : '-'
}
const handleTabChange = (key: string) => {
	activeTab.value = key
}
//#endregion
</script>

<style lang="scss" scoped>
.organizedGroup {
	width: calc(100vw - 40px) !important;
	background-color: #fff;
	.flex {
		display: flex;
		width: 100%;
		> * {
			flex: 1;
		}
	}
	.align-center {
		align-items: center;
	}
	.gap {
		gap: 32px;
		.flex-item {
			flex: 1 1 50%;
		}
	}
	.flex-direction {
		display: flex;
		flex-direction: column;
	}
	&-item {
		padding: 24px 22px;
		background-color: #fff;
		&:first-child {
			margin-bottom: 20px;
		}
		.total-wrap {
			@extend .flex;
			padding: 35px 0;
			margin: 36px 0;
			background: linear-gradient(277deg, #ecf7ff 0%, #fafdff 100%);
			box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
			border-radius: 8px;
			.total {
				text-align: center;
				font-size: 20px;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: 500;
				color: #333333;
				border-right: 1px solid #e5e5e5;
				&:last-child {
					border: none;
				}
				.number {
					margin: 0 5px 0 10px;
					font-size: 48px;
					font-family: ArTarumianBakhum-Regular, ArTarumianBakhum;
					font-weight: 400;
					color: #008eff;
					vertical-align: sub;
				}
			}
		}
		.item-title {
			position: relative;
			padding-left: 16px;
			font-size: 20px;
			font-family: PingFang SC-Heavy, PingFang SC;
			font-weight: 800;
			color: #000000;
			&::before,
			&::after {
				content: '';
				position: absolute;
				left: 0;
				top: 5px;
				width: 8px;
				height: 22px;
				background: linear-gradient(#86c6ff, #40a3fe);
			}
			&::after {
				top: auto;
				bottom: 4px;
				left: 2px;
				width: 0;
				height: 0;
				border: 5px solid;
				border-color: transparent #fff #fff transparent;
			}
		}
		.search-wrap {
			.item-title {
				&::before {
					top: 3px;
				}
				&::after {
					bottom: 0;
				}
			}
			.search {
				display: flex;
				justify-content: flex-end;
				::v-deep .ant-select-selector {
					border-radius: 4px;
				}
			}
		}
		.warn-total {
			margin: 0px 45px 0px 0px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			flex: 0 0 203px;
			height: 281px;
			background: linear-gradient(180deg, #ffffff 0%, #fff8f8 100%);
			box-shadow: 0px 5 6px 0px rgba(0, 0, 0, 0.1);
			border-radius: 6px 6px 6px 6px;
			border-bottom: 3px solid rgba(255, 0, 0, 0.67);
			box-shadow: 0px 5 6px 0px rgba(0, 0, 0, 0.1);
			.label {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 26px;
				color: rgba(0, 0, 0, 0.9);
				line-height: 30px;
			}
			.number {
				margin-top: 20px;
				font-family: ArTarumianBakhum, ArTarumianBakhum;
				font-weight: 400;
				font-size: 45px;
				color: #ff0000;
				line-height: 53px;
			}
		}
		.card-wrap {
			height: 100%;
			display: flex;
			flex-direction: column;
			gap: 15px 0px;
			.item-card {
				width: 100%;
				display: flex;
				padding: 23px 30px;
				background: linear-gradient(306deg, #f5fbff 0%, #ffffff 100%);
				box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.1);
				border-radius: 6px 6px 6px 6px;
				.card-label {
					margin-right: 20px;
					display: flex;
					align-items: flex-end;

					.label {
						margin-right: 26px;
						font-size: 26px;
						color: rgba(0, 0, 0, 0.9);
						line-height: 25px;
					}
					.value {
						display: flex;
						.number {
							margin-right: 3px;
							width: 40px;
							text-align: right;
							font-weight: 400;
							font-size: 30px;
							color: #008eff;
							line-height: 25px;
							font-family: ArTarumianBakhum, ArTarumianBakhum;
						}
						.unit {
							font-size: 25px;
							line-height: 24px;
							color: #008eff;
						}
					}
				}
				.card-warn {
					flex: 1;
					display: flex;
					align-items: flex-end;
					justify-content: center;
					svg {
						width: 39px !important;
						height: 39px !important;
					}
					.value {
						display: flex;
						align-items: flex-end;
						.number {
							margin-right: 3px;
							margin-left: 13px;
							font-weight: 400;
							font-size: 30px;
							line-height: 22px;
							font-family: ArTarumianBakhum, ArTarumianBakhum;
							width: 40px;
							text-align: right;
						}
						.unit {
							font-size: 23px;
							line-height: 23px;
							color: #000000;
						}
					}
				}
			}
			.card-content {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 30px 47px 20px;
				flex: 0 1 calc(50% - 48px);
				background: linear-gradient(305deg, #ffe2e2 0%, #ffffff 100%);
				border-radius: 8px;
				box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
				border-bottom: 8px solid #ff473e;
				font-size: 14px;
				font-family: Source Han Sans CN-Regular, Source Han Sans CN;
				font-weight: 400;
				color: #333333;
				> div {
					cursor: pointer;
				}
				.icon {
					transform: translateY(-8px);
				}
				.num {
					margin-right: 5px;
					font-size: 40px;
					font-family: ArTarumianBakhum-Regular, ArTarumianBakhum;
					font-weight: 400;
					color: #ff0000;
					vertical-align: sub;
				}
				&:nth-child(2) {
					background: linear-gradient(305deg, #fef0da 0%, #ffffff 100%);
					border-color: #ffa300;
					.num {
						color: #ffa300;
					}
				}
				&:nth-child(3) {
					background: linear-gradient(305deg, #ffffdd 0%, #ffffff 100%);
					border-color: #f6dd00;
					.num {
						color: #f6dd00;
					}
				}
				&:nth-child(4) {
					background: linear-gradient(305deg, #dfffe0 0%, #ffffff 100%);
					border-color: #60ca71;
					.num {
						color: #60ca71;
					}
				}
			}
		}
		.item-wrap {
			margin-top: 32px;
			padding: 20px 24px 24px;
			background-color: #f6f8fc;
			border-radius: 8px;
			.set-just {
				justify-content: space-between;
			}
			.title {
				font-size: 18px;
				font-family: Source Han Sans CN-Bold, Source Han Sans CN;
				font-weight: bold;
				color: #333333;
				&::before {
					content: '';
					display: inline-block;
					margin-right: 15px;
					width: 12px;
					height: 12px;
					background-color: #008eff;
					border-radius: 50%;
				}
			}
			.content {
				height: 100%;
				.team-rule {
					padding: 24px 0px 30px;
					width: 100%;
					display: flex;
					justify-content: space-between;
					height: 100%;

					.tabs-container {
						height: 678px; /* 设置整体高度 */
						display: flex;
						flex-direction: column;

						.ant-tabs-content-holder {
							flex: 1;
							min-height: 0; /* 重要：防止内容溢出 */
						}

						// 修改标签页标题的文字大小
						:deep(.ant-tabs-tab) {
							font-size: 18px; // 设置标签标题的文字大小
						}
						:deep(.ant-descriptions-item-label) {
							font-size: 18px;
						}
						.tab-content {
							font-size: 18px;
							transition: height 0.3s ease;
							min-height: 500px; /* 设置最小高度 */
							overflow-y: auto; /* 内容超出时可以滚动 */
						}
						.value-box {
							font-size: 18px;
						}
						/* 滚动条样式 */
						::-webkit-scrollbar {
							width: 6px;
						}
						::-webkit-scrollbar-thumb {
							background: #d9d9d9;
							border-radius: 3px;
						}
					}
				}
			}
			.education {
				display: flex;
				flex-direction: column;
				justify-content: space-around;
				height: 100%;
				.progress {
					margin: 30px;
				}
				.echarts {
					flex: 1;
				}
			}
		}
		.chart1 {
			height: 360px;
			width: 100%;
		}
		.chart2 {
			height: 224px;
			width: 100%;
		}

		.chart3 {
			height: 150px;
			width: 100%;
		}
		.chart4 {
			height: 250px;
			width: 100%;
		}
		.no-margin {
			margin: 0;
		}
	}
}

.avatar {
	width: 53px;
	height: 66px;
	background: #c4c4c4;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	object-fit: contain;
}
.structure-box {
	margin-left: -30px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	height: 46px;
	.structure-item {
		display: flex;
		align-items: flex-end;
		.img {
			margin-right: 4px;
			width: 24px;
			height: 24px;
			img {
				width: 100%;
				height: 100%;
			}
		}
		.structure-label {
			font-size: 18px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: rgba(0, 0, 0, 0.9);
			line-height: 14px;
		}
	}
}
</style>
<style lang="less">
.table-striped td {
	background-color: #fafafa;
}
.orgGroupModal {
	.ant-modal-header {
		.ant-modal-title {
			font-size: 24px;
		}
	}
	.ant-modal-body {
		.modal-title {
			font-size: 20px;
		}
		.ant-table-wrapper {
			th {
				font-size: 20px;
			}
			td {
				font-size: 20px;
			}
			.ant-empty-description {
				font-size: 20px;
			}
		}
	}
}
.openAimodal {
	position: fixed;
	top: 300px;
	right: 0;
	padding: 20px 16px;
	background: #13AF0B;
	box-shadow: 0px 0px 26px 0px rgba(3, 69, 0, 0.28);
	font-weight: 500;
	font-size: 23px;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8px 8px 8px 8px;
}
</style>
