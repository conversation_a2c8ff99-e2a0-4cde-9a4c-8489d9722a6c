<template>
	<div class="rule-modal-content">
		<div class="title">
			{{ baseData.title }}
		</div>
		<div class="team-rule" v-if="type !== 4">
			<div class="left-box">
				<div class="group-name">
					乡镇1<span v-if="baseData.townNum" class="sub-name">（{{ baseData.townNum }}）</span>
				</div>
				<a-descriptions bordered :column="1">
					<a-descriptions-item :label="item.label" v-for="(item, index) in baseData.townDescriptionsList" :key="index">
						<div class="value-box">
							<template v-if="item.children">
								<div class="value-item" v-for="(value, index) in item.children" :key="index">
									<span class="text">
										{{ value }}
									</span>
								</div>
							</template>
							<template v-else>
								<div class="value-item">
									<span class="text">{{ item.value }}</span>
								</div>
							</template>
						</div>
					</a-descriptions-item>
					<a-descriptions-item v-if="type === 0">
						<div class="structure-box">
							<div class="structure-item" v-for="(item, index) in baseData.townStrcut" :key="index">
								<img :src="item.icon" alt="" class="img" />
								<div class="structure-label">{{ item.label }}</div>
							</div>
						</div>
					</a-descriptions-item>
				</a-descriptions>
			</div>
			<div class="right-box">
				<div class="group-name">
					部门<span v-if="baseData.party" class="sub-name">（{{ baseData.party }}）</span>
				</div>
				<a-descriptions bordered :column="1">
					<a-descriptions-item :label="item.label" v-for="(item, index) in baseData.DescriptionsList" :key="index">
						<div class="value-box">
							<template v-if="item.children">
								<div class="value-item" v-for="(value, index) in item.children" :key="index">
									<span class="text">
										{{ value }}
									</span>
								</div>
							</template>
							<template v-else>
								<div class="value-item">
									<span class="text">{{ item.value }}</span>
								</div>
							</template>
						</div>
					</a-descriptions-item>
					<a-descriptions-item class="self-row" v-if="type === 0">
						<div class="structure-box">
							<div class="structure-item" v-for="(item, index) in baseData.strcut" :key="index">
								<img :src="item.icon" alt="" class="img" />
								<div class="structure-label">{{ item.label }}</div>
							</div>
						</div>
					</a-descriptions-item>
				</a-descriptions>
			</div>
		</div>
		<div class="team-rule" v-else>
			<a-table :columns="columns" bordered :data-source="tableDataSource" style="width: 100%" :pagination="false" />
		</div>
	</div>
</template>

<script lang="ts" setup>
import { strcut, strcutTown, structure, townStructure } from '@/types/structure'
import { computed } from 'vue'

const props = defineProps({
	visible: {
		type: Boolean,
		default: false,
	},
	type: {
		type: Number,
	},
	data: {
		type: Object,
	},
})

// 班子生态 部门
const townEcology = [
	{
		label: '全面从严治党',
		value: '权重：15%',
	},
	{
		label: '班子生态测评',
		value: '权重：10%',
	},
	{
		label: '一报告两评议',
		value: '权重：10%',
	},
	{
		label: '巡察审计评级',
		value: '巡察审计评为A等次加0.3，评为C、D等次的分别扣0.3、1分',
	},
	{
		label: '个人事项报告',
		value: '抽查、核查发现不实的，根据严重程度扣0.3-2分',
	},
	{
		label: '清正廉洁',
		value: '班子成员年度内违纪违法受到处理的，根据情节严重程度每件次扣0.5-3分',
	},
]
// 乡镇
const town = [
	{
		label: '全面从严治党',
		value: '权重：20%',
	},
	{
		label: '班子生态测评',
		value: '权重：10%',
	},
	{
		label: '一报告两评议',
		value: '权重：5%',
	},
	{
		label: '巡察审计评级',
		value: '巡察审计评为A等次加0.3，评为C、D等次的分别扣0.3、1分',
	},
	{
		label: '个人事项报告',
		value: '抽查、核查发现不实的，根据严重程度扣0.3-2分',
	},
	{
		label: '清正廉洁',
		value: '班子成员年度内违纪违法受到处理的，根据情节严重程度每件次扣0.5-3分',
	},
]
// 业绩
const TownPerformance = [
	{
		label: '经济发展实绩',
		value: '权重：40%',
	},
	{
		label: '一把手例会',
		value: '权重：20%',
	},
	{
		label: '民意调查',
		value: '权重：5%',
	},
	{
		label: '班子排名升降',
		value: '根据班子年度考核序列排名升降加扣0.1分/名次',
	},
]
const performance = [
	{
		label: '经济发展实绩',
		value: '权重：65%',
	},
	{
		label: '民意调查',
		value: '运用社情民意满意度结果，涉及部门加扣分',
	},
	{
		label: '班子排名升降',
		value: '根据班子年度考核序列排名升降加扣0.1分/名次',
	},
]

const tableDataSource = [
	{
		row_id: 1,
		col_cell: 2,
		title: '班子生态（20）',
		index: '班子团结度',
		score: 10,
		content: '根据年度民主测评“班子团结度”结果折算计分',
	},
	{
		title: '班子生态',
		index: '班子廉洁度',
		score: 10,
		content: '根据年度民主测评“党风廉政建设”结果折算计分，班子成员违纪违法受到处理的，根据情节严重程度每件次扣0.2-1分',
	},
	{
		row_id: 2,
		col_cell: 2,
		title: '班子业绩（60）',
		index: '年度考核',
		score: 30,
		content: '根据年度党建得分和经济实绩得分确定评估分值',
	},
	{
		title: '班子业绩',
		index: '动态比试',
		score: 30,
		content: '根据年度核心业务经济发展重点工作确定评估分值',
	},
	{
		row_id: 3,
		col_cell: 2,
		title: '班子口碑（20）',
		index: '组织认可度',
		score: 10,
		content: '根据年度民主测评综合得分折算计分',
	},
	{
		title: '班子口碑',
		index: '群众满意度',
		score: 10,
		content: '运用社情民意满意度结果折算计分',
	},
]

const columns = [
	{
		title: '评估进度',
		key: 'title',
		dataIndex: 'title',
		// 合并
		customCell: (row: any) => ({
			rowSpan: row.col_cell || 0,
		}),
		align: 'center',
		width: '20%',
	},
	{
		title: '评估指标',
		key: 'index',
		dataIndex: 'index',
		// 合并
		align: 'center',
		width: '25%',
		className: 'align-left',
	},
	{
		title: '分值权重',
		key: 'score',
		dataIndex: 'score',
		// 合并
		align: 'center',
		width: '25%',
	},
	{
		title: '评估内容',
		key: 'content',
		dataIndex: 'content',
		// 合并
		align: 'center',
		width: '30%',
		className: 'align-left',
	},
]

const baseData = computed(() => {
	const { type } = props
	const data = {
		title: '',
		townNum: 0,
		party: 0,
		townDescriptionsList: [] as any[],
		DescriptionsList: [] as any[],
		townStrcut: [] as any[],
		strcut: [] as any[],
	}
	// 班子生态
	if (type === 0) {
		data.title = '班子结构说明'
		data.townDescriptionsList = strcutTown
		data.DescriptionsList = strcut
		data.strcut = structure
		data.townStrcut = townStructure
	} else if (type === 1) {
		data.title = '班子生态说明'
		data.townDescriptionsList = town
		data.DescriptionsList = townEcology
		data.townNum = 35
		data.party = 35
	} else if (type === 2) {
		data.title = '班子业绩说明'
		data.townDescriptionsList = TownPerformance
		data.DescriptionsList = performance
		data.townNum = 65
		data.party = 65
	}
	return data
})
</script>

<style lang="less" scoped>
.rule-modal-content {
	width: 100%;
	.title {
		padding: 10px 0px 30px;
		font-size: 16px;
		font-family: Source Han Sans CN-Bold, Source Han Sans CN;
		font-weight: bold;
		color: rgba(0, 0, 0, 0.9);
		line-height: 22px;
		border-bottom: 1px solid #f0f0f0;
	}
}
.team-rule {
	padding: 24px 0px 30px;
	width: 100%;
	display: flex;
	justify-content: space-between;
	.left-box {
		width: 49%;
	}
	.right-box {
		width: 49%;
		display: flex;
		flex-direction: column;
		::v-deep .self-row {
			height: 46px;
		}
		::v-deep .ant-descriptions {
			flex: 1;
			.ant-descriptions-view {
				height: 100%;
				table {
					height: 100%;
				}
			}
		}
	}
	.left-box,
	.right-box {
		.group-name {
			padding: 14px 0;
			width: 100%;
			text-align: center;
			background: #daf4dd;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			border: 1px solid #e9e9e9;

			text-align: center;
			font-size: 24px;
			font-family: PingFang SC-Heavy, PingFang SC;
			font-weight: 800;
			color: #333333;
			.sub-name {
				font-size: 20px;
			}
		}
		.text {
			font-size: 14px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: rgba(0, 0, 0, 0.9);
			line-height: 18px;
		}
		.value-box {
			.value-item {
				padding: 14px 31px;
				&:not(:last-child) {
					border-bottom: 1px solid #e9e9e9;
				}
			}
		}
		.structure-box {
			margin-left: -30px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 46px;
			.structure-item {
				display: flex;
				align-items: flex-end;
				.img {
					margin-right: 4px;
					width: 24px;
					height: 24px;
					img {
						width: 100%;
						height: 100%;
					}
				}
				.structure-label {
					font-size: 14px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: rgba(0, 0, 0, 0.9);
					line-height: 14px;
				}
			}
		}
	}
}
:deep(.ant-descriptions-item-content) {
	padding: 0px;
}
:deep(.ant-table-tbody) {
	.align-left {
		text-align: left !important;
	}
}
</style>
