<script lang="ts" setup>
import { ref } from 'vue'
import { queryTeam, getTeamTermDetail } from '@/apis/statistics'
import type { TableColumnType } from 'ant-design-vue'
import { watchEffect } from 'vue'
const props = defineProps({
	type: {
		type: Number,
	},
	data: {
		type: Object,
	},
})
const dataSource = ref<any>([])
const columns = ref<TableColumnType[]>([])
const loading = ref<any>(false)

watchEffect(() => {
	const { ids, name } = props.data || {}
	let resApi = queryTeam
	let params: any = { org_ids: ids }
	columns.value = [
		{
			title: '组织名称',
			dataIndex: 'org_name',
			width: 200,
			customCell: ({ oneRow }: any) => {
				return { rowSpan: oneRow || 0 }
			},
		},
		{
			title: '预警项',
			dataIndex: 'warning_item_name',
			colSpan: 2,
			width: 100,
			customCell: ({ twoRow }: any) => {
				return { rowSpan: twoRow }
			},
		},
		{
			title: '预警项1',
			colSpan: 0,
			dataIndex: 'alert_item_name',
		},
		{
			title: '预警详情',
			dataIndex: 'warning_details',
		},
	]
	loading.value = true
	console.log('request', params)
	resApi(params).then((res: any) => {
		loading.value = false
		if (res.code === 0) {
			const { data } = res
			if (!name) {
				const _data: any = []
				data.forEach(({ org_name, warning_items_vo }: any) => {
					if (!warning_items_vo) {
						// 如果 warning_items_vo 为 null 或 undefined，只添加 org_name
						_data.push({
							org_name,
							warning_item_name: '',
							alert_item_name: '',
							warning_details: '',
							oneRow: 1, // 占用一行
							twoRow: 0, // 不需要额外的行
						})
					} else {
						const arr: any = []
						warning_items_vo.forEach(({ warning_item_name, warning_items_key_vo }: any, key: number) => {
							warning_items_key_vo.forEach(({ alert_item_name, warning_details }: any, index: number) => {
								arr.push({
									twoRow: index === 0 ? warning_items_key_vo.length : 0, // 第一个元素记录总行数
									org_name,
									warning_item_name,
									alert_item_name,
									warning_details,
								})
							})
						})
						arr[0].oneRow = arr.length // 第一个元素记录总行数
						_data.push(...arr)
					}
				})
				dataSource.value = _data
			} else {
				dataSource.value = data
			}
		}
	})
})
</script>

<template>
	<div class="modal-title" v-show="data?.name">{{ data?.name }}结构不达标的班子如下：</div>
	<a-table bordered :columns="columns" :data-source="dataSource" :loading="loading" :scroll="{ y: 500 }" :pagination="false" />
</template>

<style lang="scss" scoped>
.modal-title {
	margin-bottom: 16px;
	font-size: 16px;
	font-family: Source Han Sans CN-Medium, Source Han Sans CN;
	font-weight: 500;
	color: #008eff;
}

.ant-table-striped :deep(.table-striped) td {
	background-color: #fafafa;
}
.ant-table-striped :deep(.table-striped) td {
	background-color: rgb(29, 29, 29);
}
</style>
