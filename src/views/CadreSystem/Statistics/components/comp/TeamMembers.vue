<template>
	<div class="team-member" title="班子成员树型图" header>
		<div class="content-box">
			<div class="left-tree tree-box">
				<div class="title">干部指数</div>
				<div class="tree-list">
					<div class="tree-item" v-for="(item, index) in leaderIndex" :key="index">
						<div class="username" @click="onLocation(item)">{{ item.user_name }}</div>
						<div class="score">{{ item.rindex }}</div>
						<div :class="`bar-length ${item.active ? 'active-bar' : ''}`" :style="item.style"></div>
					</div>
				</div>
			</div>
			<div class="right-tree tree-box">
				<div class="title">风险指数</div>
				<div class="tree-list">
					<div class="tree-item" v-for="(item, index) in riskIndex" :key="index">
						<div class="username" @click="onLocation(item)">{{ item.user_name }}</div>
						<div class="score">{{ item.rindex }}</div>
						<div :class="`bar-length ${item.active ? 'active-bar' : ''}`" :style="item.style"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, watch, toRefs } from 'vue'
import { getOrgTreediagram } from '@/apis/cadre-portrait/home'
import useUser from '@/store/user'

import { Base64 } from '@/utils/utils'
import { historyPush } from '@/utils/history'
import { isGeneratorFunction } from 'util/types'

export default defineComponent({
	props: {
		mode: {
			type: String,
			default: '1',
		},
		org_id: Number,
	},
	setup(props) {
		const org_id = props.org_id

		const user = useUser()
		const treeData = reactive<any>({ leader_index: [], risk_index: [] })
		let _LeaderIndex: any = [
			{
				user_id: 3,
				user_name: '鲁迅',
				rindex: 22.46,
			},
			{
				user_id: 3,
				user_name: '鲁迅',
				rindex: 21.46,
			},
			{
				user_id: 3,
				user_name: '鲁迅',
				rindex: 20.46,
			},
			{
				user_id: 3,
				user_name: '鲁迅',
				rindex: 19.46,
			},
			{
				user_id: 3,
				user_name: '鲁迅',
				rindex: 15.46,
			},
			{
				user_id: 3,
				user_name: '鲁迅',
				rindex: 5,
			},
			{
				user_id: 3,
				user_name: '鲁迅1',
				rindex: 0,
			},
			{
				user_id: 3,
				user_name: '鲁迅2',
				rindex: 0,
			},
			{
				user_id: 3,
				user_name: '鲁迅3',
				rindex: 0,
			},
		]
		const basicWidth = (466 / 1920) * 100

		const leaderIndex = computed(() => {
			const percent = (100 / treeData.leader_index.length) * 0.01
			const { name } = user.detail as any

			return treeData.leader_index.map((item: any, index: number) => {
				let width = basicWidth * (1 - percent * index) + 'vw'
				let active = name === item.user_name
				return {
					...item,
					active,
					style: {
						width,
					},
				}
			})
		})

		const riskIndex = computed(() => {
			const { name } = user.detail as any
			return treeData.risk_index.map((item: any, index: number) => {
				const percent = item.rindex / 100
				console.log('🚀 ~ returntreeData.risk_index.map ~ percent:', percent)
				let width: any = basicWidth * percent

				let active = name === item.user_name

				const minWidth = (document.body.clientWidth * width) / 100 < 10
				return {
					...item,
					active,
					style: {
						width: item.rindex === 0 ? '10px' : minWidth ? 10 + item.rindex + 'px' : width + 'vw',
						backgroundColor: true ? riskColor(item.rindex) : '',
					},
				}
			})
		})

		// const leaderIndex = computed(() => {
		// 	const { name } = user.detail as any
		// 	let first = 0
		// 	return treeData.leader_index.map((item: any, index: number) => {
		// 		const { rindex, user_name } = item
		// 		// 根据第一条数据
		// 		index === 0 && (first = rindex)
		// 		// 值是否为0
		// 		const rindexIsZero = rindex === 0
		// 		// 为0时展示不同的样式
		// 		const width = rindexIsZero ? '10px' : (rindex / first) * basicWidth + 'vw'
		// 		// 是否是当前用户
		// 		const active = name === user_name

		// 		const borderRadius = rindexIsZero ? '10px' : undefined

		// 		return {
		// 			...item,
		// 			active,
		// 			style: {
		// 				width,
		// 				borderRadius,
		// 			},
		// 		}
		// 	})
		// })
		// const riskIndex = computed(() => {
		// 	const { name } = user.detail as any
		// 	let first = 0
		// 	return treeData.risk_index.map((item: any, index: number) => {
		// 		const { rindex, user_name } = item
		// 		// 根据第一条数据
		// 		index === 0 && (first = rindex)
		// 		// 值是否为0
		// 		const rindexIsZero = rindex === 0
		// 		// 为0时展示不同的样式
		// 		const width = rindexIsZero ? '10px' : (rindex / first) * basicWidth + 'vw'
		// 		// 是否是当前用户
		// 		const active = name === user_name
		// 		const color = active ? riskColor(rindex) : '#FFEBEA'

		// 		const borderRadius = rindexIsZero ? '10px' : undefined

		// 		return {
		// 			...item,
		// 			width,
		// 			active,
		// 			style: {
		// 				width,
		// 				backgroundColor: color,
		// 				borderRadius,
		// 			},
		// 		}
		// 	})
		// })
		const riskColor = (riskIndex: any) => {
			if (riskIndex >= 0 && riskIndex < 10) {
				return '#60CA71'
			} else if (riskIndex >= 10 && riskIndex < 40) {
				return '#F6DD00'
			} else if (riskIndex >= 40 && riskIndex < 70) {
				return '#FF9900'
			} else {
				return '#FF0000'
			}
		}

		const onLocation = (data: any) => {
			const userInfo = JSON.parse(sessionStorage.getItem('userInfo') || '{}')
			userInfo.user_id = data.user_id
			const user_info = Base64.encode(JSON.stringify(userInfo))
			// window.open(`/cadre-portrait/home?_h=${encodeURIComponent(user_info)}`)
			historyPush(`/cadre-portrait/home?user_id=${data.user_id}&_h=${encodeURIComponent(user_info)}`)
		}

		const loadData = async () => {
			if (!org_id) return

			const res = await getOrgTreediagram({
				org_id,
				flag: props.mode,
			} as any)

			const { code, data } = res
			if (code === 0) {
				const { leader_index, risk_index } = data
				treeData.leader_index = leader_index
				treeData.risk_index = risk_index
			}
		}
		// 请求数据
		loadData()

		watch(toRefs(props).mode, (newV) => {
			loadData()
		})
		return {
			riskIndex,
			leaderIndex,
			treeData,
			onLocation,
		}
	},
})
</script>

<style scoped lang="less">
.team-member {
	border-radius: 8px;
	margin-top: 16px;
	padding-bottom: 37px !important;
	width: 100%;
	height: unset;
	min-height: 311px;
	background: #ffffff;
	opacity: 1;
	.content-box {
		width: 100%;
		// height: 100%;
		// overflow-y: auto;
		display: flex;
		.left-tree {
			border-right: 1px solid #eeeeee;
			.title {
				padding-right: 11px;
				text-align: right;
			}
			.tree-list {
				padding-right: 11px;
				.tree-item {
					display: flex;
					justify-content: flex-end;
					.active-bar {
						background-color: #60ca71 !important;
					}
				}
			}
		}
		.right-tree {
			border-left: 1px solid #eeeeee;
			.title {
				padding-left: 11px;
				text-align: left;
			}
			.tree-list {
				padding-left: 11px;
				.tree-item {
					display: flex;
					flex-direction: row-reverse;
					justify-content: flex-end;
				}
			}
		}
		.tree-box {
			flex: 1;
			.title {
				padding-top: 16px;
				padding-bottom: 16px;
				font-size: 22px;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #000000;
				line-height: 24px;
				border-bottom: 1px solid #eeeeee;
			}
			.tree-list {
				width: 100%;
				// height: 100%;
				.tree-item {
					margin-top: 20px;
					display: flex;
					align-items: center;
					white-space: nowrap;

					&:nth-child(1) {
						margin-top: 28px;
					}
					.username {
						font-size: 18px;
						font-family: PingFang SC-Regular, PingFang SC;
						font-weight: 400;
						color: #000000;
						line-height: 24px;
						cursor: pointer;
					}
					.score {
						margin: 0 16px;
						font-size: 18px;
						font-family: PingFang SC-Regular, PingFang SC;
						font-weight: 400;
						color: #000000;
						line-height: 24px;
					}
					.bar-length {
						width: 466px;
						height: 20px;
						// background: #ffebea;
						border-radius: 19px 19px 19px 19px;
						opacity: 1;
						background-color: #60ca71;
					}
					.zero-bar {
						border-radius: 5px;
					}
				}
			}
		}
	}
}
</style>
