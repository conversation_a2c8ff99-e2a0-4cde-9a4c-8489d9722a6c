<template>
	<div class="town-ships">
		<div class="town-header">
			<!-- <span class="town-icon"></span> -->
			<span class="town-title">{{ props.orgName }}</span>
		</div>
		<div class="content">
			<div class="data-card—top">
				<div class="card-title">班子职数</div>
				<div class="card-content">
					<div class="total-data">
						<span class="total-data-title">总职数：</span>
						<span class="total-data-num">{{ teambaseinfo.total_number }}</span>
					</div>

					<!-- 正职 -->
					<div class="position-data">
						<span class="position-title">正职：</span>
						<span class="position-label">已配</span>
						<span class="position-num">{{ teambaseinfo.positive_assigned_number }}</span>
						<span class="position-label">/应配{{ teambaseinfo.positive_number }}</span>
					</div>
					<div class="position-data">
						<span class="position-title">副职：</span>
						<span class="position-label">已配</span>
						<span class="position-num">{{ teambaseinfo.deputy_assigned_number }}</span>
						<span class="position-label">/应配{{ teambaseinfo.deputy_number }}</span>
					</div>
				</div>
			</div>
			<div class="data-card-bottom">
				<div :class="`card-item structure structure-${Index}`">
					<div class="card-title">
						<div class="title-1">班子结构</div>

						<div class="right" @click.stop="changePage(1)">班子结构分析></div>
					</div>
					<div class="card-content m-top-35">
						<div :class="`left left-${Index} cursor-pointer`" :style="{ color: colorList[Index] }">
							<div class="text-group">
								<span class="icon" :style="{ backgroundImage: `url(${imgList[Index]})` }"></span>
								<span class="label">未达标项数：</span>
							</div>
							<span class="number">{{ teambaseinfo.warning_not_pass_number }}/{{ teambaseinfo.warning_number }}</span>
						</div>
					</div>
				</div>
				<div class="card-item ecology">
					<div class="card-title" @click="onRule(4)">班子运行指数</div>
					<div class="card-content">
						<div class="number">
							<span>{{ teambaseinfo.team_index?.score || 0 }}</span>
						</div>
						<div class="number_card margin-left-24">
							<div type="label">同序列</div>
							<div type="number">{{ teambaseinfo.team_index?.rank }}</div>
						</div>
						<div class="number_card margin-left-16">
							<div type="label">乡镇/部门</div>
							<div type="number">{{ teambaseinfo.team_index?.townRank }}</div>
						</div>
					</div>
				</div>
				<div class="card-item equipment">
					<div class="card-title">
						<div>班子配备指数</div>
						<div class="eq-button" @click="onDetail">详情></div>
					</div>
					<div class="card-content">
						<div class="number">
							<span>{{ teambaseinfo.cadre_index?.score || 0 }}</span>
						</div>
						<div class="number_card margin-left-24">
							<div type="label">同序列</div>
							<div type="number">{{ teambaseinfo.cadre_index?.rank }}</div>
						</div>
						<div class="number_card margin-left-16">
							<div type="label">乡镇/部门</div>
							<div type="number">{{ teambaseinfo.cadre_index?.townRank }}</div>
						</div>
					</div>
				</div>
				<div class="card-item xuncha">
					<div class="card-title">
						<div>班子巡察指数</div>
						<div class="eq-button" @click="onDetailOrg">详情></div>
					</div>
					<div class="card-content">
						<div class="number">
							<span>{{ teambaseinfo.patrol_index?.score }}</span>
						</div>
						<div class="number_card margin-left-24">
							<div type="label">同序列</div>
							<div type="number">{{ teambaseinfo.patrol_index?.rank || '-' }}</div>
						</div>
					</div>
				</div>
				<!-- <div class="card-item ecology">
					<div class="card-title" @click="onRule(1)">班子生态</div>
					<div class="card-content">
						<div class="number">
							<span>{{ teambaseinfo.team_ecology?.score || 0 }}</span>
						</div>
						<div class="number_card margin-left-24">
							<div type="label">同序列排名:</div>
							<div type="number">{{ teambaseinfo.team_ecology?.rank }}</div>
						</div>
						<div class="number_card margin-left-16">
							<div type="label">乡镇/部门排名:</div>
							<div type="number">{{ teambaseinfo.team_ecology?.townRank }}</div>
						</div>
					</div>
				</div>
				<div class="card-item performance">
					<div class="card-title" @click="onRule(2)">班子业绩</div>
					<div class="card-content">
						<div class="number">
							<span>{{ teambaseinfo?.team_performance?.score || 0 }}</span>
						</div>
						<div class="number_card margin-left-24">
							<div type="label">同序列排名:</div>
							<div type="number">{{ teambaseinfo.team_performance?.rank }}</div>
						</div>
						<div class="number_card margin-left-16">
							<div type="label">乡镇/部门排名:</div>
							<div type="number">{{ teambaseinfo.team_performance?.townRank }}</div>
						</div>
					</div>
				</div> -->
			</div>
			<div class="card-row margin-top-40">
				<pyramid :pyramid-data="teamPyramid" />
				<point :point-data="teamScatter" @select-change="onPointSelectChange" :select-value="selectValue" />
			</div>
			<team-index :team-data="teamIndex" :onOrgSelect="onOrgSelect" />
			<run-index :line-data="teamIndexLine.lineData" :table-data="teamIndexLine.tableData" :columns="teamIndexLine.columns" />
			<!-- <min-card title="巡查审计评级" header>
				<table-data :columns="columns1" :data-source="teamPatrol" />
			</min-card>
			<min-card title="个人事项报告" header>
				<table-data :columns="columns2" :data-source="teamPersonal" />
			</min-card>
			<min-card title="清正廉洁" header>
				<table-data :columns="columns3" :data-source="teamHonest" />
			</min-card>
			<min-card title="班子年度考核" header>
				<table-data :columns="columns4" :data-source="teamAnnual">
					<template #rank="{ data }">
						<span
							>{{ data.rank }} <i class="up-icon"></i> <span class="up-text">{{ data.rank_up }}</span></span
						>
					</template>
</table-data>
</min-card> -->
		</div>
		<a-modal v-model:visible="visible" class="town-ship-modal" width="" :footer="null">
			<team-rule :type="ruleType" />
		</a-modal>
		<a-modal v-model:visible="structureVisible" width="" class="structure-modal" :footer="null" destroyOnClose>
			<!-- 班子结构表 -->
			<structure-table :data="structureTable" :close="onClose" />
		</a-modal>
		<a-modal v-model:visible="equipmentVisible" width="" class="equipment-modal" :footer="null" destroyOnClose>
			<!-- 班子结构表 -->
			<Equipment :org_id="orgId" />
		</a-modal>

		<div class="openAimodal" @click="openAiModal">
			<div>一键生成班子分析报告</div>
		</div>
		<a-drawer
			title=""
			placement="right"
			:closable="false"
			:visible="visibleAi"
			@close="onCloseAi"
			:destroyOnClose="true"
			style="z-index: 9999 !important"
		>
			<drawer-ai :parameterID="orgId" type="org" />
		</a-drawer>
	</div>
</template>

<script lang="ts" setup>
import {
	getTeamAnnual,
	getTeamBasicInfo,
	getTeamHonest,
	getTeamIndex,
	getTeamIndexLine,
	getTeamPatrol,
	getTeamPersonal,
	getTeamPyramid,
	getTeamScatter,
	getTeamStructure,
} from '@/apis/statistics'
import structure0 from '@/assets/images/structure/structure-0.png'
import structure1 from '@/assets/images/structure/structure-1.png'
import structure2 from '@/assets/images/structure/structure-2.png'
import structure3 from '@/assets/images/structure/structure-3.png'
import DrawerAi from '@/components/DrawerAi.vue'
import StructureTable from '@/components/StructureTable.vue'
import { useCadreSystem } from '@/store/cadreSystem'
import { message } from 'ant-design-vue'
import { computed, provide, reactive, ref, toRefs, watch } from 'vue'
import { useRouter } from 'vue-router'
import Equipment from './comp/Equipment.vue'
import Point from './comp/Point.vue'
import Pyramid from './comp/Pyramid.vue'
import RunIndex from './comp/RunIndex.vue'
import TeamIndex from './comp/TeamIndex.vue'
import TeamRule from './comp/TeamRule.vue'

const router = useRouter()

import { useRoute } from 'vue-router'
const route = useRoute()
const props = defineProps({
	orgId: {
		type: Number,
		required: false,
		default: undefined,
	},
	orgName: {
		type: String,
		required: true,
	},
	changePage: {
		type: Function,
		required: true,
	},
	onOrgSelect: {
		type: Function,
		required: true,
	},
})
const state = useCadreSystem()

const visible = ref(false)
const selectValue = ref('0')
const structureVisible = ref(false)
const equipmentVisible = ref(false)
const visibleAi = ref<boolean>(false)
const pageData = reactive({
	teambaseinfo: {
		total_number: 0,
		positive_number: 0,
		positive_assigned_number: 0,
		deputy_number: 0,
		deputy_assigned_number: 0,
		warning_number: 0,
		warning_not_pass_number: 0,
		team_ecology: {
			score: 0,
			rank: '',
			townRank: '',
		},
		team_performance: {
			score: 0,
			rank: '',
			townRank: '',
		},
		team_index: {
			score: 0,
			rank: '',
			townRank: '',
		},
		patrol_index: {
			score: 0,
			rank: '',
			townRank: '',
		},
	},
	teamPyramid: {
		org_id: 0,
		name: '',
		pyramid_index: '0',
		reindex: '0',
	},
	teamScatter: {
		others: [],
		mine: {},
	},
	teamIndex: [],
	teamIndexLine: {
		lineData: {},
		tableData: [],
		columns: [],
	},
	teamPatrol: [],
	teamPersonal: [],
	teamHonest: [],
	teamAnnual: [],
	structureTable: [], //班子结构分析表
})

const columns1 = [
	{
		name: '巡查审计评级',
		title: '巡查审计评级',
		dataIndex: 'patrol_audit_grade',
		key: 'patrol_audit_grade',
		align: 'center',
		width: '50%',
	},
	{
		title: '加扣分',
		dataIndex: 'patrol_audit_score',
		key: 'patrol_audit_score',
		align: 'center',
		width: '50%',
	},
]

const columns2 = [
	{
		title: '事项描述',
		dataIndex: 'description',
		key: 'description',
		align: 'center',
		width: '50%',
	},
	{
		title: '扣分',
		dataIndex: 'score',
		key: 'score',
		align: 'center',
		width: '50%',
	},
]

const columns3 = [
	{
		title: '班子成员',
		dataIndex: 'user_name',
		key: 'user_name',
		align: 'center',
		width: '25%',
	},
	{
		title: '处理类别',
		dataIndex: 'type',
		key: 'type',
		align: 'center',
		width: '25%',
	},
	{
		title: '处理时间',
		dataIndex: 'date',
		key: 'date',
		align: 'center',
		width: '25%',
	},
	{
		title: '扣分',
		dataIndex: 'score',
		key: 'score',
		align: 'center',
		width: '25%',
	},
]

const columns4 = [
	{
		title: '年度考核排名',
		dataIndex: 'rank',
		key: 'rank',
		align: 'center',
		width: '50%',
	},
	{
		title: '加扣分',
		dataIndex: 'score',
		key: 'score',
		align: 'center',
		width: '50%',
	},
]

provide('org_id', props.orgId)

const onDetail = () => {
	equipmentVisible.value = !equipmentVisible.value
}

const onDetailOrg = () => {
	router.push({
		path: '/team-inspection-index',
		query: {
			org_id: props.orgId,
			org_name: props.orgName,
		},
	})
}

const onPointSelectChange = (value: any) => {
	selectValue.value = value
	initTeamScatter(orgId.value)
}

const createData = (origin_data: [], title: string) => {
	const data: any = {}
	data.name = title
	for (let i = 0; i < origin_data.length; i++) {
		data[`name${i + 1}`] = origin_data[i]
	}

	return data
}

const initBaseInfo = async (org_id: number) => {
	const res = await getTeamBasicInfo({ org_id })
	if (res.code === 0) {
		pageData.teambaseinfo = res.data
	} else {
		message.error(res.message)
	}
}

const initTeamPyramid = async (org_id: any) => {
	const res = await getTeamPyramid({ org_id, flag: state.coor_menu_selected })
	if (res.code === 0) {
		pageData.teamPyramid = res.data
	} else {
		message.error(res.message)
	}
}

const initTeamScatter = async (org_id: any) => {
	const res = await getTeamScatter({ org_id, flag: state.coor_menu_selected, type: selectValue.value })
	if (res.code === 0) {
		pageData.teamScatter = res.data
	} else {
		message.error(res.message)
	}
}

const initTeamIndex = async (org_id: any) => {
	const res = await getTeamIndex({ org_id, flag: state.coor_menu_selected })
	if (res.code === 0) {
		pageData.teamIndex = res.data
	} else {
		message.error(res.message)
	}
}

const initTeamIndexLine = async (org_id: number) => {
	const res = await getTeamIndexLine({ org_id })
	if (res.code === 0) {
		const { data } = res
		let { org_data = {}, sequence_avg = {}, department_avg = {} } = data

		const _org_data = [
			org_data.unity,
			org_data.punishment,
			org_data.annual,
			org_data.dynamic_competition,
			/**org_data.construct,
			org_data.economic_development,
			org_data.safety,
			org_data.livelihood_service,
			*/
			org_data.evaluation,
			org_data.will,
		]
		const _sequence_avg = [
			sequence_avg.unity,
			sequence_avg.punishment,
			sequence_avg.annual,
			sequence_avg.dynamic_competition,
			/**sequence_avg.construct,
			sequence_avg.economic_development,
			sequence_avg.safety,
			sequence_avg.livelihood_service,
			*/
			sequence_avg.evaluation,
			sequence_avg.will,
		]
		const _department_avg = [
			department_avg.unity,
			department_avg.punishment,
			department_avg.annual,
			department_avg.dynamic_competition,
			/**department_avg.construct,
			department_avg.economic_development,
			department_avg.safety,
			department_avg.livelihood_service,
			*/
			department_avg.evaluation,
			department_avg.will,
		]
		const columns: any = [
			{
				key: 'title',
				title: '',
				align: 'center',
				dataIndex: 'title',
				width: '10%',
			},
			{
				key: 'unity',
				title: '班子团结度',
				align: 'center',
				dataIndex: 'unity',
				width: '10%',
			},
			{
				key: 'punishment',
				title: '班子廉洁度',
				align: 'center',
				dataIndex: 'punishment',
				width: '10%',
			},
			{
				key: 'annual',
				title: '年度考核',
				align: 'center',
				dataIndex: 'annual',
				width: '10%',
			},
			{
				key: 'dynamic_competition',
				title: '动态比试',
				align: 'center',
				dataIndex: 'dynamic_competition',
				width: '10%',
			},
			// {
			// 	key: 'construct',
			// 	title: '党的建设',
			// 	align: 'center',
			// 	dataIndex: 'name',
			// 	width: '10%',
			// },
			// {
			// 	key: 'economic_development',
			// 	title: '经济发展',
			// 	align: 'center',
			// 	dataIndex: 'name',
			// 	width: '10%',
			// },
			// {
			// 	key: 'safety',
			// 	title: '平安法治',
			// 	align: 'center',
			// 	dataIndex: 'name',
			// 	width: '10%',
			// },
			// {
			// 	key: 'livelihood_service',
			// 	title: '民生服务',
			// 	align: 'center',
			// 	dataIndex: 'name',
			// 	width: '10%',
			// },
			{
				key: 'evaluation',
				title: '组织认可度',
				align: 'center',
				dataIndex: 'name',
				width: '10%',
			},
			{
				key: 'will',
				title: '群众满意度',
				align: 'center',
				dataIndex: 'name',
				width: '10%',
			},
		]
		org_data['title'] = '本单位'
		sequence_avg['title'] = '序列平均值'
		department_avg['title'] = '乡镇(部门)平均值'

		const xlabel = [
			'班子团结度',
			'班子廉洁度',
			'年度考核',
			'动态比试',
			/** '党的建设', '经济发展', '平安法治', '民生服务',  */ '组织认可度',
			'群众满意度',
		]

		// const tableData: any = [createData(org_data, '本单位'), createData(sequence_avg, '序列平均值'), createData(department_avg, '乡镇(部门)平均值')]
		pageData.teamIndexLine = {
			tableData: [org_data, sequence_avg, department_avg] as any,
			columns,
			lineData: {
				xlabel: xlabel,
				org_data: _org_data,
				sequence_avg: _sequence_avg,
				department_avg: _department_avg,
			},
		}
	} else {
		message.error(res.message)
	}
}

const initTeamPatrol = async (org_id: number) => {
	const res = await getTeamPatrol({ org_id })
	if (res.code === 0) {
		pageData.teamPatrol = res.data
	} else {
		message.error(res.message)
	}
}

const initTeamPersonal = async (org_id: number) => {
	const res = await getTeamPersonal({ org_id })
	if (res.code === 0) {
		pageData.teamPersonal = res.data
	} else {
		message.error(res.message)
	}
}

const initTeamHonest = async (org_id: number) => {
	const res = await getTeamHonest({ org_id })
	if (res.code === 0) {
		pageData.teamHonest = res.data
	} else {
		message.error(res.message)
	}
}

const initTeamAnnual = async (org_id: number) => {
	const res = await getTeamAnnual({ org_id })
	if (res.code === 0) {
		pageData.teamAnnual = [res.data]
	} else {
		message.error(res.message)
	}
}
// 班子结构table
const getStructureTable = async (org_id: number) => {
	const res = await getTeamStructure({ org_id })
	const allocationInfo = {
		name: '配备情况',
		age: res.data?.age_case,
		gender: res.data?.gender_case,
		educational: res.data?.educational_case,
		major: res.data?.major_case,
		experience: res.data?.experience_case,
		cadre_index: res.data?.cadre_index_case,
		withdraw: res.data?.withdraw_case,
	}
	const standardsInfo = {
		name: '是否达标',
		age: res.data?.age_standards,
		gender: res.data?.gender_standards ? '是' : '否',
		educational: res.data?.educational_standards,
		major: res.data?.major_standards,
		experience: res.data?.experience_standards,
		cadre_index: res.data?.cadre_index_standards,
		withdraw: res.data?.withdraw_standards,
	}
	res.data?.users?.unshift(standardsInfo, allocationInfo)
	if (res.code === 0) {
		pageData.structureTable = res.data?.users
	} else {
		message.error(res.message)
	}
}

const onStructure = () => {
	structureVisible.value = true
}
// 0结构 1 生态 2 业绩
const ruleType = ref<number>(1)

const onRule = (type: number) => {
	visible.value = true
	ruleType.value = type
}

const onClose = () => {
	structureVisible.value = false
}

const openAiModal = () => {
	visibleAi.value = true
}
const onCloseAi = () => {
	visibleAi.value = false
}
const { orgId } = toRefs(props)

const currentOrgId = computed(() => {
	// 优先使用 props 传入的值
	if (props.orgId !== undefined) {
		return props.orgId
	}
	// 其次使用路由参数
	const routeOrgId = route.query.org_id
	return routeOrgId ? Number(routeOrgId) : undefined
})

watch(
	currentOrgId,
	(newVal) => {
		if (!newVal) return

		// 单班子基础信息
		const org_id = newVal
		initBaseInfo(org_id)
		// 单班子金字塔
		initTeamPyramid(org_id)
		// 单班子散点图
		initTeamScatter(org_id)
		// 单班子指标
		initTeamIndex(org_id)
		// 单班子指标折线图
		initTeamIndexLine(org_id)
		// 单班子巡查审计评级
		initTeamPatrol(org_id)
		// 单班子清正廉洁
		initTeamPersonal(org_id)
		// 单班子班子年度考核
		initTeamHonest(org_id)
		// 单班子班子年度考核
		initTeamAnnual(org_id)
		// 班子结构分析表
		getStructureTable(org_id)
	},
	{
		immediate: true,
	}
)

watch(toRefs(state).coor_menu_selected, () => {
	Promise.all([initTeamScatter(orgId.value), initTeamPyramid(orgId.value), initTeamIndex(orgId.value)])
})

watch(toRefs(state).coor_user_id, (value) => {
	Promise.all([initTeamPyramid(value)])
})

const colorList = ['#60ca71', '#f6dd00', ' #ff9900', '#ff0000']
const imgList = [structure0, structure1, structure2, structure3]

const Index = computed(() => {
	const { teambaseinfo } = pageData
	const { warning_not_pass_number } = teambaseinfo

	if (warning_not_pass_number == 0) return 0

	if (warning_not_pass_number <= 2) return 1

	if (warning_not_pass_number <= 4) return 2

	if (warning_not_pass_number >= 5) return 3

	return 0
})
// 展开pageData中的属性
const { teambaseinfo, teamPyramid, teamAnnual, teamHonest, teamIndex, teamIndexLine, teamPatrol, teamPersonal, teamScatter, structureTable } =
	toRefs(pageData)
</script>

<style lang="scss" scoped>
$small-font: 22px;
$base-font: 24px;
$large-font: 32px;

.town-ships {
	background: #ffffff;

	.town-header {
		padding: 24px 0px 0px 22px;
		display: flex;
		align-items: center;

		& > span {
			cursor: pointer;
		}

		.town-icon {
			margin-right: 9px;
			display: inline-block;
			width: 21px;
			height: 18px;
			background: #000000;
			border-radius: 0px 0px 0px 0px;
			opacity: 1;
			background: url('@/assets/images/back.png') no-repeat center / cover;
		}

		.town-title {
			display: inline-block;
			font-size: 24px;
			font-family: PingFang SC-Heavy, PingFang SC;
			font-weight: bold;
			color: #000000;
		}
	}

	.content {
		padding: 44px 40px;

		.data-card—top {
			position: relative;
			padding: 16px 32px;
			width: 100%;
			height: 120px;
			background: linear-gradient(305deg, #e1f2ff 0%, #ffffff 100%);
			box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
			border-radius: 8px 8px 8px 8px;

			&::after {
				content: '';
				position: absolute;
				top: 50%;
				right: 54px;
				height: 52px;
				width: 709px;
				background: url('../images/lineBack.png') no-repeat center / cover;
				transform: translateY(-50%);
				z-index: 0;
			}

			.card-title {
				font-size: $base-font;
				font-family: Source Han Sans CN-Medium, Source Han Sans CN;
				font-weight: bold;
				color: #333333;
				line-height: 19px;
				cursor: pointer;
			}

			.card-content {
				position: relative;
				z-index: 1;
				margin-top: 39px;
				display: flex;
				align-items: center;
				height: 16px;

				.total-data {
					display: flex;
					align-items: center;
					height: 100%;

					&-title {
						font-size: $base-font;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #000000;
					}

					&-num {
						// margin: -10px 0px 0px 1px;
						font-size: $large-font;
						font-family: ArTarumianBakhum-Regular, ArTarumianBakhum;
						font-weight: 400;
						color: #008eff;
					}
				}

				.position-data {
					margin-left: 85px;
					display: flex;
					align-items: center;
					height: 100%;
					font-size: $base-font;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: rgba(0, 0, 0, 0.9);

					.position-title,
					.position-label {
						font-size: $base-font;
						line-height: $base-font;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
					}

					.position-num {
						margin: 0px 4px;
						vertical-align: bottom;
						font-size: $base-font;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #008eff;
					}
				}
			}
		}

		.data-card-bottom {
			margin-top: 36px;
			width: 100%;
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			gap: 20px;

			.card-item {
				padding: 24px 24px;
				width: 49%;
				height: 162px;

				.card-title {
					display: flex;
					align-items: center;
					font-size: $base-font;
					font-family: Source Han Sans CN-Medium, Source Han Sans CN;
					font-weight: 800;
					color: #000000;

					&::after {
						margin-left: 11px;
						content: '';
						width: 18px;
						height: 18px;
						display: inline-block;
						background: url('@/assets/images/question-mark.png') no-repeat center center / 100% 100%;
						cursor: pointer;
					}
				}

				.card-content {
					display: flex;
				}
			}

			.structure {
				box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);

				.card-title {
					display: flex;
					justify-content: space-between;

					&::after {
						display: none;
					}

					.title-1 {
						display: flex;
						align-items: center;
						font-size: $base-font;
						font-family: Source Han Sans CN-Medium, Source Han Sans CN;
						font-weight: 800;
						color: #000000;

						&::after {
							margin-left: 11px;
							content: '';
							width: 18px;
							height: 18px;
							display: inline-block;
							background: url('@/assets/images/question-mark.png') no-repeat center center / 100% 100%;
							cursor: pointer;
						}
					}

					.right {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 166px;
						height: 36px;
						border-radius: 4px 4px 4px 4px;
						opacity: 1;
						font-size: $base-font;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #008eff;
						line-height: $base-font;
						cursor: pointer;
					}
				}

				.card-content {
					margin-top: 35px !important;
				}

				&-0 {
					background: linear-gradient(305deg, #e5fee6 0%, #ffffff 100%);
				}

				&-1 {
					background: linear-gradient(305deg, #fef0da 0%, #ffffff 100%);
				}

				&-2 {
					background: linear-gradient(305deg, #e1f2ff 0%, #ffffff 100%);
				}

				&-3 {
					background: linear-gradient(305deg, #e1f2ff 0%, #ffffff 100%);
				}

				.card-content {
					margin-top: 16px;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.left {
						white-space: nowrap;
						display: flex;
						align-items: center;

						.text-group {
							display: flex;
							align-items: center;
							cursor: pointer;
						}

						.icon {
							margin-bottom: 3px;
							display: inline-block;
							width: 27px;
							height: 24px;
							background: no-repeat center / 100%;
						}

						.label {
							margin-left: 6px;
							font-size: 21px;
							font-family: Source Han Sans CN-Regular, Source Han Sans CN;
							font-weight: 400;
							line-height: 21px;
						}

						.number {
							font-size: $large-font;
							font-family: ArTarumianBakhum-Regular, ArTarumianBakhum;
							font-weight: 400;
							line-height: $large-font;
						}
					}

					.right {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 156px;
						height: 36px;
						background: #f0f8ff;
						border-radius: 4px 4px 4px 4px;
						opacity: 1;
						font-size: $base-font;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #008eff;
						line-height: $base-font;
						cursor: pointer;
					}
				}
			}

			.ecology,
			.performance,
			.equipment,
			.xuncha {
				background: linear-gradient(305deg, #e1f2ff 0%, #ffffff 100%);
				box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
				border-radius: 8px 8px 8px 8px;

				.number {
					align-self: center;
					margin-bottom: -5px;
					font-size: $large-font;
					font-family: ArTarumianBakhum-Regular, ArTarumianBakhum;
					font-weight: 400;
					color: #008eff;
					line-height: $large-font;
				}

				.card-content {
					margin-top: 20px;

					.number_card {
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						// justify-content: center;
						// height: 44px;
						// background: #d7edff;
						border-radius: 2px 2px 2px 2px;
						opacity: 1;
						white-space: nowrap;
						width: 110px;
						height: 58px;
						font-weight: bold;
						font-size: 18px;
						color: #ffffff;
						line-height: 15px;

						div[type='label'] {
							font-size: $small-font;
							font-family: Source Han Sans CN-Regular, Source Han Sans CN;
							font-weight: 400;
							color: #ffffff;
							line-height: $small-font;
						}

						div[type='number'] {
							margin-top: 5px;
							margin-left: 4px;
							font-size: $small-font;
							font-family: Source Han Sans CN-Medium, Source Han Sans CN;
							font-weight: 500;
							color: #ffffff;
							line-height: $small-font;
						}
					}
				}
			}

			.ecology {
				background: linear-gradient(305deg, #e5fee6 0%, #ffffff 100%);

				.number {
					color: #60ca71;
				}

				.number_card {
					background: #60ca71;
					color: #ffffff;
				}
			}

			.equipment {
				.number {
					color: #008eff;
				}

				.number_card {
					background: #008eff;
					color: #ffffff;
				}
			}

			.xuncha {
				background: linear-gradient(305deg, #ffe4e1 0%, #ffffff 100%);

				.number {
					color: rgba(210, 49, 34, 1);
				}

				.number_card {
					background: rgba(210, 49, 34, 1);
					color: #ffffff;
				}

				.card-title {
					display: flex;
					justify-content: space-between;

					&::after {
						display: none;
					}

					.eq-button {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 80px;
						height: 36px;
						border-radius: 4px 4px 4px 4px;
						opacity: 1;
						font-size: $base-font;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #008eff;
						line-height: $base-font;
						cursor: pointer;
					}
				}
			}

			.performance {
				background: linear-gradient(305deg, #e1f2ff 0%, #ffffff 100%);
				box-shadow: 0px 6px 8px 0px rgba(0, 0, 0, 0.1);
			}

			.equipment {
				.card-title {
					display: flex;
					justify-content: space-between;

					&::after {
						display: none;
					}

					.eq-button {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 80px;
						height: 36px;
						border-radius: 4px 4px 4px 4px;
						opacity: 1;
						font-size: $base-font;
						font-family: Source Han Sans CN-Regular, Source Han Sans CN;
						font-weight: 400;
						color: #008eff;
						line-height: $base-font;
						cursor: pointer;
					}
				}
			}
		}

		.card-row {
			width: 100%;
			display: flex;
			justify-content: space-between;

			& > div {
				width: 49%;
			}
		}
	}
}

.up-icon {
	display: inline-block;
	width: 6px;
	height: 10px;
	background: url(../images/top.png) no-repeat center/ 100% 100%;
}

.up-text {
	font-size: 14px;
	font-family: Source Han Sans CN-Regular, Source Han Sans CN;
	font-weight: 400;
	color: #ff0000;
	line-height: 18px;
}

.margin-left-24 {
	margin-left: 24px;
}

.margin-left-16 {
	margin-left: 16px;
}

.margin-top-40 {
	margin-top: 40px;
}

.cursor-pointer {
	cursor: pointer;
}
</style>
<style>
.town-ship-modal {
	width: 1489px;
}

.structure-modal {
	width: 1689px;
}

.equipment-modal {
	width: 1489px;
}

.openAimodal {
	position: fixed;
	top: 300px;
	right: 0;
	padding: 20px 16px;
	background: #13af0b;
	box-shadow: 0px 0px 26px 0px rgba(3, 69, 0, 0.28);
	font-weight: 500;
	font-size: 23px;
	color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8px 8px 8px 8px;
}
</style>
