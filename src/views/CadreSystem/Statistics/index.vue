<template>
	<div class="Statistics">
		<div :class="`left-ignore ${!sliderStatus && 'translate-animation'}`">
			<div class="scroll-box">
				<OrgTree @select="onTreeSelect" ref="orgTreeRef" />
			</div>
			<div :class="['slider-control', !sliderStatus && 'slider-hidden']" @click="onExpand"></div>
		</div>
		<div class="content-wrap" ref="scrollContainer">
			<component :is="currentView" :orgId="orgId" :orgName="orgName" :onOrgSelect="onOrgSelect" :change-page="onChangePage" />
		</div>
	</div>
</template>

<script lang="ts" setup>
import { nextTick, ref } from 'vue'
import { useRoute } from 'vue-router'
import OrgTree from '@/components/OrgTree.vue'
import Townships from './components/townships1.vue'
import OrganizedGroup from './components/organizedGroup.vue'
import StructuralAnalysis from './components/structuralAnalysis.vue'
import StructuralAnalysisParty from './components/structuralAnalysis-party.vue'
import { getTeamStructureAnalyze } from '@/apis/statistics'

import { scrollToTop } from '@/utils/utils'
const orgName = ref<string | undefined>(undefined)
const currentView = ref<any>(OrganizedGroup)
const sliderStatus = ref<boolean>(false)
const orgId = ref<number | undefined>(1)
const scrollContainer = ref<any>(null)
const orgTreeRef = ref()
const { org_id } = useRoute().query

const onTreeSelect = (selectedKey: any, org: any) => {
	if (!selectedKey) return

	orgId.value = selectedKey
	orgName.value = org.short_name

	if (selectedKey === 1) {
		currentView.value = OrganizedGroup
	} else if (selectedKey) {
		currentView.value = Townships
	}
	scrollToTop(scrollContainer.value)
}
const onOrgSelect = (org_id: any) => {
	org_id && orgTreeRef.value.onSelectApi(org_id)
}
const onExpand = () => {
	sliderStatus.value = !sliderStatus.value
}

const onChangePage = async (type = 1) => {
	let comp = null
	if (type === 1) {
		const pageType = await teamStructureAnalyze(orgId.value)

		if (pageType === 1) {
			comp = StructuralAnalysisParty
		} else {
			comp = StructuralAnalysis
		}
	}
	currentView.value = type === 1 ? comp : Townships
}

const teamStructureAnalyze = async (org_id: any) => {
	const { code, data } = await getTeamStructureAnalyze({ org_id })
	if (code === 0) {
		return data.type
	}
}
nextTick(() => {
	org_id && orgTreeRef.value.onSelectApi(org_id)
})
</script>

<style lang="less" scoped>
.Statistics {
	display: flex;
	height: 100%;
	position: relative;
	@font-face {
		font-family: 'iconfont';
		src: url('./BAKHUM.TTF');
		src: url('./BAKHUM.TTF') format('truetype');
		font-weight: normal;
		font-style: normal;
	}
	::v-deep .bakhum {
		font-family: 'iconfont' !important;
		-webkit-font-smoothing: antialiased;
		-webkit-text-stroke-width: 0.2px;
		-moz-osx-font-smoothing: grayscale;
	}
	.content-wrap {
		margin: 0px 20px 0px;
		flex: 1;
		overflow: auto;
	}
	.translate-animation {
		position: absolute !important;
		transform: translate(-100%);
		z-index: 999;
	}
	.left-ignore {
		position: relative;
		padding: 16px 8px;
		height: 100%;
		background: #ffffff;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		transition: transform 0.3s;

		.scroll-box {
			height: 100%;

			overflow: auto;
			& > div {
				width: auto;
			}
			::v-deep(.ant-tree-treenode) {
				margin-bottom: 5px;
			}
		}

		.slider-control {
			position: absolute;
			right: 0;
			top: 40%;
			width: 30px;
			height: 56px;
			background: url(../image/slider-icon.png) no-repeat center / cover;
			cursor: pointer;
		}
		.slider-hidden {
			transform: rotateZ(180deg) translate(-100%);
		}

		// ::v-deep(.ant-tree-title) {
		// 	font-size: 16px;
		// }
		// ::v-deep(.anticon) {
		// 	font-size: 15px;
		// }
	}
}
</style>
