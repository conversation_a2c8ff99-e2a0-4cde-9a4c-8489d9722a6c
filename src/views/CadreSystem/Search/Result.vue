<template>
	<div class="result">
		<div class="left">
			<div class="inner">
				<span @click="onSearchChange"> 查 询 条 件 </span>
			</div>
			<span class="icon" @click="onSearchChange"></span>
		</div>
		<div class="center">
			<div class="filter-menu">
				<div class="filter">
					<div class="label">年龄：</div>
					<div class="filter-btn">
						<div :class="`item ${filterState.sort_type === 'ASC' ? 'active' : ''}`" @click="handlePaginationChange('birthday', 'ascend')">
							<div v-html="svgIconUp(filterState.sort_type === 'ASC' ? '#008EFF' : undefined)" :class="`icon`"></div>
							升序
						</div>
						<div :class="`item ${filterState.sort_type === 'DESC' ? 'active' : ''}`" @click="handlePaginationChange('birthday', 'descend')">
							<div v-html="svgIcon(filterState.sort_type === 'DESC' ? '#008EFF' : undefined)" :class="`icon`"></div>
							降序
						</div>
					</div>
				</div>
				<div class="filter">
					<div class="label">干部指数：</div>
					<div class="filter-btn">
						<div :class="`item ${filterState.cadre_sort_type === 'ASC' ? 'active' : ''}`" @click="handlePaginationChange('cadre_index', 'ascend')">
							<div v-html="svgIconUp(filterState.cadre_sort_type === 'ASC' ? '#008EFF' : undefined)" :class="`icon`"></div>
							升序
						</div>
						<div :class="`item ${filterState.cadre_sort_type === 'DESC' ? 'active' : ''}`" @click="handlePaginationChange('cadre_index', 'descend')">
							<div v-html="svgIcon(filterState.cadre_sort_type === 'DESC' ? '#008EFF' : undefined)" :class="`icon`"></div>
							降序
						</div>
					</div>
				</div>
				<div class="filter">
					<div class="label"></div>
					<div class="filter-btn">
						<div :class="`item ${pageType === 1 ? 'active' : ''}`" @click="pageType = 1">
							<div v-html="svgIconPing(pageType === 1 ? '#008EFF' : undefined)" :class="`icon`"></div>
							平铺
						</div>
						<div :class="`item ${pageType === 2 ? 'active' : ''}`" @click="pageType = 2">
							<div v-html="svgIconTable(pageType === 2 ? '#008EFF' : undefined)" :class="`icon`"></div>
							列表
						</div>
					</div>
				</div>
			</div>
			<div class="main-box">
				<template v-if="pageType === 2">
					<a-table
						class="search-res-table"
						rowKey="user_id"
						:loading="tableLoading"
						:columns="columns"
						:data-source="dataSource"
						:pagination="false"
						:scroll="{ y: '30.395833vw' }"
						:customRow="(record:any)=> {
							return {
								style: {
									backgroundColor: selectedRowKeys.includes(record.user_id)  ? 'rgba(237,247,255,0.9)' : ''
								},
								onClick: () => {
									onSelect(record)
								}
							}
						}"
					>
						<template #bodyCell="{ column, record, index }">
							<template v-if="column.key === 'index'">
								{{ index + 1 }}
							</template>
							<template v-if="column.key === 'username'">
								<a @click="goPage({ path: '/cadre-portrait/home', user_id: record.user_id })">
									{{ record.username }}
								</a>
							</template>
							<template v-if="column.key === 'birthday'">
								<span>{{ record.birthday }}（{{ record.age }}岁）</span>
							</template>
							<template v-if="column.key === 'action'">
								<a @click="goPage({ path: '/cadre-portrait/cadre-table', user_id: record.user_id })" class="table-a">干部任免审批表</a>
								<a-divider type="vertical" />
								<a @click="goPage({ path: '/cadre-portrait/home', user_id: record.user_id })" class="table-a">干部画像</a>
							</template>
							<template v-if="column.key === 'avatar'">
								<CodeAvatar :user_id="record.user_id" :head_url="record.head_url">
									<template #avatar="{ avatar }">
										<img :src="avatar" class="avatar" />
									</template>
								</CodeAvatar>
							</template>
						</template>
					</a-table>
				</template>
				<template v-else>
					<div class="user-box" ref="userBox">
						<div
							:class="{
								'userinfo-box': true,
								selected: selectedRowKeys.includes(item.user_id),
								'light-selected': selectedRowKeys.includes(item.user_id),
							}"
							v-for="item in dataSource"
							:key="item.user_id"
							@click="onSelect(item)"
						>
							<div class="top-box">
								<div class="avatar">
									<CodeAvatar
										@click.stop="goPage({ path: '/cadre-portrait/home', user_id: item.user_id })"
										:user_id="item.user_id"
										:head_url="item.head_url"
									>
										<template #avatar="{ avatar }">
											<img :src="avatar" />
										</template>
									</CodeAvatar>
								</div>
								<div class="info">
									<div class="name m-top-7">
										<span>{{ item.userName || item.username }}</span>
									</div>
									<div class="index-box m-top-10"><span class="limit-width"> 出生年月 </span>：{{ item.birthday }}</div>
									<div class="index-box flex m-top-10">
										<span><span class="limit-width">干部指数</span>：{{ item.cadre_index || '-' }}</span>
									</div>
									<div class="index-box flex m-top-10">
										<span><span class="limit-width">排名</span>：{{ item.cadre_index_rank }}</span>
									</div>
								</div>
							</div>
							<div class="bottom-box">
								<div class="info-box m-top-1"><span class="limit-width">全日制学历</span>：{{ item.diploma }}</div>
								<div class="info-box m-top-15"><span class="limit-width">毕业学校及专业</span>：{{ item.school }}</div>
								<div class="info-box m-top-15 limit-3-row"><span class="limit-width">现任职务</span>：{{ item.current_job }}</div>
							</div>
							<span class="select-icon"></span>
						</div>
					</div>
				</template>
			</div>
		</div>
		<div class="right">
			<div class="already-have">
				<div class="have-nubmer">
					<span class="label">已选: </span>
					<span class="select-number">{{ quotaList.length }}</span>
					<span class="tips">（对比分析最多可选择10人）</span>
				</div>
				<div class="clear">
					<a-popconfirm title="确认清空?" ok-text="确认" cancel-text="取消" @confirm="onClear">
						<a>一键清空</a>
					</a-popconfirm>
				</div>
				<div class="have-name">
					<div class="inner-box">
						<div v-for="(item, index) in quotaList" :key="index" class="select-box">
							<span class="icon">{{ item.username }}</span
							><span class="have-delete" @click.stop="deleteName(item)"></span>
						</div>
					</div>
				</div>
				<div class="button-box">
					<a-button class="my-button" :disabled="selectedRowKeys.length < 2 || selectedRowKeys.length > 10" @click="onCompare">对比分析</a-button>
					<a-button type="primary" class="my-button" :disabled="selectedRowKeys.length < 1" @click="onCollect">批量收藏</a-button>
				</div>
			</div>
		</div>
	</div>
	<a-modal class="folder-modal" :closable="false" :visible="collectVisible" width="" destroyOnClose :footer="null" @cancel="onClose">
		<Folder @close="onClose" @success="onSuccess" modal-type="collect" :source-ids="selectedRowKeys.join(',')" />
	</a-modal>
</template>

<script lang="ts" setup>
import { getPmsLeader1 } from '@/apis/search'
import CodeAvatar from '@/components/CodeAvatar.vue'
import { useComparative } from '@/store/comparative'
import { Base64, getUserInfo, MeScrollInit } from '@/utils/utils'
import { nextTick, onActivated, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { svgIcon, svgIconPing, svgIconTable, svgIconUp } from './svg'
const CDN_URL = import.meta.env.VITE_CDN_URL

import { message } from 'ant-design-vue'
import 'mescroll.js/mescroll.min.css'

const props = defineProps({
	getParams: {
		type: Function,
		required: true,
	},
})

const emits = defineEmits(['close', 'update-total'])

// const { getParams } = toRefs(props)

const mescroll1 = ref()

const router = useRouter()

const comparative = useComparative()

const quotaList = ref<any>([])
const currentPage = ref(1)
const selectedRowKeys = ref<any>([])
const tableLoading = ref<boolean>(false)
const tableBoxRef = ref<any>(null)
const dataSource: any = ref([])
const collectVisible = ref(false)
const selected = ref(false)
// 滚动盒子
const userBox = ref()
// 1.平铺 2.表格
const pageType = ref(2)

const filterState = reactive({ sort_type: undefined, cadre_sort_type: 'DESC' })

const handlePaginationChange = async (field: any, order: any) => {
	const sortMap: any = {
		ascend: 'ASC',
		descend: 'DESC',
	}

	switch (field) {
		case 'cadre_index':
			filterState.sort_type = undefined
			filterState.cadre_sort_type = sortMap[order]
			break
		case 'birthday':
			filterState.sort_type = sortMap[order]
			filterState.cadre_sort_type = undefined
			break
	}

	currentPage.value = 1
	dataSource.value = []

	mescroll1.value.showUpScroll()

	const res = await loadData()

	if (res.code === 0) {
		dataSource.value = res.data.content
		// 发出totalElements事件给父组件
		if (res.data && res.data.totalElements !== undefined) {
			emits('update-total', res.data.totalElements)
		}
	}

	mescroll1.value.endUpScroll()
}

const loadData = async () => {
	const formState = props.getParams()
	const _params: any = { ...formState, page: currentPage.value++, ...filterState }
	const res = await getPmsLeader1(_params)
	if (res.code !== 0) {
		message.warning(res.message)
	}
	const { totalElements }: any = res.data || {}
	if (totalElements !== undefined) {
		emits('update-total', totalElements)
	}
	return res
}

const columns = [
	{
		title: '照片',
		dataIndex: 'avatar',
		key: 'avatar',
		width: '13%',
		align: 'center',
	},
	{
		title: '姓名',
		dataIndex: 'username',
		key: 'username',
		width: '9%',
		align: 'center',
	},
	// {
	// 	title: '序号',
	// 	dataIndex: 'index',
	// 	key: 'index',
	// 	width: '5%',
	// 	align: 'center',
	// },
	{
		title: '现任职务',
		dataIndex: 'current_job',
	},
	{
		title: '出生年月(年龄)',
		dataIndex: 'birthday',
		key: 'birthday',
		width: '15%',
		// sorter: true,
		// defaultSortOrder: 'ascend',
	},
	{
		title: '排序',
		dataIndex: 'cadre_index_rank',
		width: '10%',
		align: 'center',
	},
	{
		title: '全日制学历',
		dataIndex: 'diploma',
		width: '13%',
		align: 'center',
	},
	{
		title: '毕业学校及专业',
		dataIndex: 'school',
	},
	{
		title: '干部指数',
		dataIndex: 'cadre_index',
		width: '10%',
		align: 'center',
		// sorter: true,
	},
	// {
	// 	title: '操作',
	// 	key: 'action',
	// 	align: 'center',
	// },
]

const goPage = ({ path, ...parmas }: any) => {
	const userInfo = JSON.stringify(getUserInfo())

	const _h = Base64.encode(userInfo)

	router.push({
		path,
		query: {
			...parmas,
			_h,
		},
	})
}

/**
 * @description: 选中项
 * @param {*} record
 * @return {*}
 */
let _selectedRowKeys: any = []
const onSelect = (record: any) => {
	const index = _selectedRowKeys.findIndex((item: any) => {
		return item === record.user_id
	})
	if (index == -1) {
		_selectedRowKeys.push(record.user_id)
		quotaList.value.push(record)
	} else {
		if (index == -1) {
			return void 0
		}
		_selectedRowKeys.splice(index, 1)
		quotaList.value.splice(index, 1)
	}
	selectedRowKeys.value = [..._selectedRowKeys]

	record.selected = !record.selected
}
// 删除选中项中的数据
const deleteName = (item: any) => {
	// 获取下标
	const index = selectedRowKeys.value.findIndex((item1: any) => {
		return item1 === item.user_id
	})

	const index1 = quotaList.value.findIndex((item1: any) => item1.user_id === item.user_id)

	_selectedRowKeys.splice(index, 1)

	selectedRowKeys.value = [..._selectedRowKeys]

	quotaList.value.splice(index1, 1)
}

const onClear = () => {
	_selectedRowKeys = []
	selectedRowKeys.value = []
	quotaList.value = []
}

const onCompare = () => {
	goPage({ path: '/comparison-results', user_id: selectedRowKeys.value.join(','), config_ids: comparative.config_ids.join(',') })
}

const onSearchChange = () => {
	emits('close')
}
// 滚动距离
const scrollInstance = ref<any>(null)
const scrollInstance1 = ref()

const bindScrollLoad = () => {
	const scrollBody: any = document.querySelector('.search-res-table .ant-table-body') || userBox.value
	mescroll1.value = MeScrollInit(scrollBody, {
		auto: true,
		onScroll: (_mescroll: any, y: number) => {
			scrollInstance.value = y
		},
		callback: async () => {
			const formState = props.getParams()

			const _params: any = { ...formState, page: currentPage.value++, ...filterState }

			const res = await getPmsLeader1(_params)

			if (res.code === 0) {
				let { content = [], totalPages }: any = res.data || {}
				// 安卓
				if (content == null) {
					content = []
				}

				dataSource.value.push(...content)
				mescroll1.value.endByPage(content, totalPages || 0)
				// 发出totalElements事件给父组件（分页加载时）
			} else {
				// 弹出提示框显示错误信息
				message.warning(res.message)
				// 关闭加载
				mescroll1.value.endErr()
			}
		},
		htmlLoading: undefined,
	})
}

const onClose = () => {
	collectVisible.value = false
}

const onSuccess = () => {
	collectVisible.value = false
}
const onCollect = () => {
	collectVisible.value = true
}

onActivated(() => {
	const scrollBody: any = document.querySelector('.search-res-table .ant-table-body') || userBox.value

	scrollInstance.value && scrollBody?.scrollTo(0, scrollInstance.value)
})

// 重新加载数据的函数
const reloadData = async () => {
	currentPage.value = 1
	dataSource.value = []

	mescroll1.value.showUpScroll()

	const res = await loadData()

	if (res.code === 0) {
		dataSource.value = res.data.content
		// 发出totalElements事件给父组件
		if (res.data && res.data.totalElements !== undefined) {
			emits('update-total', res.data.totalElements)
		}
	}

	mescroll1.value.endUpScroll()
}

onMounted(() => {
	bindScrollLoad()
	// 初始加载数据
	reloadData()
})

// 监听查询参数变化，自动重新加载数据
watch(
	() => props.getParams(),
	() => {
		// 当查询参数变化时，重新加载数据
		reloadData()
	},
	{ deep: true }
)

watch(pageType, () => {
	nextTick(() => {
		bindScrollLoad()
	})
})
</script>

<style lang="less" scoped>
.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}
.result {
	width: 100%;
	height: 100%;
	display: flex;
	.left {
		position: relative;
		margin-right: 12px;
		background: #ffffff;
		width: 105px;
		height: 100%;
		.inner {
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			color: #008eff;
			font-size: 24px;
			writing-mode: vertical-lr;
		}
		.icon {
			position: absolute;
			top: 50%;
			right: 0px;
			transform: translateY(-50%) rotateZ(180deg);
			display: inline-block;
			width: 30px;
			height: 105px;
			background: url('../image/icon-11.png') center / cover no-repeat;
		}
	}
	.center {
		display: flex;
		flex-direction: column;
		background: #ffffff;
		flex: 1;
		height: 100%;
		overflow: hidden;
		.filter-menu {
			padding: 22px 27px;
			display: flex;
			justify-content: space-between;
			.filter {
				display: flex;
				align-items: center;
				.label {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 21px;
					color: #000000;
				}
				.active {
					color: #008eff;
				}
				.filter-btn {
					.flex-center;

					.item {
						.flex-center;
						width: 128px;
						height: 48px;
						background: #f7f8fa;
						border-radius: 38px;
						font-size: 21px;
						.icon {
							width: 27px;
							height: 27px;
						}
						&:not(:last-child) {
							margin-right: 15px;
						}
					}
				}
			}
		}
		.main-box {
			padding: 27px;
			flex: 1;
			overflow: hidden;
			.avatar {
				width: 80px;
				height: 105px;
				object-fit: cover;
			}
			:deep(.ant-table-cell) {
				font-size: 21px;
			}
		}
		.user-box {
			height: 100%;
			overflow: auto;
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;
			gap: 27px 1%;
			overflow: auto;
			.top-box {
				display: flex;
			}

			.userinfo-box {
				position: relative;
				padding: 16px 18px;
				display: flex;
				flex-direction: column;
				width: 49%;
				height: 355px;
				background: #ffffff;
				box-shadow: 0px 0 11px 0px rgba(0, 0, 0, 0.06);
				border-radius: 3px 3px 3px 3px;
				opacity: 1;
				.limit-width {
					display: inline-block;
					width: 147px;
					text-align-last: justify;
				}
				.limit-width-s {
					display: inline-block;
					width: 70px;
					text-align-last: justify;
				}
				.avatar {
					flex-shrink: 0;
					margin-right: 15px;
					width: 105px;
					height: 141px;
					border-radius: 3px 3px 3px 3px;
					opacity: 1;
					img {
						width: 100%;
						height: 100%;
						object-fit: contain;
					}
				}

				.info {
					.name {
						display: flex;
						align-items: center;
						width: 100%;
						font-size: 24px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 600;
						color: #000000;
						line-height: 25px;
					}
					.birth {
						font-size: 21px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #000000;
						line-height: 19px;
					}
					.index-box {
						font-size: 21px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #000000;
						line-height: 24px;
						span:not(:last-child) {
							margin-right: 24px;
						}
					}
					.position-box {
						font-size: 17px;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #000000;
						line-height: 22px;
						// 超过两行省略
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
					}
				}
				.bottom-box {
					font-size: 21px;
					.limit-3-row {
						// 保留三行超過省略
						display: -webkit-box;
						overflow: hidden; /* 隐藏溢出的内容 */
						display: -webkit-box;
						-webkit-box-orient: vertical;
						text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
						-webkit-line-clamp: 3; /* 保留的行数 */
						line-clamp: 3; /* 保留的行数 */
					}
				}
			}
		}
	}
	.right {
		margin-left: 12px;
		background: #ffffff;
		height: 100%;
		width: 438px;

		.already-have {
			padding-bottom: 29px;
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			.clear {
				flex-shrink: 0px;
				margin: 0px 18px;
				height: 53px;
				background: #f4faff;
				border-radius: 6px 6px 6px 6px;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 21px;
				color: #008eff;
				display: flex;
				align-items: center;
				justify-content: center;
				a {
					color: #2462ff;
				}
			}
			.have-nubmer {
				display: flex;
				align-items: center;
				flex-shrink: 0px;
				padding: 28px 18px;
				width: 100%;
				height: 76px;
				background: transparent;
				border-radius: 4px 4px 4px 4px;
				opacity: 1;
				font-size: 14px;
				font-weight: 400;
				.label {
					font-size: 23px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 500;
					color: rgba(0, 0, 0, 0.9);
					line-height: 26px;
				}
				.select-number {
					margin-left: 10px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					color: #2462ff;
					font-weight: bold;
					font-size: 29px;
				}
				.tips {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 23px;
					color: rgba(0, 0, 0, 0.9);
				}
			}

			.have-name {
				padding: 10px;
				flex: 1;
				border-radius: 4px 4px 4px 4px;
				opacity: 1;
				overflow: auto;
				.inner-box {
					padding: 10px;
					height: 100%;
					background-color: #ffffff;
					.select-box {
						padding: 18px 0px 24px;
						display: flex;
						align-items: center;
						justify-content: space-between;
						border-bottom: 1px solid rgba(0, 0, 0, 0.15);

						.icon {
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							font-size: 23px;
							color: rgba(0, 0, 0, 0.9);
							line-height: 26px;
							&::before {
								display: inline-block;
								margin-right: 12px;
								content: '';
								width: 11px;
								height: 11px;
								border-radius: 50%;
								background: #008eff;
							}
						}
					}
				}
				div {
					margin-bottom: 13px;
				}

				span {
					font-size: 16px;
					font-family: Source Han Sans CN-Regular, Source Han Sans CN;
					font-weight: 400;
					color: #222222;
				}

				.have-delete {
					width: 20px;
					height: 20px;
					display: inline-block;
					background-image: url('../image/delete.png');
					background-size: 100% 100%;
					margin-left: 15px;
					vertical-align: middle;
					cursor: pointer;
				}
			}
			.button-box {
				padding: 0px 20px;
				width: 100%;
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;
				.my-button {
					font-size: 24px;
					padding: 0px;
					width: 189px;
					height: 66px;
				}
			}
		}
	}
}
.light-selected {
	background-color: rgba(237, 247, 255, 0.9);
	// .name,
	// .birth,
	// .index-box,
	// .position-box {
	// 	color: #008eff !important;
	// }
}

.selected {
	background-color: rgba(237, 247, 255, 0.9) !important;
	// .name,
	// .birth,
	// .index-box,
	// .position-box {
	// 	color: #008eff !important;
	// }

	.select-icon {
		position: absolute;
		top: 0;
		right: 0;
		display: inline-block;
		width: 39px;
		height: 39px;
		background: url('../image/select-icon.png') no-repeat center / cover;
		opacity: 1;
	}
}
.main-box {
	:deep(.ant-table-wrapper) {
		height: 100%;
		.ant-spin-nested-loading,
		.ant-table-container,
		.ant-table,
		.ant-spin-container,
		.ant-spin-nested-loading {
			height: 100%;
		}
		.ant-table-container {
			display: flex;
			flex-direction: column;
			.ant-table-body {
				flex: 1;
				max-height: none !important;
			}
		}
	}
}
</style>
<style lang="less">
.folder-modal {
	width: 752px;
	.ant-modal-body {
		padding: 0px !important;
	}
}
.mescroll-upwarp,
.mescroll-hardware {
	width: 100%;
}
</style>
