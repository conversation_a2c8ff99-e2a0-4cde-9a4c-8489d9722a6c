<template>
	<div class="new-search">
		<div class="ai-prompt" v-if="!isSearch">
			<div class="ai-prompt-flex">
				<img class="icon" src="@/assets/images/ai-list/ai_search_avatar.png" />
				<div class="content">提出您的要求，帮您查询(例如:90后女性干部，3年以上乡镇工作经验或者2年以上乡镇领导经验，大专以上学历。)</div>
				<a-button type="primary" class="poc-btn" @click="showChatClick">AI 语音</a-button>
			</div>
		</div>
		<div class="main-content">
			<div class="left-box">
				<div
					class="menu-item"
					:class="{ 'menu-item-active': activeIndex === index }"
					@click="onActive(item, index)"
					v-for="(item, index) in menuConfig"
					:key="index"
				>
					<img class="icon" :src="activeIndex === index ? item.activeIcon : item.icon" />
					<div class="label">{{ item.label }}</div>
				</div>
			</div>
			<div class="right-box">
				<div class="condition-box">
					<a-form ref="formRef" :model="formState">
						<div class="base-info" data-label="基本信息">
							<div class="title">基本信息</div>
							<!-- <a-form-item name="age">
								<PocCheck :options="ageOption" v-model:value="formState.age" />
							</a-form-item> -->
							<div class="flex-avg border-bottom">
								<PocFormItem name="name" label="姓名" :wrapperCol="{ span: 20 }">
									<a-input type="text" placeholder="请输入" class="poc-input" v-model:value="formState.name" />
								</PocFormItem>
								<PocFormItem name="gender" label="性别">
									<PocCheck :options="genderOption" v-model:value="formState.gender" />
								</PocFormItem>
								<PocFormItem name="ethic" label="民族">
									<PocCheck :options="nationOption" v-model:value="formState.ethic" />
								</PocFormItem>
								<PocFormItem label="出生年月">
									<div class="date-box date-picker-width">
										<a-form-item name="birthday_start">
											<a-date-picker
												v-model:value="formState['birthday_start']"
												value-format="YYYY-MM"
												format="YYYY-MM"
												inputReadOnly
												picker="month"
												@openChange="onDataPickerChange"
											/>
										</a-form-item>
										<div class="line"></div>
										<a-form-item name="birthday_end">
											<a-date-picker
												v-model:value="formState['birthday_end']"
												value-format="YYYY-MM"
												inputReadOnly
												picker="month"
												@openChange="onDataPickerChange"
											/>
										</a-form-item>
									</div>
								</PocFormItem>
							</div>
							<div class="flex-avg border-bottom">
								<PocFormItem name="name" label="入党时间">
									<div class="date-box date-picker-width">
										<a-form-item name="join_time_start">
											<a-date-picker
												v-model:value="formState['join_time_start']"
												inputReadOnly
												value-format="YYYY-MM-DD"
												@openChange="onDataPickerChange"
											/>
										</a-form-item>
										<div class="line"></div>
										<a-form-item name="join_time_end">
											<a-date-picker
												v-model:value="formState['join_time_end']"
												inputReadOnly
												value-format="YYYY-MM-DD"
												@openChange="onDataPickerChange"
											/>
										</a-form-item>
									</div>
								</PocFormItem>
								<PocFormItem name="political" label="政治面貌">
									<PocCheck :options="politicalOption" v-model:value="formState.political" />
								</PocFormItem>
								<PocFormItem name="profession_specialty" label="熟悉专业和特长" :wrapperCol="{ span: 20 }">
									<a-input type="text" placeholder="请输入" v-model:value="formState.profession_specialty" class="poc-input" />
								</PocFormItem>
								<PocFormItem label="专业技术职务" name="technical_position" :wrapperCol="{ span: 20 }">
									<a-input type="text" v-model:value="formState.technical_position" placeholder="请输入" class="poc-input" />
								</PocFormItem>
							</div>
							<div class="flex-avg border-bottom">
								<PocFormItem label="培训情况" name="train" :wrapperCol="{ span: 20 }">
									<PocCheck :options="trainOption" v-model:value="formState.train" type="radio" />
								</PocFormItem>
							</div>
						</div>
						<div class="base-info" data-label="职务信息">
							<div class="title">职务信息</div>
							<!-- <a-form-item name="age">
								<PocCheck :options="ageOption" v-model:value="formState.age" />
							</a-form-item> -->
							<div class="flex-avg border-bottom">
								<PocFormItem name="cadre_category" label="干部类别">
									<PocCheck :options="cadreTypeOption" v-model:value="formState.cadre_category" />
								</PocFormItem>
							</div>
							<div class="flex-avg border-bottom">
								<PocFormItem name="identity" label="干部身份" :wrapperCol="{ span: 24 }">
									<PocCheck :options="codeOptionMap.cadreLevel" v-model:value="formState.identity" />
								</PocFormItem>
							</div>
							<PocFormItem label="干部任用情况" :wrapperCol="{ span: 20 }">
								<div class="date-box date-picker-width">
									<span class="label-text"> 研究时间： </span>
									<a-form-item name="assign_start">
										<a-date-picker
											v-model:value="formState['assign_start']"
											format="YYYY-MM-DD"
											value-format="YYYY-MM-DD"
											:disabled-date="
												(date) => {
													return date.year() < 2021
												}
											"
										/>
									</a-form-item>
									<div class="line"></div>
									<a-form-item name="assign_end">
										<a-date-picker
											v-model:value="formState['assign_end']"
											value-format="YYYY-MM-DD"
											format="YYYY-MM-DD"
											:disabled-date="
												(date) => {
													return date.year() < 2021
												}
											"
										/>
									</a-form-item>
									<span class="label-text m-left-20"> 类别： </span>
									<a-form-item name="assign_type">
										<a-select v-model:value="formState.assign_type" class="custom-select">
											<a-select-option value="全部">全部</a-select-option>
											<a-select-option :value="i.op_key" v-for="i in assignTypeOptions" :key="i.op_key">{{ i.op_value }}</a-select-option>
										</a-select>
									</a-form-item>
								</div>
							</PocFormItem>
							<div class="flex-avg border-bottom">
								<PocFormItem name="current_job" label="现任职务" :wrapperCol="{ span: 20 }">
									<a-input type="text" v-model:value="formState.current_job" placeholder="请输入" class="poc-input" />
								</PocFormItem>
								<PocFormItem name="gender" label="任现职务时间" :wrapperCol="{ span: 24 }">
									<div class="date-box">
										大于
										<a-form-item name="current_job_time_gte" :wrapperCol="{ span: 24 }" style="width: 40%">
											<a-input-number
												style="width: 100%"
												v-model:value="formState['current_job_time_gte']"
												inputReadOnly
												value-format="YYYY-MM-DD"
												@openChange="onDataPickerChange"
											/>
										</a-form-item>
										年
										<!-- <div class="line"></div> -->
										&nbsp; &nbsp; 小于
										<a-form-item name="current_job_time_lte" :wrapperCol="{ span: 24 }" style="width: 40%">
											<a-input-number
												style="width: 100%"
												v-model:value="formState['current_job_time_lte']"
												inputReadOnly
												value-format="YYYY-MM-DD"
												@openChange="onDataPickerChange"
											/>
										</a-form-item>
										年
									</div>
								</PocFormItem>
							</div>
							<div class="flex-avg border-bottom">
								<PocFormItem name="current_rank" label="干部职级" :wrapperCol="{ span: 24 }">
									<PocCheck :options="cadrePositionOption" v-model:value="formState.current_rank" />
								</PocFormItem>
								<PocFormItem label="现职级任职时间" :wrapperCol="{ span: 24 }">
									<div class="date-box">
										大于
										<a-form-item name="current_rank_time_gte" :wrapperCol="{ span: 24 }" style="width: 40%">
											<a-input-number style="width: 100%" v-model:value="formState.current_rank_time_gte" />
										</a-form-item>
										年
										<!-- <div class="line"></div> -->
										&nbsp; &nbsp; 小于
										<a-form-item name="current_rank_time_lte" :wrapperCol="{ span: 24 }" style="width: 40%">
											<a-input-number style="width: 100%" v-model:value="formState.current_rank_time_lte" />
										</a-form-item>
										年
									</div>
								</PocFormItem>
							</div>
							<div class="flex-avg border-bottom">
								<PocFormItem name="work_resume_start" label="简历时间" :wrapperCol="{ span: 24 }">
									<a-date-picker
										style="width: 37%"
										v-model:value="formState['work_resume_start']"
										value-format="YYYY-MM"
										format="YYYY-MM"
										picker="month"
										inputReadOnly
										@openChange="onDataPickerChange"
									/>
								</PocFormItem>
							</div>
						</div>
						<div class="base-info" data-label="教育信息">
							<div class="title">教育信息</div>
							<!-- <a-form-item name="age">
								<PocCheck :options="ageOption" v-model:value="formState.age" />
							</a-form-item> -->
							<div class="flex-avg border-bottom">
								<PocFormItem name="full_time_education" label="初始学历">
									<PocCheck :options="fullTimeEducationOption" v-model:value="formState.full_time_education" />
								</PocFormItem>
								<PocFormItem name="on_job_education" label="最高学历">
									<PocCheck :options="fullTimeEducationOption" v-model:value="formState.on_job_education" />
								</PocFormItem>
							</div>
							<div class="flex-avg border-bottom">
								<PocFormItem name="full_time_school" label="全日制院校" :wrapperCol="{ span: 20 }">
									<a-input type="text" v-model:value="formState.full_time_school" placeholder="毕业院校包含的关键字" class="poc-input" />
								</PocFormItem>
								<PocFormItem name="major" label="专业" :wrapperCol="{ span: 24 }">
									<a-input v-model:value="formState.major" placeholder="请输入" class="poc-input" />
								</PocFormItem>
							</div>
						</div>
						<div class="base-info" data-label="其他信息">
							<div class="title">其他信息</div>
							<!-- <a-form-item name="age">
								<PocCheck :options="ageOption" v-model:value="formState.age" />
							</a-form-item> -->
							<div class="flex-avg border-bottom">
								<PocFormItem name="annual_assessment" label="年度考核" :wrapperCol="{ span: 20 }">
									<div class="year-aprove form-common-style">
										<a-form-item name="year_examine_start" style="width: 18%" :wrapperCol="{ span: 24 }">
											<a-input-number placeholder="请输入" style="width: 100%" v-model:value="formState.year_examine_start" :min="0" />
										</a-form-item>
										<span class="line"></span>
										<a-form-item name="year_examine_end" style="width: 18%" :wrapperCol="{ span: 24 }">
											<a-input-number placeholder="请输入" style="width: 100%" v-model:value="formState.year_examine_end" :min="0" />
										</a-form-item>
										<span class="text-style">年有</span>
										<a-form-item name="examine_count">
											<a-input-number placeholder="请输入" v-model:value="formState.examine_count" :min="0" />
										</a-form-item>
										<span class="text-style">次</span>
										<a-form-item name="examine_count">
											<a-select placeholder="考核等次" v-model:value="formState.examine_level" class="custom-select">
												<a-select-option value="1">优秀</a-select-option>
												<a-select-option value="2">称职</a-select-option>
												<a-select-option value="4">基本称职</a-select-option>
												<a-select-option value="6">不称职</a-select-option>
											</a-select>
										</a-form-item>
									</div>
								</PocFormItem>
								<PocFormItem name="on_job_education" label="基层年限" :wrapperCol="{ span: 24 }">
									<div class="form-common-style" style="width: 100%">
										<a-form-item name="base_year_start" style="width: 50%" :wrapperCol="{ span: 24 }">
											<a-input-number placeholder="请输入" style="width: 100%" v-model:value="formState.base_year_start" :min="0" />
										</a-form-item>
										<span class="line"></span>
										<a-form-item name="base_year_end" style="width: 50%" :wrapperCol="{ span: 24 }">
											<a-input-number placeholder="请输入" style="width: 100%" v-model:value="formState.base_year_end" :min="0" />
										</a-form-item>
									</div>
								</PocFormItem>
							</div>
							<div class="flex-avg border-bottom">
								<PocFormItem name="full_time_school" label="干部指数排名" :wrapperCol="{ span: 20 }">
									<div class="cadre-index form-common-style">
										<a-form-item name="department_type">
											<a-select placeholder="请选择" v-model:value="formState.department_type" allow-clear class="custom-select">
												<a-select-option value="1">同序列</a-select-option>
												<a-select-option value="2">乡镇（部门）</a-select-option>
												<!-- <a-select-option value="3">本单位</a-select-option> -->
											</a-select>
										</a-form-item>
										<span class="text-style">排名</span>
										<a-form-item name="rank_start" style="width: 25%" :wrapperCol="{ span: 24 }">
											<a-input-number v-model:value="formState.rank_start" style="width: 100%" :min="0" />
										</a-form-item>
										<span class="text-style-percent">%</span>
										<span class="line"></span>
										<a-form-item name="rank_end" style="width: 25%" :wrapperCol="{ span: 24 }">
											<a-input-number v-model:value="formState.rank_end" style="width: 100%" :min="0" />
										</a-form-item>
										<span class="text-style-percent">%</span>
									</div>
								</PocFormItem>
								<PocFormItem name="source" label="干部来源" :wrapperCol="{ span: 24 }">
									<PocCheck :options="originOptions" v-model:value="formState.source" type="radio" />
								</PocFormItem>
							</div>
							<div class="flex-avg border-bottom">
								<PocFormItem name="relationship" label="社会关系" :wrapperCol="{ span: 24 }">
									<PocCheck :options="societyOptions" v-model:value="formState.relationship" type="radio" />
								</PocFormItem>
								<PocFormItem name="attention" label="班子回访评价" :wrapperCol="{ span: 24 }">
									<PocCheck :options="attentionOptions" v-model:value="formState.attention" type="radio" />
								</PocFormItem>
							</div>
							<div class="flex-avg border-bottom">
								<PocFormItem name="information" label="简历信息" :wrapperCol="{ span: 20 }">
									<div class="no-wrap-box">
										<SearchInput
											class="poc-input"
											v-model:value="formState.information"
											placeholder=""
											@search="fetchInfo($event, 1)"
											@change="updateInformation"
											:options="fetchState.data1"
											allow-clear
										/>
										<span class="text-style">(包含)</span>
									</div>
								</PocFormItem>
								<PocFormItem name="responsibilities" label="分管领域" :wrapperCol="{ span: 24 }">
									<div class="no-wrap-box">
										<SearchInput
											class="poc-input"
											v-model:value="formState.responsibilities"
											placeholder=""
											@search="fetchInfo($event, 2)"
											@change="updateResponsibilities"
											:options="fetchState.data2"
											allow-clear
										/>
										<span class="text-style">(包含)</span>
									</div>
								</PocFormItem>
							</div>
							<div class="flex-avg border-bottom">
								<PocFormItem name="speciality" label="熟悉领域和特长领域" :wrapperCol="{ span: 20 }">
									<div class="no-wrap-box">
										<SearchInput
											class="poc-input"
											v-model:value="formState.speciality"
											placeholder=""
											@search="fetchInfo($event, 3)"
											@change="updateSpeciality"
											:options="fetchState.data3"
											allow-clear
										/>

										<span class="text-style">(包含)</span>
									</div>
								</PocFormItem>
								<PocFormItem name="label" label="特征标签" :wrapperCol="{ span: 24 }">
									<div class="no-wrap-box">
										<SearchInput
											class="poc-input"
											v-model:value="formState.label"
											placeholder=""
											@search="fetchInfo($event, 4)"
											@change="updateLabel"
											:options="fetchState.data4"
											allow-clear
										/>
										<span class="text-style">(包含)</span>
									</div>
								</PocFormItem>
							</div>
						</div>
					</a-form>
				</div>
				<div class="button-box" v-if="!isSearch">
					<a-button @click="onReset">重置</a-button>
					<a-button type="primary" @click="onSearch">查询</a-button>
				</div>
			</div>
		</div>
	</div>
	<div class="search-result" v-if="isSearch">
		<Result :get-params="getParams" @close="onPageStatus" @update-total="handleUpdateTotal" />
	</div>
	<CadreChat @close="closeChatClick" v-if="showCadreChat" ref="aiChatRef" @update-form="handleUpdateForm" :total-elements="totalElements" />
</template>
<script lang="ts" setup>
import { getCode } from '@/apis/new-sand-table-exercise'
import { fetchInfoByType } from '@/apis/search'
import { debounce } from '@/utils/utils'
import CadreChat from '@/views/AiChat/cadreChat.vue'
import dayJs from 'dayjs'
import { nextTick, onMounted, reactive, ref } from 'vue'
import BaseInfoActivePng from '../image/base-info-active.png'
import BaseInfoPng from '../image/base-info.png'
import CategoryActivePng from '../image/category-active.png'
import CategoryPng from '../image/category.png'
import EducationInfoActivePng from '../image/education-info-active.png'
import EducationInfoPng from '../image/education-info.png'
import PositionInfoActivePng from '../image/position-info-active.png'
import PositionInfoPng from '../image/position-info.png'
import PocCheck from './components/check.vue'
import PocFormItem from './components/poc-form-item.vue'
import Result from './Result.vue'
interface FormState {
	work_resume_start: string
	train: number
	cadre_type: string
	sort_type: string
	name: string
	birthday: [string, string]
	birthday_start: string
	join_time_start: string
	join_time_end: string
	birthday_end: string
	ethic: [string, string]
	gender: [string, string]
	political: [string, string]
	join_time: [string, string]
	cadre_category: [string, string]
	identity: [string, string]
	full_time_education: [string, string]
	on_job_education: [string, string]
	full_time_school: [string, string]
	current_rank: [string, string]
	major: [string, string]
	profession_specialty: string
	technical_position: string
	current_job: string
	current_job_time_gte: string
	current_job_time_lte: string
	current_rank_time_gte: string
	current_rank_time_lte: string
	year_examine_start: string
	year_examine_end: string
	base_year_start: string
	base_year_end: string
	source: number
	information: string
	responsibilities: string
	speciality: string
	label: string
	examine_count: string
	examine_level: string
	relationship: number
	assign_start: string
	assign_end: string
	assign_type: string
	attention: number
}
const menuConfig = [
	{
		label: '基本信息',
		icon: BaseInfoPng,
		activeIcon: BaseInfoActivePng,
	},
	{
		label: '职务信息',
		icon: PositionInfoPng,
		activeIcon: PositionInfoActivePng,
	},
	{
		label: '教育信息',
		icon: EducationInfoPng,
		activeIcon: EducationInfoActivePng,
	},
	{
		label: '其他信息',
		icon: CategoryPng,
		activeIcon: CategoryActivePng,
	},
]

const genderOption = [
	{
		label: '男性',
		value: 1,
	},
	{
		label: '女性',
		value: 2,
	},
]

const nationOption = [
	{
		label: '汉族',
		value: 1,
	},
	{
		label: '少数民族',
		value: 2,
	},
]

const politicalOption = [
	{
		label: '中共党员',
		value: 1,
	},
	{
		label: '非党员',
		value: 2,
	},
]
const trainOption = [
	{
		label: '参加过培训',
		value: 1,
	},
	{
		label: '优秀学员',
		value: 2,
	},
]

const cadreTypeOption = [
	{
		label: '市管领导',
		value: 1,
	},
	{
		label: '县管正职',
		value: 2,
	},
	{
		label: '县管副职',
		value: 3,
	},
	{
		label: '乡镇正职',
		value: 200108,
	},
	{
		label: '乡镇副职',
		value: 200101,
	},
	{
		label: '部门正职',
		value: 200102,
	},
	{
		label: '部门副职',
		value: 200103,
	},
	{
		label: '企业正职',
		value: 200104,
	},
	{
		label: '企业副职',
		value: 200105,
	},
	{
		label: '街道正职',
		value: 200106,
	},
	{
		label: '街道副职',
		value: 200107,
	},
	// {
	// 	label: '中层干部',
	// 	value: 200109,
	// },
	{
		label: '乡镇中层',
		value: 4,
	},
	{
		label: '部门中层',
		value: 5,
	},
	{
		label: '企业中层',
		value: 6,
	},
]
const societyOptions = [
	{
		label: '有社会关系在体制内',
		value: 1,
	},
	{
		label: '夫妻双方都在体制内',
		value: 2,
	},
]
const attentionOptions = [
	{
		label: '重点关注',
		value: 1,
	},
]
const cadrePositionOption = [
	{ label: '正处', value: 200307 },
	{ label: '副处', value: 200301 },
	{ label: '保留副处', value: 200302 },
	{ label: '正科', value: 200303 },
	{ label: '保留正科', value: 200311 },
	{ label: '副科', value: 200309 },
]
const fullTimeEducationOption = [
	{ label: '研究生', value: 1 },
	{ label: '大学本科', value: 2 },
	{ label: '大学专科', value: 3 },
]

const originOptions = [
	{ label: '选调生', value: '1' },
	{ label: '村官', value: '2' },
	{ label: '五方面人才', value: '3' },
]
// 表单
const formRef = ref()
const currentPage = ref(1)
const activeIndex = ref(0)
const dataSource = ref<any>([])
const tableBoxRef = ref<any>(null)
const drawerVisible = ref<boolean>(false)
const isSearch = ref(false)
const pagination = reactive({
	showSizeChanger: false,
	showQuickJumper: true,
	showTotal: (total: number) => `共${total}条数据`,
} as any)
const formState = reactive<FormState>({
	gender: [],
	full_time_education: [1, 2],
	on_job_education: [],
	identity: [],
	age: undefined,
	birthday_start: '1980-01', // 默认开始时间为1985年1月
	birthday_end: '2022-12', // 默认结束时间为至今
	assign_type: '全部',
	cadre_category: [3],
	major: '旅游',
} as any)
// 联想查询
const fetchState = reactive({
	data1: [],
	data2: [],
	data3: [],
	data4: [],
	type: undefined,
	fetching: false,
})
// 滚动实例
const mescroll1 = ref()
// 总数据量
const totalElements = ref<number>(0)
//#region ====================== AI干部查询显示 ======================
const aiChatRef = ref<any>()
const showCadreChat = ref(false)
//是否显示聊天窗口
const showChatClick = () => {
	showCadreChat.value = true
}
const closeChatClick = () => {
	showCadreChat.value = false
}
// 处理总数更新
const handleUpdateTotal = (total: number) => {
	console.log('🚀 ~ total:', total)
	totalElements.value = total
}
//更新form表单的选项
const handleUpdateForm = (formData: any) => {
	// 先重置表单数据
	formState.full_time_education = []
	formState.age = undefined
	formState.birthday_start = ''
	formState.birthday_end = ''
	formState.assign_type = ''
	formState.cadre_category = []
	formState.major = ''
	// 然后更新新的表单数据
	Object.keys(formData).forEach((key) => {
		if (formData[key] !== undefined && formData[key] !== null) {
			formState[key] = formData[key]
		}
	})
	// 触发查询
	nextTick(() => {
		onSearch()
	})
}
//#endregion
const onSearch = async () => {
	isSearch.value = true // 设置为 true，隐藏按钮
	// 手动点击查询，排序
	onFinish({ scroll: true }) // 执行查询逻辑
}

const onReset = () => {
	formRef.value.resetFields() // 重置表单字段
	formState.full_time_education = []
	formState.age = undefined
	formState.birthday_start = ''
	formState.birthday_end = ''
	formState.assign_type = ''
	formState.cadre_category = []
	formState.major = ''
}
const is_lock = ref(false)
const clearBack = debounce(() => {
	is_lock.value = false
}, 500)
const codeOptionMap = reactive<any>({ cadreLevel: [] })
const onActive = (item: any, index: any) => {
	activeIndex.value = index
	const allTitle = document.querySelectorAll('[data-label]')
	const container = document.querySelector('.right-box')
	is_lock.value = true
	allTitle.forEach((el: any) => {
		if (el.dataset.label === item.label) {
			container?.scrollTo({
				top: el.offsetTop,
				behavior: 'smooth',
			})
			clearBack()
		}
	})
}
const initCode = async (code: number, key: string) => {
	const res = await getCode({ code })
	if (res.code === 0) {
		codeOptionMap[key] = res.data.map((code: any) => ({ label: code.op_value, value: code.op_key }))
	}
}
initCode(2002, 'cadreLevel')

const assignTypeOptions = ref([])
const initAssignType = async () => {
	const res = await getCode({ code: 10013 })
	if (res.code === 0) {
		assignTypeOptions.value = res.data
	}
}
initAssignType()

onMounted(() => {
	const currentDate = dayJs().format('YYYY-MM') // 获取当前日期并格式化为 YYYY-MM
	formState.birthday_end = currentDate // 设置默认结束时间
	const container: any = document.querySelector('.right-box')
	const allTitle = document.querySelectorAll('[data-label]')
	container?.addEventListener('scroll', (e) => {
		if (is_lock.value) {
			return
		}

		let index = 0

		allTitle.forEach((el: any, _index: any) => {
			if (container.scrollTop >= el.offsetTop) {
				index = _index
			}
		})

		activeIndex.value = index
	})
})

const onDataPickerChange = (status: any) => {
	if (status) {
		nextTick(() => {
			const el: any = document.querySelector('.ant-picker-year-btn')

			el?.click()
		})
	}
}
const fetchInfo = debounce(async (name: any, type: number) => {
	const keyMap: any = {
		1: 'information',
		2: 'responsibilities',
		3: 'speciality',
		4: 'label',
	}
	fetchState.type = type

	fetchState.fetching = true
	const res = await fetchInfoByType({
		name,
		type,
		cadre_type: formState.cadre_type,
	})

	if (res.code === 0) {
		const _data = res.data?.map((item: string) => ({ label: item, value: item }))

		// _data.unshift({ label: name, value: name })

		fetchState[`data${type}`] = _data
	}
	const key: string = keyMap[type]
	// updateDebounce(formState, key)
	fetchState.fetching = false
}, 300)
/**
 * @description: 数据加载
 * @param {*} page
 * @param {*} scroll
 * @return {*}
 */
const onFinish = async ({ scroll = true }: any) => {
	// 注意：数据加载现在由Result.vue组件自己处理
	// 这里只需要处理UI状态和滚动
	if (tableBoxRef.value && scroll) {
		// 获取距离顶部得距离
		nextTick(() => {
			const top = tableBoxRef.value?.offsetTop
			// 滚动到指定位置
			document.querySelector('.search-wrap')?.scrollTo({
				top,
				behavior: 'smooth',
			})
		})
	}
}

const getParams = () => {
	const params: any = { ...formState }
	if (formState.birthday) {
		params.birthday_start = formState.birthday[0]
		params.birthday_end = formState.birthday[1]
		delete params.birthday
	}
	if (formState.join_time) {
		params.join_time_start = formState.join_time[0]
		params.join_time_end = formState.join_time[1]
		delete params.join_time
	}
	if (formState.join_time) {
		params.join_time_start = formState.join_time[0]
		params.join_time_end = formState.join_time[1]
		delete params.join_time
	}
	if (formState.information) {
		params.information = formState.information
	}
	if (formState.responsibilities) {
		params.responsibilities = formState.responsibilities
	}
	if (formState.speciality) {
		params.speciality = formState.speciality
	}
	if (formState.label) {
		params.label = formState.label
	}

	if (!formState.sort_type && !formState.cadre_sort_type) {
		params.sort_type = 'ASC'
	}
	if (formState.assign_type === '全部') {
		delete params.assign_type
	}
	const _params: any = {}
	Object.entries(params).forEach(([key, value]) => {
		if (Array.isArray(value)) {
			if (value[0] !== undefined) {
				_params[key] = value
			}
			return false
		}
		_params[key] = value
	})

	return _params
}
const updateInformation = (value: any) => {
	formState.information = value
}
const updateResponsibilities = (value: any) => {
	formState.responsibilities = value
}
const updateSpeciality = (value: any) => {
	formState.speciality = value
}
const updateLabel = (value: any) => {
	formState.label = value
}
const onPageStatus = () => {
	isSearch.value = !isSearch.value
}
</script>

<style lang="scss" scoped>
.new-search {
	display: flex;
	flex-direction: column;
	height: 100vh;
	overflow: hidden;

	.ai-prompt {
		position: sticky;
		top: 0;
		z-index: 100;
		width: 100%;
		background: url('@/assets/images/ai-list/ai_search_bg.png') no-repeat;
		background-size: 100% 100%;
		padding: 16px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		&-flex {
			display: flex;
			align-items: center;
			max-width: 1300px;
			margin: 0 auto;

			.icon {
				width: 70px;
				height: 70px;
				margin-right: 12px;
			}

			.content {
				flex: 1;
				color: #000;
				font-size: 22px;
			}

			.poc-btn {
				margin-left: 16px;
			}
		}
	}

	.main-content {
		display: flex;
		height: calc(100vh - 220px);
		overflow-y: auto;
		.left-box {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 165px;
			height: 100%;
			overflow-y: auto;
			background: #ffffff;
			border-right: 1px solid rgba(0, 0, 0, 0.1);

			.menu-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				margin: 50px 0;

				cursor: pointer;
				.icon {
					height: 35px;
					width: 35px;
					margin-bottom: 11px;
				}
				.label {
					font-size: 24px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 500;
					color: #666666;
					line-height: 28px;
				}
			}
			.menu-item-active {
				.label {
					color: #028fff;
				}
			}
		}
		.right-box {
			padding: 23px 32px;
			height: 100%;
			flex: 1;
			overflow-y: auto;
			padding-bottom: 135px;
			background: #ffffff;
			.condition-box {
				min-height: calc(100% - 150px - 12px);
				.title {
					padding-left: 18px;
					display: flex;
					align-items: center;
					width: 100%;
					height: 62px;
					background: #e7f5fd;
					border-radius: 6px 6px 0px 0px;
					font-size: 24px;
					line-height: 62px;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: bold;
					color: rgba(0, 0, 0, 0.9);
				}

				.poc-input,
				:deep(.poc-input) {
					// width: 351px;
					height: 54px;
					font-size: 21px;
					background: #f7f8fa !important;
					input {
						background: transparent;
					}
				}
				:deep(.ant-input-number-input) {
					height: 54px;
					font-size: 21px;
					background: #f7f8fa;
				}
				.ant-picker {
					height: 54px;
					font-size: 21px;
					background: #f7f8fa;
					border: none;
					::v-deep(.ant-picker-input) {
						input {
							font-size: 21px;
						}
					}
				}
				.date-picker-width {
					.ant-picker {
						width: 168px;
					}
				}
			}
			.text-style {
				margin: 0px 12px;
				white-space: nowrap;
				font-size: 21px;
			}
			.form-common-style {
				display: flex;
				align-items: center;
				font-size: 21px;
				color: rgba(0, 0, 0, 0.85);
				justify-content: space-between;
				.ant-input-number {
					width: 138px;
				}
			}

			.no-wrap-box {
				display: flex;
				align-items: flex-end;
			}
			.cadre-index {
				.ant-select {
					width: 190px;
				}
				.text-style-percent {
					margin-left: 5px;
					font-size: 18px;
				}
				.line {
					margin: 0 10px;
				}
				.ant-input-number {
					width: 70px;
				}
			}
			.button-box {
				position: fixed;
				bottom: 0;
				left: 0;
				box-sizing: content-box;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 150px;
				margin-bottom: 0;
				background: #ffffff;
				border-top: 12px solid #f5f5f5;
				gap: 45px;
				z-index: 100;
				button {
					width: 195px;
					height: 66px;
					font-size: 24px;
				}
			}
			.date-box {
				width: 100%;
				display: flex;
				align-items: center;
				white-space: nowrap;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 21px;
				color: rgba(0, 0, 0, 0.85);
				line-height: 25px;
				.label-text {
					margin-right: 10px;
					font-size: 16px;
				}
			}
			.line {
				display: inline-block;
				flex-shrink: 0;
				width: 5px;
				height: 1px;
				background: rgba(0, 0, 0, 0.85);
				margin: 0 3px;
			}
		}
	}

	.flex-avg {
		display: flex;
		& > div {
			flex: 1 0 0%;
		}
	}
	.border-bottom {
		border-bottom: 1px dashed #ececec;
	}
	.custom-select {
		width: 180px;
		:deep(.ant-select-selector) {
			width: 100%;
			height: 54px;
			font-weight: 400;
			font-size: 21px;
			color: rgba(0, 0, 0, 0.85);
			line-height: 54px;
			background: #f7f8fa;
		}
		:deep(.ant-select-selection-placeholder) {
			line-height: 54px;
		}
		:deep(.ant-select-selection-item) {
			line-height: 54px;
		}
	}
}
.search-result {
	position: absolute;
	inset: 0;
	background: #f5f5f5;
}
</style>
<style lang="less">
.search-modal {
	margin-top: 24px;
	padding: 24px;
	width: 100%;
	.ant-checkbox-wrapper {
		font-size: 20px;
	}
	button {
		font-size: 19px;
		height: 40px;
		width: 80px;
	}
}
</style>
