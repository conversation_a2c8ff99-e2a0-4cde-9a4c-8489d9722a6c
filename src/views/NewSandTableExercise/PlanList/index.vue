<template>
	<div class="page">
		<HeaderBack title="所有方案" />
		<div class="page_content">
			<a-button type="primary" class="add" @click="openFormModel('add')">+新建</a-button>
			<a-table class="page_content_table" :dataSource="dataSource" :columns="columns" bordered :loading="tableLoading" :pagination="false">
				<template #bodyCell="{ text, column, record }">
					<template v-if="column.key === 'handler'">
						<div v-if="[0, 1, 2].includes(record.status)" class="operation">
							<a-button v-if="[0, 1].includes(record.status)" type="link" @click="copyPlanHandler(record)" :loading="curCopyId === record.mock_id"
								>复制方案</a-button
							>
							<a-button v-if="[0, 1].includes(record.status)" type="link" @click="toHome(record)">修改方案</a-button>
							<a-button
								v-if="[0, 1].includes(record.status)"
								@click="onDeletePlan(record)"
								:disabled="record.mock_id === $route.query.mock_id"
								type="link"
								:style="{ color: record.mock_id == $route.query.mock_id ? '#ccc' : '' }"
								>删除方案</a-button
							>
							<a-button type="link" @click="toAdjust('list', record)">调整名单</a-button>
							<!-- <a-button v-if="record.status === 0" @click="planStatusToggle(record)" type="link" :loading="curStatusId === record.mock_id"
								>确定为上会方案</a-button
							>
							<a-dropdown v-if="[0, 1].includes(record.status)" type="link" :trigger="['click']">
								<a-button type="link" class="ant-dropdown-link" @click.prevent>
									输出
									<DownOutlined />
								</a-button>
								<template #overlay>
									<a-menu>
										<a-menu-item v-for="item in dropList" :key="item.key" @click="onWordView(record.mock_id, item.key)">
											<a>{{ item.label }}</a>
										</a-menu-item>
									</a-menu>
								</template>
							</a-dropdown>
							<a-button v-else-if="record.status === 1" @click="openFinalModal(record)" type="link">确定为最终方案</a-button> -->
						</div>
					</template>
					<template v-else-if="column.key === 'mock_name'">
						<div class="page_content_table_name">
							<span>{{ text }}</span>
							<img @click="openFormModel('edit', record)" src="../../../assets/images/plan_list/edit.png" alt="" srcset="" />
						</div>
					</template>
					<template v-else-if="column.key === 'status'">
						<span class="page_content_table_status">{{ statusMap[text] }}</span>
					</template>
				</template></a-table
			>
		</div>
		<a-modal
			v-model:visible="planModal.visible"
			:title="planModal.title"
			width="300px"
			destroyOnClose
			:confirmLoading="planModal.loading"
			@ok="submitHandler"
			@cancel="cancelHandler"
			class="add_plan_modal"
		>
			<a-form ref="formRef" :model="planModal.formData" name="basic" :label-col="{ span: 4 }" autocomplete="off">
				<a-form-item label="方案名称" name="mock_name" :rules="[{ required: true, message: '请输入方案名称' }]">
					<a-input v-model:value.trim="planModal.formData.mock_name" showCount :maxlength="100" />
				</a-form-item>
			</a-form>
		</a-modal>
		<a-modal
			v-model:visible="submitFinalPlanModal.visible"
			:title="submitFinalPlanModal.title"
			width="300px"
			destroyOnClose
			:confirmLoading="submitFinalPlanModal.loading"
			@ok="submitFinalHandler"
			@cancel="cancelHandler"
		>
			<div class="submiy_final_modal">
				<img class="submiy_final_modal_img" src="../../../assets/images/plan_list/warning.png" alt="warning" />
				<div class="submiy_final_modal_title">
					<span v-if="submitFinalPlanModal.errorCodeList.length === 0">方案名称：{{ submitFinalPlanModal.mock_name }}</span>
					<span v-else>提交失败</span>
				</div>
				<div v-if="submitFinalPlanModal.errorCodeList.length === 0" class="submiy_final_modal_tip">
					此操作会根据方案中的干部调整名单，对干部进行岗位调整，同时更新干部的职务信息、简历信息，请确认！
				</div>
				<div v-for="item in submitFinalPlanModal.errorCodeList" :key="item" class="error_code_item">
					<span>{{ errorCodeMap[item] }}<img src="../../../assets/images/plan_list/right_icon.png" /></span>
					<a-button @click="toAdjust('modal', item)" type="link">{{ item === 1 ? '待配名单' : '调整名单' }}</a-button>
				</div>
			</div>
		</a-modal>
	</div>
</template>
<script lang="ts">
export default {
	name: 'newSandTableExercisePlanList',
}
</script>
<script setup lang="ts">
import { message, Modal } from 'ant-design-vue'
import HeaderBack from '../components/HeaderBack.vue'
import { ref, reactive, inject } from 'vue'
import { getPlanList, addPlan, editPlan, copyPlan, submitUpMeetPlan, submitFinalPlan, deleteMock } from '@/apis/new-sand-table-exercise'
import { useRouter, useRoute } from 'vue-router'
import { DownOutlined } from '@ant-design/icons-vue'

const route = useRoute()
const router = useRouter()

const pageApi: any = inject('pageApi')

const columns = [
	{
		title: '方案名称',
		dataIndex: 'mock_name',
		key: 'mock_name',
		align: 'center',
	},
	{
		title: '更新时间',
		dataIndex: 'date',
		key: 'date',
		align: 'center',
	},
	{
		title: '方案阶段',
		dataIndex: 'status',
		key: 'status',
		align: 'center',
	},
	{
		title: '操作',
		dataIndex: 'handler',
		key: 'handler',
		width: 550,
		align: 'center',
	},
]
interface StatusMapType {
	[key: string]: any
}
const statusMap: StatusMapType = {
	'-1': '已作废',
	'0': '动议方案',
	'1': '上会方案',
	'2': '最终方案',
}
const errorCodeMap: StatusMapType = {
	'1': '还有干部未安排职务',
	'2': '调整名单中存在一人对应多个拟任职务',
	'3': '调整名单中存在一个职务对应多个候选人',
}

const dropList = [
	{
		label: '研判名单',
		key: 1,
	},
	{
		label: '干部人事酝酿名单（书记专题会）',
		key: 2,
	},
	{
		label: '部务会/常委会表决票',
		key: 3,
	},
	{
		label: '干部人事任免说明',
		key: 4,
	},
	{
		label: '提拔交流统计表',
		key: 5,
	},
	// {
	// 	label: '重要干部任免征求县委委员意见表',
	// 	key: 6,
	// },
]

const dataSource = ref([])

const tableLoading = ref(true)
const getPlanListhandler = async () => {
	try {
		const { code, data, message: msg } = await getPlanList()
		tableLoading.value = false
		if (code === 0) {
			dataSource.value = data
		} else {
			message.error(msg)
		}
	} catch (error: any) {
		tableLoading.value = false
		message.error(error.message)
	}
}
getPlanListhandler()

// 状态切换
const curStatusId = ref()
const planStatusToggle = async (record: any) => {
	try {
		const { mock_id } = record
		curStatusId.value = mock_id
		const { code, message: msg, data } = await submitUpMeetPlan({ mock_id })
		if (code === 0) {
			getPlanListhandler()
			curStatusId.value = null
			message.success(data)
		} else {
			message.error(msg)
		}
	} catch (error: any) {
		message.error(error.message)
		curStatusId.value = null
	}
}

// 新增编辑弹窗
const planModal = reactive({
	type: 'add',
	title: '新增方案',
	visible: false,
	loading: false,
	formData: {
		mock_name: '',
	},
})
// 打开弹窗
const openFormModel = (type: string, row?: any) => {
	planModal.type = type
	planModal.visible = true
	if (type === 'edit') {
		planModal.title = '编辑方案'
		planModal.formData = { ...row }
	} else {
		planModal.title = '新增方案'
	}
}

const formRef = ref()
// 方案新增或者编辑
const submitHandler = async () => {
	try {
		await formRef.value.validate()
		planModal.loading = true
		const API = planModal.type === 'add' ? addPlan : editPlan
		const { code, message: msg, data } = await API(planModal.formData)
		planModal.loading = false
		if (code === 0) {
			message.success(data)
			getPlanListhandler()
			cancelHandler()
		} else {
			message.error(msg)
		}
	} catch (err: any) {
		message.error(err.message)
		planModal.loading = false
	}
}
const onWordView = (mock_id: any, type: any) => {
	router.push({
		path: '/word-preview',
		query: {
			mock_id,
			type,
		},
	})
}
// 关闭弹窗
const cancelHandler = async () => {
	planModal.visible = false
	formRef.value.resetFields()
}

// 复制方案
const curCopyId = ref()
const copyPlanHandler = async (record: any) => {
	try {
		curCopyId.value = record.mock_id
		const { code, data, message: msg } = await copyPlan({ mock_id: record.mock_id })
		curCopyId.value = null
		tableLoading.value = false
		if (code === 0) {
			message.success(data)
			getPlanListhandler()
		} else {
			message.error(msg)
		}
	} catch (error: any) {
		curCopyId.value = null
		message.error(error.message)
	}
}

// 去调整名单页面
const toAdjust = (type: string, record: any) => {
	// 从列表点击
	if (type === 'list') {
		router.push({
			path: '/new-sand-table-exercise/adjust-list',
			query: {
				mock_name: record.mock_name,
				mock_id: record.mock_id,
				status: record.status,
			},
		})
		// 从最终方案弹窗里面点击
	} else {
		// 去待配页面
		if (record === 1) {
			router.push({
				path: '/new-sand-table-exercise/wait-list',
			})
			// 去调整名单
		} else {
			router.push({
				path: '/new-sand-table-exercise/adjust-list',
				query: {
					mock_name: submitFinalPlanModal.mock_name,
					mock_id: submitFinalPlanModal.mock_id,
					activeKey: record === 2 ? 'user' : 'job',
				},
			})
		}
	}
}

// 去首页
const toHome = (record: any) => {
	router.push({
		path: '/new-sand-table-exercise/home',
		query: {
			mock_id: record.mock_id,
		},
	})
}

const onDeletePlan = (plan: any) => {
	// 当前方案不允许删除
	if (plan.mock_id == route.query.mock_id) {
		return
	}
	Modal.confirm({
		title: '提示',
		content: '确认删除？',
		okText: '确认',
		okType: 'danger',
		cancelText: '取消',
		maskClosable: true,
		wrapClassName: 'plan-list-delete',
		onOk() {
			deleteMock({
				mock_id: plan.mock_id,
			}).then((res: any) => {
				if (res.code === 0) {
					console.log(plan.mock_id, route.query.mock_id)
					if (plan.mock_id == route.query.mock_id) {
						pageApi?.routerClearCache()
					}
					getPlanListhandler()
				}
			})
		},
		onCancel() {
			console.log('Cancel')
		},
	})
}

// 最终方案弹窗
const submitFinalPlanModal = reactive({
	visible: false,
	title: '确定最终方案',
	loading: false,
	mock_name: '',
	mock_id: '',
	errorCodeList: [],
})
const openFinalModal = (record: any) => {
	message.warning('功能开发中...')
	return
	const { mock_id, mock_name } = record
	submitFinalPlanModal.visible = true
	submitFinalPlanModal.mock_id = mock_id
	submitFinalPlanModal.mock_name = mock_name
	submitFinalPlanModal.errorCodeList = []
}
// 最终方案弹窗确定按钮
const submitFinalHandler = async () => {
	if (submitFinalPlanModal.errorCodeList.length > 0) {
		submitFinalPlanModal.visible = false
		return
	}
	submitFinalPlanModal.loading = true
	try {
		const { code, data, message: msg } = await submitFinalPlan({ mock_id: submitFinalPlanModal.mock_id })
		if (code === 0) {
			submitFinalPlanModal.errorCodeList = data
			if (data.length === 0) {
				message.success('最终方案确定成功！')
				submitFinalPlanModal.visible = false
				getPlanListhandler()
			}
			submitFinalPlanModal.loading = false
		} else {
			message.error(msg)
			submitFinalPlanModal.loading = false
		}
	} catch (error: any) {
		message.error(error.message)
		submitFinalPlanModal.loading = false
	}
}
</script>

<style lang="less" scoped>
.page {
	display: flex;
	flex-direction: column;
	height: 100%;
	&_content {
		flex: 1;
		overflow: auto;
		padding: 16px;
		.add {
			width: 144px;
			height: 60px;
			font-weight: 500;
			font-size: 26px;
			color: #ffffff;
		}
		.operation {
			button {
				font-size: 26px;
			}
		}
		:deep(.ant-table-cell) {
			font-weight: bold;
			font-size: 26px;
			color: #3d3d3d;
		}
		&_table {
			margin-top: 16px;
			&_name {
				display: flex;
				justify-content: space-between;
				align-items: center;
				img {
					width: 27px;
					height: 27px;
					cursor: pointer;
				}
			}
		}
	}
}
.submiy_final_modal {
	text-align: center;
	&_img {
		width: 100px;
		height: 100px;
	}
	&_title {
		margin: 12px 0;
		font-weight: 500;
		font-size: 18px;
		color: rgba(0, 0, 0, 0.9);
	}
	&_tip {
		font-weight: 400;
		font-size: 16px;
		color: rgba(0, 0, 0, 0.65);
	}
	.error_code_item {
		padding: 12px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: rgba(0, 142, 255, 0.04);
		font-weight: 400;
		font-size: 16px;
		color: rgba(0, 0, 0, 0.85);
		span {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: space-between;
			img {
				width: 18px;
				height: 18px;
			}
		}
	}
}
</style>

<style lang="less">
.plan-list-delete {
	.ant-modal-content {
		.ant-modal-confirm-body {
			svg {
				width: 30px;
				height: 30px;
			}
			.ant-modal-confirm-title {
				font-size: 30px !important;
				line-height: 30px !important;
			}
			.ant-modal-confirm-content {
				font-size: 25px !important;
				line-height: 25px !important;
			}
		}
		button {
			padding: 10px 0px;
			width: 100px;
			height: unset;
			font-size: 18px;
			line-height: 18px;
		}
	}
}
.add_plan_modal {
	.ant-modal-content {
		width: 733px;
		.ant-modal-title {
			padding: 10px;
			font-size: 27px;
		}
		.ant-modal-body {
			padding-top: 50px;
			height: 429px;
		}
	}
	.ant-input-affix-wrapper {
		height: 54px;
		input {
			font-size: 23px;
		}
	}
	.ant-form-item-label {
		display: flex;
		align-items: center;
	}
	.ant-form-item-required {
		font-size: 23px;
	}
	.ant-modal-footer {
		padding: 20px;
		display: flex;
		justify-content: center;
		.ant-btn {
			width: 255px;
			height: 60px;
			font-size: 21px;
		}
	}
}
</style>
