<template>
	<div class="search">
		<a-form ref="formRef" :model="formState">
			<div class="flex-avg border-bottom base-form">
				<PocFormItem class="custom-poc__form" name="user_name" label="姓名" :wrapperCol="{ span: 23 }">
					<a-input class="poc-input" type="text" placeholder="请输入" v-model:value="formState.user_name" />
				</PocFormItem>

				<PocFormItem class="custom-poc__form" name="information" label="简历信息" :wrapperCol="{ span: 23 }">
					<a-input class="poc-input" type="text" placeholder="请输入" v-model:value="formState.information" />
				</PocFormItem>
			</div>
			<div class="flex-avg border-bottom age-form">
				<PocFormItem label="年龄" :wrapperCol="{ span: 24 }">
					<div class="flex-avg">
						<a-form-item name="age">
							<PocCheck :options="ageOption" v-model:value="formState.age" />
						</a-form-item>
						<div class="age-box">
							<a-form-item name="age_start">
								<a-input class="poc-input" type="number" placeholder="请输入" v-model:value="formState.age_start" />
							</a-form-item>
							<span class="label">岁</span>
							<span class="label">-</span>
							<a-form-item name="age_end">
								<a-input class="poc-input" type="number" placeholder="请输入" v-model:value="formState.age_end" />
							</a-form-item>
							<span class="label">岁</span>
						</div>
					</div>
				</PocFormItem>
			</div>
			<div class="flex-avg border-bottom edu-form">
				<PocFormItem label="学历" :wrapperCol="{ span: 24 }">
					<div class="flex-avg">
						<a-form-item name="full_time_education">
							<PocCheck :options="eduOption" v-model:value="formState.full_time_education" />
						</a-form-item>
						<div class="educa-box">
							<div class="label">专业:</div>
							<a-form-item name="major">
								<a-input class="poc-input" type="text" placeholder="请输入" v-model:value="formState.major" />
							</a-form-item>
						</div>
					</div>
				</PocFormItem>
			</div>
			<div class="flex-avg border-bottom exp-form">
				<PocFormItem label="经历" :wrapperCol="{ span: 24 }" name="basic">
					<PocCheck :options="expOption" v-model:value="formState.basic" />
				</PocFormItem>
			</div>
			<div class="flex-avg border-bottom exp-form">
				<PocFormItem label="干部类别" name="user_type" :wrapperCol="{ span: 24 }">
					<PocCheck :options="cadreTypeOption" v-model:value="formState.user_type" />
				</PocFormItem>
			</div>
			<div class="flex-avg border-bottom exp-form">
				<PocFormItem label="干部来源" :wrapperCol="{ span: 24 }" name="user_source">
					<PocCheck :options="originOptions" v-model:value="formState.user_source" />
				</PocFormItem>
			</div>
			<div class="flex-avg border-bottom position-form">
				<PocFormItem label="职务情况" :wrapperCol="{ span: 24 }" name="job">
					<PocCheck :options="positionOptions" v-model:value="formState.job" />
				</PocFormItem>
			</div>
			<div class="flex-avg border-bottom position-form">
				<PocFormItem label="干部指数" :wrapperCol="{ span: 24 }" name="cadre_index_rank">
					<PocCheck :options="indexOptions" v-model:value="formState.cadre_index_rank" />
				</PocFormItem>
			</div>
			<div class="flex-avg border-bottom position-form">
				<PocFormItem label="民主测评" :wrapperCol="{ span: 24 }" name="eval_index_rank">
					<PocCheck :options="appraisalOptions" v-model:value="formState.eval_index_rank" />
				</PocFormItem>
			</div>
			<div class="flex-avg other-form">
				<PocFormItem label="其他" :wrapperCol="{ span: 24 }">
					<div class="flex-avg">
						<a-form-item name="other">
							<PocCheck :options="otherOptions" v-model:value="formState.other" />
						</a-form-item>
						<div class="other-box">
							<div class="label">擅长领域:</div>
							<a-form-item :wrapperCol="{ span: 24 }" name="speciality">
								<a-input class="poc-input" type="text" placeholder="请输入" v-model:value="formState.speciality" />
							</a-form-item>
						</div>
					</div>
				</PocFormItem>
			</div>
		</a-form>
		<div class="button-box">
			<a-button @click="onReset">重置</a-button>
			<a-button @click="onSearch" type="primary">查询</a-button>
		</div>
	</div>
	<Result v-if="isSearch" @searchChange="onSearchChange" :formState="formState" v-bind="props" />
</template>

<script lang="ts" setup>
import { ref, PropType } from 'vue'
import PocFormItem from './components/poc-form-item.vue'
import PocCheck from '../components/check.vue'
import Result from './components/Result.vue'

const props = defineProps({
	onRouter: {
		default: () => void 0,
		type: Function,
	},
	onRowClick: {
		type: Function as PropType<() => void>,
	},
})

const formState = ref<any>({
	age: undefined,
})

const ageOption = [
	{ label: '80后', value: 1 },
	{ label: '85后', value: 2 },
	{ label: '90后', value: 3 },
	{ label: '95后', value: 4 },
]

const eduOption = [
	{
		label: '专科及以上',
		value: 1,
	},
	{
		label: '大学及以上',
		value: 2,
	},
]

const expOption = [
	{
		label: '乡镇工作经历3年以上',
		value: 1,
	},
	{
		label: '乡镇领导经历2年以上',
		value: 2,
	},
	{
		label: '基层年限2年以上',
		value: 3,
	},
]

const cadreTypeOption = [
	// {
	// 	label: '市管领导',
	// 	value: 1,
	// },
	// {
	// 	label: '县管正职',
	// 	value: 2,
	// },
	// {
	// 	label: '县管副职',
	// 	value: 3,
	// },
	{
		label: '乡镇正职',
		value: 1,
	},
	{
		label: '乡镇副职',
		value: 2,
	},
	{
		label: '部门正职',
		value: 3,
	},
	{
		label: '部门副职',
		value: 4,
	},
	{
		label: '企业正职',
		value: 5,
	},
	{
		label: '企业副职',
		value: 6,
	},
	{
		label: '中层干部',
		value: 7,
	},
	// {
	// 	label: '街道副职',
	// 	value: 200107,
	// },
]

const originOptions = [
	{ label: '选调生', value: '1' },
	{ label: '村官', value: '2' },
	{ label: '五方面人才', value: '3' },
]
const positionOptions = [
	{ label: '现职级5年以上', value: '1' },
	{ label: '现职务5年以上', value: '2' },
	{ label: '同一单位任职10年以上', value: '3' },
]

const indexOptions = [
	{ label: '班子内排名前3', value: '3' },
	{ label: '同序列排名前30%', value: '30' },
]
const appraisalOptions = [
	{ label: '班子内排名前3', value: '3' },
	{ label: '同序列排名前30%', value: '30' },
]
const otherOptions = [
	{ label: '女干部', value: '1' },
	{ label: '少数民族', value: '2' },
	{ label: '中共党员', value: '3' },
	{ label: '非中共党员', value: '4' },
]
const formRef = ref()

const isSearch = ref(false)

const onReset = () => {
	formRef.value.resetFields()
}

const onSearch = () => {
	const params: any = {}

	Object.entries(formState.value).forEach(([key, value]) => {
		if (Array.isArray(value)) {
			if (value[0] !== undefined) {
				params[key] = value
			}
			return false
		}
		params[key] = value
	})
	const { cadre_index_rank, eval_index_rank } = params
	console.log('🚀 ~ onSearch ~ cadre_index_rank, eval_index_rank:', cadre_index_rank, eval_index_rank)

	if (cadre_index_rank?.[0]) {
		const index0 = cadre_index_rank[0]
		const index1 = cadre_index_rank[1]
		// 第一个值大于30则为百分比, 说明只选中了第二个
		if (index0 == 30) {
			params.cadre_index_rank_percent = index0
		} else {
			params._cadre_index_rank = index0
			params.cadre_index_rank_percent = index1
		}

		// 第二个值小于30则为数字
	}
	if (eval_index_rank?.[0]) {
		const index0 = eval_index_rank[0]
		const index1 = eval_index_rank[1]
		// 第一个值大于30则为百分比, 说明只选中了第二个
		if (index0 == 30) {
			params.eval_index_rank_percent = index0
		} else {
			params._eval_index_rank = index0
			params.eval_index_rank_percent = index1
		}
	}
	formState.value = params

	isSearch.value = true
}

const onSearchChange = () => {
	isSearch.value = false
}
</script>

<style lang="less" scoped>
.search {
	padding: 0px 47px;
	width: 100%;
	height: 100%;
	overflow: auto;
}
.poc-input,
:deep(.poc-input) {
	// width: 351px;
	height: 54px;
	font-size: 21px;
	background: #f7f8fa !important;
	border: none;
	input {
		background: transparent;
	}
}
.flex-avg {
	display: flex;
}
.border-bottom {
	border-bottom: 1px dashed rgba(0, 0, 0, 0.15);
}

.base-form {
	.custom-poc__form {
		width: 30%;
	}
}
.age-form {
	width: 100%;
	.age-box {
		margin-left: 15px;
		display: flex;
		align-items: center;
		.label {
			margin: 0px 10px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 24px;
			color: rgba(0, 0, 0, 0.85);
			line-height: 28px;
		}
		.poc-input {
			width: 131px;
		}
	}
}
.edu-form {
	.educa-box {
		margin-left: 150px;
		display: flex;
		align-items: center;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		font-size: 24px;
		color: rgba(0, 0, 0, 0.85);
		.label {
			margin-right: 5px;
			font-weight: 500;
			font-size: 24px;
			color: rgba(0, 0, 0, 0.85);
			line-height: 28px;
		}
	}
}
.other-form {
	.other-box {
		margin-left: 15px;
		display: flex;
		align-items: center;
		.label {
			margin: 0px 10px;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 24px;
			color: rgba(0, 0, 0, 0.85);
			line-height: 28px;
		}
		.poc-input {
			width: 293px;
		}
	}
}

.button-box {
	gap: 0px 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 127px;
	background: #ffffff;
	border-top: 12px solid #f7f8fa;
	button {
		width: 195px;
		height: 66px;
		font-size: 24px;
	}
}
</style>
