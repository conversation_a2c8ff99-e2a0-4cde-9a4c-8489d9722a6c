<template>
	<div class="structure-abnormal">
		<HeaderBack title="班子结构异常" />
		<div class="content">
			<div class="menu-box">
				<div
					class="check-item"
					:class="{
						active: selectIndex == item.key,
					}"
					v-for="item in menuConfig"
					:key="item.key"
					@click="onActive(item.key)"
				>
					{{ item.label }}: {{ item.value }}
				</div>
			</div>
			<div class="menu-box">
				<div
					class="check-item"
					:class="{
						'icon-check-item': index !== 0,
						active: selectIndex1 == item.key,
					}"
					v-for="(item, index) in menuConfig1"
					:key="item.key"
					@click="onActive1(item.key)"
				>
					<template v-if="index === 0">
						{{ item.label }}:
						{{ item.value }}
					</template>
					<template v-else>
						<svg t="1697450179918" class="w-39 m-right-18 icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4025">
							<path
								d="M175.726 934.787v-316.26c0-189.18 153.404-342.612 342.774-342.612s342.775 153.433 342.775 342.612v316.26h117.012c24.3 0 44 19.7 44 44s-19.7 44-44 44H44.747c-24.301 0-44-19.7-44-44s19.699-44 44-44h130.98z m367.588-520.804L374.237 692.332h135.221l-33.855 208.762L644.68 622.745H509.457l33.856-208.762h0.001z m259.29-305.76c15.875 9.237 21.299 29.622 12.156 45.488l-60.778 105.636-57.464-33.238 60.78-105.636c9.04-15.865 29.333-21.287 45.106-12.25h0.2zM518.4 30c19.892 0 35.966 14.962 35.966 33.539v119.693h-71.931V63.439C482.434 44.963 498.508 30 518.4 30h-0.001z m-284.003 78.223c15.773-9.138 36.065-3.716 45.208 12.05 0 0 0 0.1 0.1 0.1l60.78 105.636-57.465 33.237-60.78-105.636c-9.14-15.866-3.716-36.15 12.156-45.387h0.001zM26.44 316.985c9.041-15.867 29.334-21.39 45.208-12.252 0 0 0.1 0 0.1 0.101l105.283 61.052-33.152 57.638L38.595 362.37c-15.872-9.137-21.298-29.522-12.155-45.387z m984.12 0c9.143 15.864 3.717 36.249-12.155 45.486L893.12 423.423l-33.152-57.637 105.283-61.053c15.773-9.137 36.065-3.715 45.208 12.05 0 0.1 0.1 0.1 0.1 0.2v0.002z"
								:fill="cardColor[item.key]"
								p-id="4026"
							></path>
						</svg>
						{{ item.value }}
					</template>
				</div>
			</div>
			<div class="org-box" @scroll="onScroll">
				<!-- <div class="left-box">
					<div class="org-item" v-for="(item, index) in data.warning_list" :key="index">
						<img src="../images/tree-icon.png" class="org-icon" />
						<span class="org-name">{{ item.short_name }}</span>
					</div>
				</div>
				<div class="right-box">
					<div class="org-item" v-for="(item, index) in data.warning_list" :key="index">
						<div class="warn-box">
							<div class="warn-item" v-for="(text, index) in item.warning_details" :key="index">
								<img src="../images/warn-icon.png" alt="" /> <span class="warn-text">{{ text }}</span>
							</div>
						</div>
						<div class="right-icon" @click="onToOrgTeam(item.org_id)"></div>
					</div>
				</div> -->
				<div class="org-item" v-for="(item, index) in data.warning_list" :key="index">
					<div class="left-box">
						<img src="../images/tree-icon.png" class="org-icon" />
						<span class="org-name">{{ item.short_name }}</span>
					</div>
					<div class="right-box" @click="onToOrgTeam(item.org_id)">
						<div class="warn-box">
							<div class="warn-item" v-for="(text, index) in item.warning_details" :key="index">
								<img src="../images/warn-icon.png" alt="" /> <span class="warn-text">{{ text }}</span>
							</div>
						</div>
						<div class="right-icon"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script lang="ts">
export default {
	name: 'newSandTableExerciseStructureAbnormal',
}
</script>
<script lang="ts" setup>
import { ref, computed, watchEffect, unref, nextTick, inject, onActivated } from 'vue'
import HeaderBack from '../components/HeaderBack.vue'
import { teamStructureList } from '@/apis/new-sand-table-exercise'
import { useRouter, useRoute } from 'vue-router'
// 10290405：乡镇街道，10290402：事业，10290403：参公，10290404：国企，10290401：行政

const pageApi: any = inject('pageApi')

const data = ref({
	head: {
		career: 0,
		villages: 0,
		stateOwnedEnterprise: 0,
		yellow: 0,
		orange: 0,
		red: 0,
		administractive: 0,
		total: 0,
		structuresTotal: 0,
	},
	warning_list: [],
})

const menuConfig = computed(() => {
	const _data = unref(data)
	return [
		{
			label: '全部',
			value: _data.head.total,
		},
		{
			label: '行政(参公)',
			key: '10290403',
			value: _data.head.administractive,
		},
		// {
		// 	label: '行政',
		// 	key: 10290401,
		// 	value: _data.head.administractive,
		// },
		{
			label: '事业',
			key: '10290402',
			value: _data.head.career,
		},
		{
			label: '国企',
			key: '10290404',
			value: _data.head.stateOwnedEnterprise,
		},
		{
			label: '乡镇（街道）',
			key: '10290405',
			value: _data.head.villages,
		},
	]
})
const menuConfig1 = computed(() => {
	const _data = unref(data)
	return [
		{
			label: '全部',
			value: _data.head.structuresTotal,
		},
		{
			key: 1,
			value: _data.head.orange,
		},
		{
			key: 2,
			value: _data.head.yellow,
		},
		{
			key: 3,
			value: _data.head.red,
		},
	]
})

const route = useRoute()
const router = useRouter()

const cardColor = ['#60ca71', '#f6dd00', '#ffa300', '#ff473e']

const selectIndex = ref()
const selectIndex1 = ref()

const onActive = (key: any) => {
	selectIndex.value = key
}

const onActive1 = (key: any) => {
	selectIndex1.value = key
}

const loadData = async () => {
	const institutionNatures = []

	const orgStructures = []
	if (selectIndex1.value) {
		orgStructures.push(selectIndex1.value)
	}

	if (selectIndex.value) {
		if (selectIndex.value == '10290403') {
			institutionNatures.push(selectIndex.value, '10290401')
		} else {
			institutionNatures.push(selectIndex.value)
		}
	}

	const res = await teamStructureList({
		institutionNatures,
		orgStructures,
	})

	if (res.code === 0) {
		data.value = res.data

		// nextTick(() => {
		// 	const leftBox: any = document.querySelector('.org-box .left-box')
		// 	const rightBox: any = document.querySelector('.org-box .right-box')
		// 	Array.from(rightBox.children)?.forEach((el: any, index: any) => {
		// 		leftBox.children[index].style.height = el.clientHeight + 1 + 'px'
		// 	})
		// })
	}
}

const onToOrgTeam = (org_id: any) => {
	pageApi.routerPushCache('newSandTableExerciseStructureAbnormal')

	router.push({
		path: `/new-sand-table-exercise/org-team`,
		query: {
			org_id,
			from: 'candidate',
			...route.query,
		},
	})
}

const scrollTop = ref(0)

const onScroll = (e: any) => {
	console.log('🚀 ~ onScroll ~ e:', e.target.scrollTop)
	scrollTop.value = e.target.scrollTop
}

watchEffect(() => {
	loadData()
})

onActivated(() => {
	pageApi?.routerRemoveCache('newSandTableExerciseStructureAbnormal')

	loadData()

	nextTick(() => {
		document.querySelector('.org-box')?.scrollTo({
			top: scrollTop.value,
		})
	})
})
</script>

<style lang="less" scoped>
.flex {
	display: flex;
}

.align-items-center {
	.flex;
	align-items: center;
}
.structure-abnormal {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.content {
		flex: 1;
		display: flex;
		flex-direction: column;
		overflow: hidden;
		.menu-box {
			display: flex;
			padding: 24px 0px 24px 16px;
			gap: 0px 15px;
			background-color: #ffffff;
			border-top: 2px solid rgba(0, 0, 0, 0.06);
			.check-item {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 11px 25px;
				min-width: 173px;
				font-weight: 500;
				font-size: 26px;
				color: #000000;
				line-height: 30px;
				border-radius: 3px;
				text-align: center;
				background: #f5f5f5;
			}
			.icon-check-item {
				.align-items-center;
				padding: 11px 29px;
				justify-content: space-between;
			}
			.active {
				background: #008eff;
				color: #ffffff;
			}
		}

		.org-box {
			margin-top: 18px;
			display: flex;
			flex-direction: column;
			flex: 1;
			overflow: auto;
			.org-item {
				display: flex;
				border-bottom: 1px solid rgba(0, 0, 0, 0.1);
				width: 100%;
				.left-box {
					display: flex;
					align-items: center;
					width: 375px;
					min-height: 83px;
					background: #ffffff;
					padding: 21px 0px 21px 42px;
					.org-icon {
						height: 32px;
						width: 32px;
						vertical-align: middle;
					}
					.org-name {
						margin-left: 15px;
						font-weight: 400;
						color: rgba(0, 0, 0, 0.85);
						font-size: 26px;
						line-height: 30px;
					}
				}
				.right-box {
					padding: 21px 24px 21px 30px;
					display: flex;
					flex: 1;
					justify-content: space-between;
					align-items: center;
					background-color: #f8f9fa;
					.warn-box {
						display: flex;
						flex-wrap: wrap;
						align-items: center;
						gap: 12px 78px;
						.warn-item {
							display: flex;
							align-items: center;

							img {
								width: 24px;
								height: 24px;
								vertical-align: middle;
								object-fit: fill;
							}
							.warn-text {
								padding-left: 6px;
								font-weight: 400;
								font-size: 23px;
								color: #3d3d3d;
								line-height: 23px;
								letter-spacing: 1px;
							}
						}
					}
					.right-icon {
						width: 27px;
						height: 27px;
						background: url('../images/right-highlight.png') center / cover no-repeat;
					}
				}
			}
		}
	}
}
</style>
