import { pmsRequest } from '@/utils/axios' // 和沈健调试根据名称调整
//根据干部名称查询干部列表
export const getListName = (params: any) => {
	return pmsRequest.get(`/ai/get-list-by-name`, params)
}
//根据班子名称查询班子列表
export const getListOrgName = (params: any) => {
	return pmsRequest.get(`/ai/get-org-by-name`, params)
}
export const getListbyUserId = (params: any) => {
	return pmsRequest.get(`/ai/get-cadre-by-user-id`, params)
}
