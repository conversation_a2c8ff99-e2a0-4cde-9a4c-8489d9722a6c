// 路由配置
import { createRouter, createWebHistory, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import { Modal } from 'ant-design-vue'
// 干部画像
import { cadrePortrait, cadreIndex } from './cadrePortait'
// 干部全息系统
import { cadreSystem } from './cadreSystem'
// 巡察指数画像
import { inspectionIndex } from './inspectionIndex'
// 班子巡查指数
import { teamInspectionIndex } from './teamInspectionIndex'
// 沙盘推演
import { newSandTableExercise } from './newSandTableExercise'
// 巡察指数大屏
import { dataScreen } from './dataScreen'
//环境变量
import { XUNCHA_VERSION, URL_PREFIX } from '@/config/env'
console.log('🚀 ~ XUNCHA_VERSION:', XUNCHA_VERSION)

export const INDEX_PATH = XUNCHA_VERSION ? '/data-screen/login' : '/login'
// setup组件名命名不用index，部分页面需要使用vue.name保活
const routes: RouteRecordRaw[] = [
	{
		path: '/',
		redirect: INDEX_PATH,
	},
	{
		path: '/login',
		name: 'login',
		component: () => import('@/views/Login/index.vue'),
	},
	{
		path: '/comparison-results',
		name: 'comparison-results',
		meta: {
			header: false,
		},
		component: () => import('@/views/ComparisonResultsNew/index.vue'),
		// component: () => import('@/views/ComparisonResultsNew/index.vue'),
	},
	{
		path: '/comparison',
		name: 'comparison',
		meta: {
			header: false,
		},
		component: () => import('@/views/Comparison/index.vue'),
	},
	{
		path: '/cadre-form',
		name: 'cadre-form',
		component: () => import('@/views/CadreForm/index.vue'),
	},
	{
		path: '/cadre-search',
		name: 'cadre-search',
		component: () => import('@/views/CadreSearch/CadreSearch.vue'),
	},
	{
		path: '/word-preview',
		name: 'word-preview',
		component: () => import('@/views/WordPreview/index.vue'),
	},
	{
		path: '/cadre-warn',
		name: 'cadre-warn',
		meta: {
			title: '干部预警',
			hiddenMenu: true,
		},
		component: () => import('@/views/CadreWarn/index.vue'),
	},
	{
		path: '/redirect',
		name: 'Redirect',
		component: () => import('@/views/Redirect/index.vue')
	},
	{
		path: '/ai-chat',
		name: 'AiChat',
		component: () => import('@/views/AiChat/AiChat.vue'),
		meta: {
			title: 'AI助手'
		}
	},
	cadrePortrait,
	cadreSystem,
	cadreIndex,
	newSandTableExercise,
	inspectionIndex,
	teamInspectionIndex,
	dataScreen,
]
const router = createRouter({
	history: createWebHashHistory(URL_PREFIX),
	routes,
})

// router.addRoute(createCadrePortraitRouter('/preview'))
router.beforeEach((to, from, next) => {
	// console.log('🚀 ~ router.beforeEach ~ to:', to)
	// console.log('🚀 ~ router.beforeEach ~ from:', from)
	// console.log('🚀 ~ router.beforeEach ~ next:', next)
	// 渲染页面到最顶级，需要提升 #app的z-index，在ant 弹窗中跳转新页面会使用到
	// const app: any = document.querySelector('#app')
	// if (app) {
	// 	if (to.query?.top_page) {
	// 		app.style['z-index'] = 99999
	// 	} else {
	// 		app.style['z-index'] = ''

	// 		Modal.destroyAll()
	// 	}
	// }
	// 获取所有modal
	const allModal = document.querySelectorAll('.ant-modal-root')
	// 从沙盘过来的路径
	if (from.path.includes('new-sand-table-exercise')) {
		allModal.forEach((item: any) => {
			item.style.display = 'none'
		})
	}
	next()
})
export default router
