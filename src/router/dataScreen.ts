// 巡察指数大屏
export const dataScreen = {
	path: '/data-screen',
	component: () => import('@/views/DataScreen/index.vue'),
	name: 'data-screen',
	meta: {
		routerViewKey: `data-screen`,
	},
	children: [
		{
			path: '/data-screen',
			redirect: '/data-screen/login',
		},
		{
			path: '/data-screen/login',
			name: 'data-screen-login',
			component: () => import('@/views/DataScreen/Login/index.vue'),
		},
		{
			path: '/data-screen/home',
			name: 'data-screen-home',
			component: () => import('@/views/DataScreen/Home/index.vue'),
		},
		{
			path: '/data-screen/org-detail',
			name: 'data-screen-detail',
			component: () => import('@/views/DataScreen/OrgDetail/index.vue'),
		},
    {
      path: '/data-screen/four-focuses',
      name: 'data-screen-four-focuses',
      component: () => import('@/views/DataScreen/FourFocuses/index.vue'),
    },
		{
			path: '/data-screen/party-inspection-detail',
			name: 'data-screen-party-inspection-detail',
			component: () => import('@/views/DataScreen/PartyInspectionDetail/index.vue'),
			children: [
				{
					path: '/data-screen/party-inspection-detail',
					redirect: '/data-screen/party-inspection-detail/four-focus-home',
				},
				{
					path: '/data-screen/party-inspection-detail/four-focus-home',
					name: 'data-screen-party-inspection-detail/four-focus-home',
					component: () => import('@/views/DataScreen/PartyInspectionDetail/component/FourFocusHome.vue'),
				},
        {
          path: '/data-screen/party-inspection-detail/talk-level-home',
          name: 'data-screen-party-inspection-detail/talk-level-home',
          component: () => import('@/views/DataScreen/PartyInspectionDetail/component/TalkLevelHome.vue'),
        },
				{
					path: '/data-screen/party-inspection-detail/org-list',
					name: 'data-screen-party-inspection-detail/org-list',
					component: () => import('@/views/DataScreen/PartyInspectionDetail/component/OrgList.vue'),
				},
			],
		},
		{
			path: '/data-screen/party-inspection-detail/CommunicateLevel.vue',
			name: 'data-screen-party-inspection-detail/CommunicateLevel.vue',
			component: () => import('@/views/DataScreen/PartyInspectionDetail/component/CommunicateLevel.vue'),
		},
	],
}
