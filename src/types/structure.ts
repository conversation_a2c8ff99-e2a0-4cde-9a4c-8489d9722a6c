// 班子说明
import structure0 from '@/assets/images/structure/structure-0.png'
import structure1 from '@/assets/images/structure/structure-1.png'
import structure2 from '@/assets/images/structure/structure-2.png'
import structure3 from '@/assets/images/structure/structure-3.png'

// 定义数据
export const townStructure = [
	{
		label: '0项不达标',
		icon: structure0,
	},
	{
		label: '1~2项不达标',
		icon: structure1,
	},
	{
		label: '3~4项不达标',
		icon: structure2,
	},
	{
		label: '5项以上不达标',
		icon: structure3,
	},
]

export const structure = [
	{
		label: '0项不达标',
		icon: structure0,
	},
	{
		label: '1~2项不达标',
		icon: structure1,
	},
	{
		label: '3~4项不达标',
		icon: structure2,
	},
	{
		label: '5项以上不达标',
		icon: structure3,
	},
]

export const strcutTown = [
	{
		label: '年龄',
		children: ['（1）35岁以下至少有1名', '（2）35~45岁至少有3名', '（3）50岁以上不超过30%'],
	},
	{
		label: '性别',
		value: '（4）配备1~3名女干部',
	},
	{
		label: '学历',
		value: '（5）（初始学历）大学本科及以上不低于30%',
	},
	{
		label: '专业',
		children: ['（6）同时具备农业产业、规划建设、法学三类专业', '（7）党政主要领导专业要差异化'],
	},
	{
		label: '经历',
		children: ['（8）班子成员具有2年乡镇领导工作经历或者3年乡镇工作经历的达到', '（9）同一职务任职超过5年，同一单位工作超过10年'],
	},
	{
		label: '干部指数',
		value: '（10）班子成员干部指数在序列内排名后30%的人数不能超过一半',
	},
	{
		label: '干部指数',
		value: '（11）主要社会关系成员在同一单位任职的',
	},
]

export const strcut = [
	{
		label: '年龄',
		value: '（1）3个班子成员及以上的班子要配备1名40岁以下的干部',
	},
	{
		label: '学历',
		value: '（2）3个班子成员及以上的班子要配备1名大学本科（初始学历）及以上学历的干部',
	},
	{
		label: '专业',
		value: '（3）3个班子成员及以上的班子要配备1名专业对口的干部',
	},
	{
		label: '经历',
		value: '（4）同一职务任职超过5年，同一单位工作超过10年',
	},
	{
		label: '干部指数',
		value: '（5）3个班子成员及以上的班子，干部指数在序列内排名后30%的人数不能超过一半',
	},
	{
		label: '任职回避',
		value: '（6）主要社会关系成员在同一单位任职的',
	},
]
