// dev环境
export const ENV_DEV = import.meta.env.MODE === 'development'
// production 环境
export const ENV_PRO = import.meta.env.MODE === 'production'
// PAD 环境
export const ENV_PAD = import.meta.env.MODE === 'pad'
// 巡察大屏
export const XUNCHA_PRO = import.meta.env.MODE === 'xuncha-prod'
// 离线版本
export const ENV_OFFLINE = import.meta.env.MODE === 'off_line'
// CND
export const CDN_URL = XUNCHA_PRO ? `${window.location.origin}/cdn/headers` : import.meta.env.VITE_CDN_URL
// 路由前缀
export const URL_PREFIX = import.meta.env.VITE_URL_PREFIX

export enum VERSION {
	FULL = 'full',
	LIMIT = 'limit',
	XUNCHA = 'xuncha',
}

// app版本
export const APP_VERSION = process.env.APP_VERSION
console.log('🚀 ~ APP_VERSION:', APP_VERSION)
// 完整版
export const FULL_VERSION = APP_VERSION === VERSION.FULL
// 限制版
export const LIMIT_VERSION = APP_VERSION === VERSION.LIMIT
// 巡察指数版本
export const XUNCHA_VERSION = APP_VERSION === VERSION.XUNCHA

// 用户登录信息
export const USER_LOGIN = {
	username: import.meta.env.VITE_USER_NAME,
	password: import.meta.env.VITE_PASSWORD,
}
