// import './mock/index'
import '@/assets/less/iconfont.less'
import 'echarts'
import { createPinia } from 'pinia'
import 'uno.css'
import { createApp } from 'vue'
import ECharts from 'vue-echarts'
import VueLazyLoad from 'vue3-lazyload'
import App from './App.vue'
import directives from './directives'
import router from './router'
import './style.less'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(directives)
app.use(VueLazyLoad)

app.component('v-chart', ECharts)
// 保存全局引用
window.globalJsKit = window.js_kit
app.mount('#app')
