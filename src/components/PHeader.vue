<template>
	<div class="header">
		<div class="back flex-box">
			<div class="common" @click="onBack">
				<span class="icon"></span>
				<span class="text">返回</span>
			</div>
		</div>
		<div class="title" @click="onTmpClick">{{ title }}</div>
		<div class="flex-box flex-justify-right">
			<a-dropdown :trigger="['click']">
				<div class="user-box">
					<span class="user-icon"></span>
					<span class="user-name">{{ getName() }}</span>
					<span class="drop-icon"></span>
				</div>
				<template #overlay>
					<a-menu>
						<a-menu-item key="0">
							<span class="reset-password" @click="visible = true">
								<span class="reset-password-icon"></span>
								<span class="reset-text"> 修改密码 </span>
							</span>
						</a-menu-item>
					</a-menu>
				</template>
			</a-dropdown>
			<a-popconfirm title="退出登录?" ok-text="是" cancel-text="否" @confirm="confirm">
				<div class="login-out common">
					<span class="icon"></span>
					<span class="text">退出</span>
				</div>
			</a-popconfirm>
		</div>
	</div>
	<ResetPasswordModal :visible="visible" :onClose="onClose" />
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import ResetPasswordModal from './ResetPasswordModal.vue'
import { getUserInfoItem } from '@/utils/utils'

const props = defineProps({
	title: String,
	onBack: Function,
})
const router = useRouter()

const visible = ref(false)

const confirm = () => {
	sessionStorage.setItem('userInfo', '')

	message.info('退出成功')

	router.replace('/login')
}

const onTmpClick = () => {
	// 全屏
	const element: any = document.documentElement
	if (element.requestFullscreen) {
		element.requestFullscreen()
	} else if (element.webkitRequestFullScreen) {
		element.webkitRequestFullScreen()
	} else if (element.mozRequestFullScreen) {
		element.mozRequestFullScreen()
	} else if (element.msRequestFullscreen) {
		element.msRequestFullscreen()
	}
}

const getName = () => {
	const user_name = decodeURIComponent(getUserInfoItem('_un') || '')

	return user_name
}

const onBack = () => {
	props.onBack ? props.onBack() : router.push('/login')
}

const resetPassword = () => {
	visible.value = true
}

const onClose = () => {
	visible.value = false
}
</script>

<style lang="less" scoped>
.header {
	display: flex;
	align-items: center;
	padding: 0 32px;
	width: 100%;
	height: 68px;
	background: url(@/assets/images/header.png) center / cover no-repeat;

	.title {
		width: 200px;
		text-align: center;
		font-size: 27px;
		line-height: 27px;
		font-weight: 600;
		color: #ffffff;
	}
	.common {
		display: flex;
		align-items: center;
		cursor: pointer;
		.icon {
			margin-right: 10px;
			display: inline-block;
			width: 24px;
			height: 24px;
		}
		.text {
			font-size: 24px;
			font-family: Source Han Sans CN-Regular, Source Han Sans CN;
			font-weight: 400;
			color: #ffffff;
		}
	}
	.back {
		.icon {
			background: url(@/assets/images/back-header.png) center / cover no-repeat;
		}
	}
	.flex-box {
		display: flex;
		flex: 1;
	}
	.flex-justify-right {
		justify-content: flex-end;
	}
	.user-box {
		margin-right: 41px;
		display: flex;
		align-items: center;
		.user-icon {
			margin-right: 9px;
			display: inline-block;
			width: 36px;
			height: 36px;
			background: url('@/assets/images/user.png') center / cover no-repeat;
		}
		.user-name {
			margin-right: 8px;
			font-weight: 400;
			font-size: 24px;
			color: #ffffff;
			line-height: 24px;
			text-align: left;
		}
		.drop-icon {
			display: inline-block;
			width: 24px;
			height: 24px;
			background: url('@/assets/images/drop.png') center / cover no-repeat;
		}
	}
	.login-out {
		justify-content: flex-end;
		.icon {
			width: 24px;
			height: 24px;
			background: url(@/assets/images/login-out.png) center / contain no-repeat;
		}
	}
}

.reset-password {
	padding: 5px 10px;
	.reset-password-icon {
		vertical-align: middle;
		display: inline-block;
		width: 24px;
		height: 24px;
		background: url(@/assets/images/reset-password.png) center / contain no-repeat;
	}
	.reset-text {
		font-weight: 400;
		font-size: 24px;
		color: #222222;
		line-height: 24px;
		vertical-align: sub;
	}
}
</style>
