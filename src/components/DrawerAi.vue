<template>
    <div class="drawer-ai">
        <div v-if="msgcontent.length > 0">
            <div class="content">
                <div class="title">{{ title }}</div>
                <div class="contentBox">
                    <template v-for="(item, index) in msgcontent" :key="index">
                        <div class="sub_title">
                            <span>{{ numberToChinese(index + 1) }}、</span> {{ item.subtitle }}
                        </div>
                        <div class="sub_content">
                            {{ item.content }}
                        </div>
                    </template>
                </div>
            </div>
        </div>
        <div v-else class="gifimg">
            <img :src="gifImg" alt="">
            <div class="text">报告正在生成中~</div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import axios from 'axios'
import { getCadreByUserId, teamAnalysisReport } from '@/apis/statistics'
import gifImg from '@/assets/images/loadingGif.gif'
import { ref, onUnmounted } from 'vue'
import { marked } from 'marked'
// API配置
const API_URL = import.meta.env.VITE_API_URL
const API_KEY = import.meta.env.VITE_REPORT_API_KEY
const props = defineProps<{
    parameterID: any,
    type: any,
}>()
// 定义数字转中文数字的函数
const numberToChinese = (num: number) => {
    const chineseNumbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
    if (num >= 1 && num <= 10) {
        return chineseNumbers[num - 1];
    }
    // 处理大于 10 的数字，简单返回原数字字符串，可根据需求扩展
    return num.toString();
}
const msgcontent = ref<any>([])
const title = ref<any>('')
const getMsg = async (id: any) => {
    try {
        let resdata;
        let arr = <any>[]
        if (props.type == 'user') {
            resdata = await getCadreByUserId({ user_id: id })
            if (resdata.code == 0) {
                title.value = resdata.data.title
                arr = [
                    {
                        subtitle: '政治素质',
                        content: resdata.data.politics
                    },
                    {
                        subtitle: '工作业绩',
                        content: resdata.data.performance
                    },
                    {
                        subtitle: '干部特点',
                        content: resdata.data.characterist
                    },
                    {
                        subtitle: '主要不足',
                        content: resdata.data.insufficient
                    },
                    {
                        subtitle: '培养建议',
                        content: resdata.data.ai_suggest
                    },
                ]
            }
        } else {
            resdata = await teamAnalysisReport({ org_id: id })
            if (resdata.code == 0) {
                title.value = resdata.data.title
                arr = [
                    {
                        subtitle: '班子生态',
                        content: resdata.data.team_ecology
                    },
                    {
                        subtitle: '班子结构',
                        content: resdata.data.team_structure
                    },
                    {
                        subtitle: '工作成效',
                        content: resdata.data.work_achievements
                    },
                    {
                        subtitle: '短板不足',
                        content: resdata.data.shortcomings
                    },
                    {
                        subtitle: '培养建议',
                        content: resdata.data.ai_suggest
                    },
                ]
            }
        }
        msgcontent.value = arr
        // if (resdata.code == 0) {
        //     // 发起请求
        //     const response = await axios.post(
        //         `${API_URL}/v1/completion-messages`,
        //         {
        //             inputs: { query: JSON.stringify(resdata.data) },
        //             response_mode: 'blocking',
        //             user: `user-${Date.now()}`,
        //         },
        //         {
        //             headers: {
        //                 Authorization: `Bearer ${API_KEY}`,
        //                 'Content-Type': 'application/json',
        //             },
        //         }
        //     )
        //     let answercontent = response.data.answer || '';
        //     answercontent = answercontent.replace(/<think>[\s\S]*?<\/think>\n?/, '');;
        //     arr.push({
        //         subtitle: '培养建议',
        //         content: answercontent
        //     })
        //     if (response.data.answer) {
        //         msgcontent.value = arr
        //     }
        // }

    } catch (error) {
        console.error('请求失败:', error)
    } finally {

    }
}

getMsg(props.parameterID)
onUnmounted(() => {
    msgcontent.value = []
})
</script>
<style lang="less">
.drawer-ai {
    .content {
        margin-top: 36px;

        .title {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 26px;
            font-weight: bold;
            font-size: 32px;
            color: #000000;
            margin-bottom: 30px;
        }

        .contentBox {
            height: calc(100vh - 170px);
            overflow-y: auto;
        }

        .sub_title {
            font-weight: 500;
            font-size: 24px;
            color: #000000;
            margin-bottom: 12px;
        }

        .sub_content {
            font-weight: 400;
            font-size: 24px;
            color: #333333;
            margin-bottom: 24px;
            line-height: 1.5;
        }
    }

    .gifimg {
        margin: 55% auto;
        height: 157.6px;
        width: 272.8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        img {
            width: 100%;
            height: 100%;
        }

        .text {
            font-size: 30px;
            color: #000000;
            margin-top: 30px;
        }
    }
}
</style>
