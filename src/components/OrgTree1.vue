<template>
	<div :class="`tree-list-ignore`">
		<a-tree
			blockNode
			v-model:expandedKeys="expandedKeys"
			v-model:selectedKeys="selectedKeys"
			v-model:checkedKeys="checkedKeys"
			:tree-data="treeData"
			:field-names="fieldNames"
			@select="onTreeSelect"
		>
			<template #title="{ short_name }">
				<span>{{ short_name }}</span>
			</template>
		</a-tree>
	</div>
</template>

<script lang="ts" setup>
import { ref, unref } from 'vue'
import { getOrgList } from '@/apis/cadreSystem'
import { message } from 'ant-design-vue'
const emit = defineEmits(['select'])

const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const checkedKeys = ref<string[]>([])
const treeData = ref<any>([])

const fieldNames = {
	children: 'children',
	title: 'short_name',
	key: 'org_id',
}

const onTreeSelect = (selectedKeys: any, { node }) => {
	const expKeys = unref(expandedKeys)

	if (!expKeys.includes(node.org_id)) {
		expandedKeys.value.push(node.org_id)
	} else {
		expandedKeys.value = expKeys.filter((item: any) => {
			return item != node.org_id
		})
	}

	emit('select', selectedKeys[0], node)
}

const initTreeList = async () => {
	const res = await getOrgList()
	if (res.code === 0) {
		treeData.value = res.data
	} else {
		message.error(res.message)
	}
}

initTreeList()
</script>

<style scoped lang="less">
.tree-list,
.tree-list-ignore {
	::v-deep(.ant-tree-title) {
		// font-size: 22px;
		font-size: 18px;
		line-height: 18px;
	}
	:deep(.ant-tree-switcher-icon) {
		font-size: 15px;
		line-height: 15px;
	}
}
</style>
