<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
const CDN_URL = import.meta.env.VITE_CDN_URL
import CodeAvatar from './CodeAvatar.vue'
const props = defineProps({
	type: {
		type: Number,
	},
	data: {
		type: Array,
	},
	close: {
		type: Function,
	},
})

const loading = ref<any>(false)
const router = useRouter()

const columns = [
	{
		title: '',
		dataIndex: 'name',
		key: 'name',
		align: 'center',
		// width: '20%',
	},
	{
		title: '年龄',
		dataIndex: 'age',
		key: 'age',
		align: 'center',
		width: '15%',
	},
	{
		title: '性别',
		dataIndex: 'gender',
		key: 'gender',
		align: 'center',
		// width: '10%',
	},
	{
		title: '学历',
		dataIndex: 'educational',
		key: 'educational',
		align: 'center',
		// width: '15%',
	},
	{
		title: '专业',
		align: 'center',
		dataIndex: 'major',
		key: 'major',
		// width: '10%',
	},
	{
		title: '经历',
		align: 'center',
		dataIndex: 'experience',
		key: 'experience',
		width: '20%',
	},
	{
		title: '干部指数',
		align: 'center',
		dataIndex: 'cadre_index',
		key: 'cadre_index',
		// width: '15%',
	},
	{
		title: '任职回避',
		align: 'center',
		dataIndex: 'withdraw',
		key: 'withdraw',
		// width: '15%',
	},
]

const customRow = (record: any) => {
	return {
		onClick: () => {
			router.push(`/cadre-portrait/home?user_id=${record.user_id}`)
			props.close?.()
		},
	}
}
</script>

<template>
	<div class="structure-table">
		<div class="modal-title">班子结构分析表</div>
		<a-table bordered :columns="columns" :data-source="props.data" :loading="loading" :pagination="false" :scroll="{ y: 620 }" :customRow="customRow">
			<template #bodyCell="{ column, record }">
				<template v-if="column.key == 'name'">
					<div class="name-wrap">
						<div class="wrap-img" v-if="record.head_url">
							<CodeAvatar :head_url="record.head_url" />
						</div>
						<div class="name-text">
							<div class="name">{{ record.name }}</div>
							<div>{{ record.current_job }}</div>
						</div>
					</div>
				</template>
				<template v-if="column.key == 'age'">
					<template v-if="Array.isArray(record.age)">
						<div v-for="(item, index) in record.age" :key="index">
							{{ item }}
						</div>
					</template>
					<div v-if="typeof record.age == 'number' || typeof record.age == 'string'">
						{{ record.age == 1 ? '是' : record.age == 0 ? '否' : ` ${record.age}${'岁'}` }}
					</div>
				</template>
				<template v-if="column.key == 'gender'">
					<template v-if="Array.isArray(record.gender)">
						<div v-for="(item, index) in record.gender" :key="index">
							{{ item }}
						</div>
					</template>
					<div v-else>
						{{ record.gender === '1' ? '男' : record.gender === '2' ? '女' : record.gender }}
					</div>
				</template>
				<template v-if="column.key == 'educational'">
					<template v-if="Array.isArray(record.educational)">
						<div v-for="(item, index) in record.educational" :key="index">
							{{ item }}
						</div>
					</template>
					<div v-if="typeof record.educational == 'number' || typeof record.educational == 'string'">
						{{ record.educational == 1 ? '是' : record.educational == 0 ? '否' : `${record.educational}` }}
					</div>
				</template>
				<template v-if="column.key == 'major'">
					<template v-if="Array.isArray(record.major)">
						<div v-for="(item, index) in record.major" :key="index">
							{{ item }}
						</div>
					</template>
					<div v-if="typeof record.major == 'number' || typeof record.major == 'string'">
						{{ record.major == 1 ? '是' : record.major == 0 ? '否' : `${record.major}` }}
					</div>
				</template>
				<template v-if="column.key == 'experience'">
					<template v-if="Array.isArray(record.experience)">
						<div v-for="(item, index) in record.experience" :key="index" class="experience">
							{{ item }}
						</div>
					</template>
					<div v-if="typeof record.experience == 'number' || typeof record.experience == 'string'" class="experience">
						{{ record.experience == 1 ? '是' : record.experience == 0 ? '否' : '' }}
					</div>
					<div v-for="(item, index) in typeof record.experience == 'string' && record.experience?.split(';')" :key="index" class="experience">
						{{ item }}
					</div>
				</template>
				<template v-if="column.key == 'cadre_index'">
					<template v-if="Array.isArray(record.cadre_index)">
						<div v-for="(item, index) in record.cadre_index" :key="index">
							{{ item }}
						</div>
					</template>
					<div v-if="typeof record.cadre_index == 'number' || typeof record.cadre_index == 'string'">
						{{ record.cadre_index == 1 ? '是' : record.cadre_index == 0 ? '否' : `${record.cadre_index}` }}
					</div>
				</template>
				<template v-if="column.key == 'withdraw'">
					<template v-if="Array.isArray(record.withdraw)">
						<div v-for="(item, index) in record.withdraw" :key="index">
							{{ item }}
						</div>
					</template>
					<div v-if="typeof record.withdraw == 'number' || typeof record.withdraw == 'string'">
						{{ record.withdraw == 1 ? '是' : record.withdraw == 0 ? '否' : `${record.withdraw}` }}
					</div>
				</template>
			</template>
		</a-table>
	</div>
</template>

<style lang="scss" scoped>
.structure-table {
	.modal-title {
		margin-bottom: 10px;
		padding: 10px 0px 30px;
		font-size: 24px;
		font-family: Source Han Sans CN-Bold, Source Han Sans CN;
		font-weight: bold;
		color: rgba(0, 0, 0, 0.9);
		line-height: 22px;
		border-bottom: 1px solid #f0f0f0;
	}
	.name-wrap {
		display: flex;
		flex-direction: column;
		align-items: center;
		.wrap-img {
			width: 60px;
			height: 76px;
			img {
				width: 100%;
				height: 100%;
			}
		}
	}
	.experience {
		text-align: left;
	}
	.name-text {
		margin-top: 15px;
		.name {
			font-size: 18px;
			font-family: Source Han Sans CN-Medium, Source Han Sans CN;
			font-weight: 500;
			color: rgba(0, 0, 0, 0.9);
			line-height: 21px;
		}
	}
}
:deep(.ant-table-row) {
	& > .ant-table-cell:nth-child(1) {
		background-color: #f3f3f3;
	}
}
:deep(.ant-table-thead) > tr > th {
	background-color: #f3f3f3;
}
</style>
<style lang="less">
	.structure-table{
		.ant-table-wrapper{
			th{
				font-size: 20px
			}
			td{
				font-size: 20px
			}
			.ant-empty-description{
				font-size: 20px
			}
		}
	}

</style>
