import fs from 'fs-extra'
import path from 'path'

const sourceDir = 'dist' // 源文件夹
const destinationDir = '/Users/<USER>/Documents/gs_yw/pms-pad/app/src/main/assets' // 目标文件夹

const resourcesPath = path.join(destinationDir, 'resource') // resource 文件夹路径

// 确保目标文件夹存在，如果不存在就创建它
fs.ensureDirSync(destinationDir)

// 获取目标文件夹中的所有文件和子文件夹
const items = fs.readdirSync(destinationDir)

// 删除除 resources 文件夹外的所有内容
items.forEach((item) => {
    const itemPath = path.join(destinationDir, item)
    if (itemPath !== resourcesPath) {
        fs.removeSync(itemPath)
    }
})

// 复制源文件夹中的所有内容到目标文件夹
fs.copySync(sourceDir, destinationDir)

console.log(`文件从 '${sourceDir}' 复制到 '${destinationDir}'`)