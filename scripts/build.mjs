// 导入 execa 库的 command 函数用于执行命令行指令
import { command } from 'execa'
// 导入 inquirer 的 select 组件用于实现选择菜单
// import select from '@inquirer/select'
import inquirer from 'inquirer'
import inquirerSelectLine from 'inquirer-select-line'

inquirer.registerPrompt('selectLine', inquirerSelectLine)

/**
 * 启动程序，让用户选择执行运行还是打包操作，并根据选择执行相应的操作。
 */
async function start() {
	console.log('[start.mjs]: 启动程序，让用户选择执行运行还是打包操作，并根据选择执行相应的操作。编写Node版本：14.18.1')

	const runTypeOption = [
		{
			name: '运行🚀',
			value: 'run',
		},
		{
			name: '打包🚀',
			value: 'build',
		},
		{
			name: '发布到测试🚀',
			value: 'gsg',
		},
	]

	const versionType = [
		{
			name: '干部系统🚀',
			value: 'full',
			description: '🎈该版本开放所有功能，注入环境变量中的key为【APP_VERSION】，值为【full】',
		},
		{
			name: '上会系统🚀',
			value: 'limit',
			description: '🎈该版本仅有一个沙盘推演入口，注入环境变量中的key为【APP_VERSION】，值为【limit】',
		},
		{
			name: '巡察指数大屏系统🚀',
			value: 'xuncha',
			description: '🎈巡察指数大屏',
		},
	]

	const envType = [
		{
			name: '测试环境🚀',
			value: 'test',
		},
		{
			name: '正式环境🚀',
			value: 'prod',
		},
		{
			name: '离线环境🚀',
			value: 'off_line',
		},
		{
			name: '巡查系统在线环境🚀',
			value: 'xuncha-prod',
		},
	]

	// 根据用户选择显示版本选择菜单
	const result = await select([
		{
			type: 'list',
			message: '你要执行的操作？',
			name: 'runType',
			choices: runTypeOption.map((item) => item.name),
		},
		{
			type: 'list',
			message: '选中你要打包的版本：',
			name: 'version',
			choices: versionType.map((item) => item.name),
		},
		{
			type: 'list',
			message: '选中你要打包的环境：',
			name: 'env',
			choices: envType.map((item) => item.name),
		},
	])
	const { runType, version, env } = result

	const getKeys = (option, key) => {
		return option.find((item) => item.name === key).value
	}

	const _runType = getKeys(runTypeOption, runType)
	const _version = getKeys(versionType, version)
	const _env = getKeys(envType, env)
	// 根据用户选择执行运行或打包操作
	if (_runType === 'run') {
		await run(_version, _env) // 运行选中的版本
	} else if (_runType === 'build') {
		await build(_version, _env) // 打包选中的版本
	} else {
		// 发布测试版本
		await realseTest(_version, _env)
	}
}

const select = async (option) => {
	return await inquirer.prompt(option)
}

/**
 * 执行运行操作。
 * @param {string} version - 选择的版本，决定运行哪个版本的脚本。
 */
const run = async (version, env) => {
	try {
		// 执行运行脚本命令
		await command(`cross-env APP_VERSION=${version} npm run ${env === 'test' ? 'dev' : 'preview'}`, {
			stdio: 'inherit',
			shell: true,
		})
	} catch (err) {
		process.exit(1) // 出错时退出进程
	}
}

/**
 * 执行打包操作。
 * @param {string} version - 选择的版本，决定打包哪个版本的脚本。
 */
const build = async (version, env) => {
	const versionMap = {
		full: '完整版',
		limit: '限制版',
	}

	const commandMap = {
		test: 'build:dev',
		prod: 'build',
		off_line: 'build:off_line',
		'xuncha-prod': 'build:xuncha-prod'
	}

	const Command = `cross-env APP_VERSION=${version} RUN_MODE=PAD npm run ${commandMap[env]}`

	console.log('🚀 ~ build ~ Command:', Command)
	try {
		const _version = versionMap[version]
		// 打印开始打包的信息
		console.log(`开始打包 ${_version} ...`)
		// 执行打包命令
		await command(Command, {
			stdio: 'inherit',
			shell: true,
		})

		// 打印打包成功的信息
		console.log(`成功打包 ${_version} 。`)
	} catch (err) {
		process.exit(1) // 打包失败时退出进程
	}
}

/**
 * 执行发布测试操作。
 * @param {string} version - 选择的版本，决定运行哪个版本的脚本。
 */
const realseTest = async (version) => {
	const command_str = `cross-env APP_VERSION=${version} gsg d pad_test`

	console.log('🚀 ~ realseTest ~ command_str:', command_str)
	try {
		// 执行运行脚本命令
		await command(command_str, {
			stdio: 'inherit',
			shell: true,
		})
	} catch (err) {
		process.exit(1) // 出错时退出进程
	}
}
// 调用 start 函数开始程序
start()
